pipeline {
    agent {
        docker {
            image 'node:18-alpine'
        }
    }
    
    stages {
        stage('Prepare') {
            steps {
                sh 'chmod +x *.sh'
                sh './jenkins-setup.sh'
                sh 'node fix-vue-cli.js'
            }
        }
        
        stage('Lint') {
            steps {
                sh 'cd case-loop && ./node_modules/.bin/vue-cli-service lint'
            }
        }
        
        stage('Build') {
            steps {
                sh 'npm run package-build'
            }
        }
        
        stage('Test') {
            steps {
                sh 'npm run test'
            }
        }
        
        stage('Upload') {
            steps {
                // Make our improved scripts executable
                sh 'chmod +x improved-zip-command.sh improved-jenkins-upload.sh'
                
                // First run the improved zip command script
                sh './improved-zip-command.sh'
                
                // Verify the zip file exists and has content
                sh '''
                if [ -f "/root/jenkins/build/build.zip" ] && [ -s "/root/jenkins/build/build.zip" ]; then
                    echo "成功验证zip文件"
                    # Run the improved upload script
                    ./improved-jenkins-upload.sh
                    exit 0
                else
                    echo "验证zip文件失败"
                    exit 1
                fi
                '''
            }
        }
    }
    
    post {
        always {
            echo 'Pipeline completed'
        }
        success {
            echo 'Pipeline succeeded'
        }
        failure {
            echo 'Pipeline failed'
        }
    }
} 