/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution');

module.exports = {
  root: true,
  extends: [
    'plugin:vue/essential',
    /**
     * Some of its rules, however, might conflict with prettier. So when used alongside other sharable configs,
     * this config should be placed after all other configs except for the one from @vue/eslint-config-prettier or eslint-plugin-prettier in the extends array.
     */
    '@vue/eslint-config-typescript/recommended',
    '@vue/eslint-config-prettier',
  ],
};
