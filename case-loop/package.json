{"name": "case-loop", "version": "0.1.0", "private": true, "scripts": {"dev": "vite --mode development", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@babel/runtime": "^7.24.0", "@cs/logicForest-core": "^0.2.14", "@cs/logicForest-extension": "^0.2.14", "@cs/media-sdk": "^1.0.6", "@guolao/vue-monaco-editor": "^1.5.5", "@ss/mtd-vue3": "^1.2.41", "ant-design-vue": "^3.2.20", "axios": "^1.9.0", "core-js": "^3.8.3", "echarts": "^5.6.0", "js-cookie": "^3.0.5", "monaco-editor": "^0.52.2", "qs": "^6.14.0", "vue": "^3.2.13", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.27.0", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/plugin-transform-runtime": "^7.26.10", "@rushstack/eslint-patch": "^1.11.0", "@tsconfig/node22": "^22.0.2", "@types/js-cookie": "^3.0.6", "@types/node": "^22.15.21", "@types/qs": "^6.14.0", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^11.0.3", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "less": "^4.3.0", "sass": "^1.89.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-nightwatch": "^0.4.6", "vite-plugin-vue-devtools": "^7.7.6"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}