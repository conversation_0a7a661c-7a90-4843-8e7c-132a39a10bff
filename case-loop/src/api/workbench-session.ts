import { PlatformType } from '@/constants/platform';
import { AidaApplicationType, ErrorSetColumnType, SessionChatLLMInspectType } from '@/constants/session-chat';
import axios from '@/plugin/axios';
import webApi from '@/plugin/webApi';
import { getIsFromIframe } from '@/utils/open-across-iframe';
import { isFromLabelPlatform } from '@/utils/platform';
import { ref } from 'vue';

const isFromLabel = ref(isFromLabelPlatform());
export const getSessionList = async (
  params: Omit<WorkbenchSessionSearchForm, 'date' | 'workspaceAndApp'> & {
    startTime?: number;
    endTime?: number;
    workspaceId?: string;
    applicationId?: string;
    pageSize: number;
    pageNum: number;
    channel?: string;
    sourceChannel?: string;
    sourcePage?: string;
    score?: string;
    evaluationStatus?: string;
    transferStaff?: Array<string>;
  }
) => {
  const { code, data, message } = (await webApi.post('/api/aigc/eval/workbench/session/page', params)) as unknown as {
    code: number;
    data: {
      totalNum: number;
      cost: number;
      pageSize: number;
      pageNum: number;
      data: Array<WorkbenchSessionInfo>;
    };
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

export const getSessionBaseInfo = async (sessionId: string) => {
  const { code, data, message } = (await webApi.get('/api/aigc/eval/workbench/session/overview', {
    sessionId,
  })) as unknown as {
    code: number;
    data: WorkbenchSessionBaseInfo;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

// platformType 和 llmSessionId 透传getSessionBaseInfo响应即可
export const getSessionChatList = async (sessionId: string, platformType: PlatformType, llmSessionIdList: string[]) => {
  const { code, data, message } = (await webApi.post('/api/aigc/eval/workbench/session/detail', {
    sessionId,
    platformType,
    llmSessionIdList,
  })) as unknown as {
    code: number;
    data: Array<WorkbenchSessionChatInfo>;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  if (isFromLabel.value) {
    sendMessageToParent({
      type: 'sessionChatList',
      data: data,
    });
  }

  return data;
};

const sendMessageToParent = (message: unknown) => {
  // 检查是否不在iframe中，如果不在iframe中则直接返回
  if (!window.parent || window.parent === window) {
    return;
  }

  try {
    // 发送消息给父页面，'*' 表示允许任何域的父页面接收
    // 在生产环境中建议指定具体的目标域以增强安全性
    window.parent.postMessage(message, '*');
  } catch (error) {
    console.error('向父页面发送消息时出错:', error);
  }
};

/**
 * 获取query维度的大模型消息详情
 */
export const getSessionChatLLMInfo = async (params: {
  sessionId: string;
  messageId: string;
  platform: PlatformType;
  originMessage: string;
  /**
   * 透传getSessionBaseInfo响应即可
   */
  llmSessionIdList: string[];
  /**
   * query消息对应的aida空间id
   * 若查询ai搭平台消息（platform为AIDA），透传getSessionChatList响应体中具体的消息的workspaceId即可
   */
  workspaceId?: string;
  /**
   * query消息对应的aida应用id
   * 若查询ai搭平台消息（platform为AIDA），透传getSessionChatList响应体中具体的消息的applicationId即可
   */
  applicationId?: string;
  /**
   * 透传session/detail接口响应的messageLevelAidaAppInfo?.llmMessageId即可
   */
  llmMessageId?: string;
}) => {
  const { code, data, message } = (await webApi.post('/api/aigc/eval/workbench/query/detail', params)) as unknown as {
    code: number;
    data: WorkbenchSessionChatLLMInfo;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

let updateSessionChatLLMInspectController: AbortController | null = null;
export const updateSessionChatLLMInspect = async (params: {
  sessionId: string;
  platformType: PlatformType;
  llmSessionId: Array<string>;
  messageId: string;
  llmMessageId: string;
  isAgree: SessionChatLLMInspectType;
}) => {
  updateSessionChatLLMInspectController?.abort();
  updateSessionChatLLMInspectController = new AbortController();

  const { code, data, message } = (await webApi.post('/api/aigc/eval/workbench/query/inspect', params, {
    signal: updateSessionChatLLMInspectController?.signal,
  })) as unknown as {
    code: number;
    data: unknown;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

let sessionChatMessageCollectController: AbortController | null = null;
export const sessionChatMessageCollect = async (params: {
  sessionId: string;
  platformType: PlatformType;
  llmSessionId: Array<string>;
  messageId: string;
  llmMessageId: string;
}) => {
  sessionChatMessageCollectController?.abort();
  sessionChatMessageCollectController = new AbortController();

  const { code, data, message } = (await webApi.post('/api/aigc/eval/workbench/query/collect', params, {
    signal: sessionChatMessageCollectController?.signal,
  })) as unknown as {
    code: number;
    data: unknown;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

/**
 * 查询人工质检结果
 */
export const getManualInspectResult = (
  params: WorkbenchSessionChatManualInspectSearchParams
): Promise<{
  code: number;
  message: string;
  data: WorkbenchSessionChatManualInspectResponseData;
}> => {
  return axios.get('/api/aigc/eval/workbench/manual/inspect', { params });
};

/**
 * 保存人工质检结果
 */
export const saveManualInspectResult = (
  params: ISaveManualInspectInfoParams
): Promise<{
  code: number;
  message: string;
  data: null;
}> => {
  return axios.post('/api/aigc/eval/workbench/manual/inspect', params);
};

/**
 * 获取机器指标id与人工指标id的一一对应关系
 * 注意：
 * 1、人工质检指标一定有对应的机器质检指标，但机器质检指标不一定有对应的人工质检指标
 * 2、若某个人工质检指标不存在对应的机器质检指标，则getMetricIdMapList接口的响应体中不存在映射对象
 * 3、即getMetricIdMapList返回的是所有存在一一映射关系的映射对象
 */
export const getMetricIdMapList = (): Promise<{
  code: number;
  message: string;
  data: WorkbenchSessionChatMetricIdMapData;
}> => {
  return axios.get('/api/aigc/eval/workbench/metrics/mapping');
};

/**
 * 查询空间 & 应用列表
 */
export function getWorkspaceAndAppList(): Promise<{
  code: number;
  message: string;
  data: TWorkspaceResponseData;
}> {
  return axios.get('/api/aigc/eval/workbench/workspaces/apps');
}

/**
 * 运营分析-导出人工已质检数据
 */
export const exportSessionData = (
  params: WorkbenchSessionSearchForm & {
    channel?: string,
    sourceChannel?: string,
    sourcePage?: string,
    score?: string,
    evaluationStatus?: string
  }
): Promise<{
  code: number;
  message: string;
  data: null;
}> => {
  return axios.post('/api/aigc/eval/workbench/session/export', params);
};

/**
 * 运营分析-查询用户角色
 */
export const getSessionUserRoleResult = (): Promise<{
  code: number;
  message: string;
  data: { code: number; description: string; name: string };
}> => {
  return axios.get('/api/aigc/eval/workbench/role');
};

/**
 * 获取当前用户信息
 */
export const getCurrentUserInfo = (): Promise<{
  traceId: string;
  code: number;
  message: string | null;
  data: {
    mis: string;
    name: string;
    id: number;
  };
}> => {
  return axios.get('/api/user');
};

/**
 * 加入评测集场景下，获取可选的大模型节点以及上次的选择状态
 */
export const getAppLlmNodelListApi = async (params: {
  versionId: string;
  applicationId: string;
  workspace: string;
}) => {
  const { code, data, message } = (await webApi.post('/api/aigc/eval/aida/bigNode/list', params)) as unknown as {
    code: number;
    data: Array<LlmNodeItem>;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

/**
 * 获取规则型应用的日志消息的评测集映射关系
 * 注意：
 * 1、若当前消息未加入评测集，则datasetId为null
 * 2、会话分析工具场景下，只存在规则型应用的会话
 */
export const getAidaRuleAppLogErrorSetMap = async (params: {
  nodeId: string;
  sessionId: string;
  messageId: string;
  originMessage: string;
  /**
   * 透传getSessionBaseInfo响应即可
   */
  llmSessionIdList: string[];
  /**
   * 透传getSessionChatLLMInfo响应即可
   */
  workspaceId?: string;
  /**
   * 透传getSessionChatLLMInfo响应即可
   */
  applicationName?: string;
  /**
   * 透传getSessionChatLLMInfo响应即可
   */
  applicationId?: string;
  /**
   * 透传getSessionChatLLMInfo接口响应的id即可
   */
  llmMessageId?: string;
}) => {
  const { code, data, message } = (await webApi.post('/api/aigc/eval/aida/robot/getInputAndOutput', {
    ...params,
    applicationType: PlatformType.AIDA,
  })) as unknown as {
    code: number;
    data: ErrorSetMapInfo;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

/**
 * 加入评测集
 */
export const addErrorSetApi = async (params: {
  aidaModelConfig: {
    nodeId?: string;
    workspaceId: string;
    applicationId: string;
    applicationName: string;
    modelConfigVersionId: string;
  };
  applicationType: AidaApplicationType;
  datasetName: string;
  /**
   * 注意：无需关注类型，透传map接口的返回值即可
   */
  datasetId: number | null;
  mapList: Array<ErrorSetMapInfo>;
  customList: Array<{
    columnName: string;
    columnValue: string;
    columnType: ErrorSetColumnType.CUSTOM_FIELD;
  }>;
  /**
   * 透传getSessionChatLLMInfo接口响应的id即可
   */
  llmMessageId?: string;
}) => {
  const { code, data, message } = (await webApi.post('/api/aigc/eval/dataset/aida/log/create', params)) as unknown as {
    code: number;
    data: ErrorSetMapInfo;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

/**
 * 变更大模型节点选择
 */
export const changeLlmNodeApi = async (params: {
  platformWorkspace?: string;
  appId?: string;
  appVersionId?: string;
  nodeId: string | null;
}) => {
  const { code, data, message } = (await webApi.put('/api/aigc/eval/workbench/node/selected', params)) as unknown as {
    code: number;
    data: unknown;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

/**
 * 变更信号排序
 */
export const changeSignalOrderApi = async (params: {
  platformWorkspace?: string;
  appId?: string;
  appVersionId?: string;
  nodeId: string;
  config: string;
}) => {
  const { code, data, message } = (await webApi.put('/api/aigc/eval/workbench/signal/reorder', params)) as unknown as {
    code: number;
    data: unknown;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

/**
 * 变更收藏
 */
export const changeSignalFavoriteApi = async (params: {
  platformWorkspace?: string;
  appId?: string;
  appVersionId?: string;
  isFavorite: boolean;
  signalKey: string;
}) => {
  const { code, data, message } = (await webApi.put('/api/aigc/eval/workbench/signal/favorite', params)) as unknown as {
    code: number;
    data: unknown;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

/**
 * 变更模型选择接口
 */
export const changeLlmSelectedApi = async (params: {
  platformWorkspace?: string;
  appId?: string;
  appVersionId?: string;
  selectedAppId: string | null;
}) => {
  const { code, data, message } = (await webApi.put('/api/aigc/eval/workbench/llm/selected', params)) as unknown as {
    code: number;
    data: unknown;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

/**
 * 调试模型节点
 */
export const debugLlmNodeApi = async (params: {
  appId: string;
  aidaAppVersionId: string;
  input: Record<string, unknown>;
}) => {
  const { code, data, message } = (await webApi.post('/api/aigc/eval/workbench/llm/debug', params)) as unknown as {
    code: number;
    data: { code: string; content: string; message: string; answer: string };
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

/**
 * 调试模型查询
 */
export const getDebugLlmQueryApi = async (params: {
  sessionId: string;
  messageId: string;
  platform: PlatformType;
  originMessage: string;
  llmSessionIdList: string[];
  workspaceId?: string;
  applicationId?: string;
  llmMessageId?: string;
}) => {
  const { code, data, message } = (await webApi.post('/api/aigc/eval/workbench/query/llm/tree', params)) as unknown as {
    code: number;
    data: {
      aidaLlmTree: Array<aidaLlmTreeNode>;
      selectedAppId: string;
    };
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

/**
 * 获取音频信息
 */
export const getAudioInfoApi = async (params: { sessionId: string }) => {
  const { code, data, message } = (await webApi.get(
    '/api/aigc/eval/workbench/session/ivr/info',
    params
  )) as unknown as {
    code: number;
    data: string;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};
/**
 * 获取音频信息
 */
export const getCallOutInfo = async (contactId: string) => {
  const { code, data, message } = (await webApi.get(
    '/api/aigc/eval/workbench/calls/' + contactId + '/info'
  )) as unknown as {
    code: number;
    data: WorkbenchSessionCallOutInfoData;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

/**
 * 获取应用的执行路径
 */
export const getAidaExecPathApi = async (params: {
  sessionId: string;
  messageId: string;
  platform: PlatformType;
  originMessage: string;
  /**
   * 透传getSessionBaseInfo响应即可
   */
  llmSessionIdList?: string[];
  /**
   * query消息对应的aida空间id
   * 若查询ai搭平台消息（platform为AIDA），透传getSessionChatList响应体中具体的消息的workspaceId即可
   */
  workspaceId?: string;
  /**
   * query消息对应的aida应用id
   * 若查询ai搭平台消息（platform为AIDA），透传getSessionChatList响应体中具体的消息的applicationId即可
   */
  applicationId?: string;
  /**
   * 透传session/detail接口响应的messageLevelAidaAppInfo?.llmMessageId即可
   */
  llmMessageId?: string;
  /**
   * 过滤应用id
   */
  filterAppId: string;
  /**
   * 过滤应用版本id
   */
  applicationVersionId: string;
}) => {
  const { code, data, message } = (await webApi.post(
    '/api/aigc/eval/workbench/query/detail/path',
    params
  )) as unknown as {
    code: number;
    data: AidaExecPathDetail;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

/**
 * 会话分析详情页-记录用户拖拽后自定义的信号模块和模型信息模块的宽度
 */
export const updateSessionSignalAndModelWidthApi = async (params: {
  totalWidth: number;
  referenceWindowWidth: number;
}) => {
  const { code, data, message } = (await webApi.put(
    '/api/aigc/eval/workbench/window-width/save',
    params
  )) as unknown as {
    code: number;
    data: null;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '保存失败');
  }

  return data;
};

/**
 * 获取会话筛选条件配置
 */
export const getSessionFilterConfig = async (params: { channel: string }): Promise<{
  code: number;
  message: string;
  data: {
    filterModules: Array<{
      moduleId: string;
      moduleName: string;
      filters: Array<{
        id: string;
        name: string;
        type: string;
        placeholder: string;
        options?: Array<{ value: string; label: string }>;
      }>;
    }>;
  };
}> => {
  return axios.get('/api/aigc/eval/workbench/session/condition/config', { params });
};

/**
 * 获取会话详情的额外信息
 */
export const getSessionBasicInfo = async (params: { 
  sessionId: string; 
  analysisType: string 
}) => {
  const { code, data, message } = (await webApi.get('/api/aigc/eval/workbench/session/basic', params)) as unknown as {
    code: number;
    data: {
      visitIds?: string;
      stars?: string;
      sessionSolved?: string;
      transferStaff?: string;
      sceneName?: string;
      standardQuestion?: string;
      windowWidthSetUp?: {
        totalWidth: number;
        referenceWindowWidth: number;
      };
      serviceProcessInfo?: any;
    };
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

/**
 * 获取场景相关的筛选配置
 */
export const getSceneRelationConfig = async (): Promise<{
  code: number;
  message: string;
  data: {
    sceneInfos: Array<{
      sceneId: number;
      sceneName: string;
      buInfos: Array<{
        buId: number;
        buName: string;
        buCode: string;
        subBuInfos: Array<{
          subBuId: number;
          subBuName: string;
          subBuCode: string;
          taskKeyInfos: Array<{
            taskKey: string;
          }>;
        }>;
      }>;
      workspaceAppList: Array<{
        workspaceId: string;
        workspaceName: string;
        applicationList?: Array<{
          applicationId: string;
          applicationName: string;
        }>;
      }>;
    }>;
  };
}> => {
  return axios.get('/api/aigc/eval/workbench/session/condition/scene');
};

// 获取触发task的节点信息 (包括版本和节点)
export const getTaskNodes = async (params: {
  taskKey: string;
  taskVersion?: string;
}) => {
  const { code, data, message } = (await webApi.get('/api/aigc/eval/workbench/session/condition/task/nodes', params)) as unknown as {
    code: number;
    data: {
      taskKey: string;
      taskName?: string;
      taskVersion?: string;
      taskVersionNodes?: Array<{ nodeId: string; nodeName: string }>;
    };
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

// 获取触发task的版本信息
export const getTaskVersions = async (params: {
  taskKey: string;
  taskVersion?: string;
}) => {
  const { code, data, message } = (await webApi.get(
    '/api/aigc/eval/workbench/session/condition/task/versions',
    params
  )) as unknown as {
    code: number;
    data: {
      taskKey: string;
      taskName?: string;
      taskVersions?: Array<{ versionId: string; versionName: string; staffMis: string }>;
    };
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

// 添加评价
export async function addReview(params: {
  sessionId: string;
  messageId?: string;
  reviewContent: string;
}): Promise<number> {
  try {
    const response = await axios({
      url: '/api/aigc/eval/workbench/review/add',
      method: 'post',
      data: params
    });
    
    // 返回评价ID
    if (response && response.data) {
      return response.data;
    }
    return 0;
  } catch (error) {
    console.error('添加评价API调用失败:', error);
    throw error;
  }
}

// 获取评价列表
export async function getReviewList(sessionId: string): Promise<Array<{
  id: number;
  sessionId: string;
  messageId: string;
  reviewContent: string;
  reviewMis: string;
  reviewName: string;
  reviewDate: string;
  likeNumber: number;
}>> {
  try {
    const response = await axios({
      url: '/api/aigc/eval/workbench/review/list',
      method: 'get',
      params: { sessionId }
    });
    
    // 确保返回数组，处理不同的响应格式
    if (response && response.data) {
      return Array.isArray(response.data) ? response.data : [];
    }
    return Array.isArray(response) ? response : [];
  } catch (error) {
    console.error('获取评价列表API调用失败:', error);
    return [];
  }
}
