<template>
  <div class="base-configuration">
    <a-tabs v-model:activeKey="activeTab">
      <a-tab-pane key="dataTracking" tab="数据埋点">
        <data-tracking />
      </a-tab-pane>
      <a-tab-pane key="sceneConfig" tab="场景配置">
        <scene-configuration />
      </a-tab-pane>
      <a-tab-pane key="basicTags" tab="基础标签">
        <basic-tags />
      </a-tab-pane>
      <a-tab-pane key="compositeTags" tab="复合标签">
        <composite-tags />
      </a-tab-pane>
      <a-tab-pane key="tagGroups" tab="标签组">
        <tag-groups />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import { defineComponent, ref } from 'vue';
import SceneConfiguration from './SceneConfiguration.vue';
import BasicTags from './BasicTags.vue';
import CompositeTags from './CompositeTags.vue';
import TagGroups from './TagGroups.vue';
import DataTracking from './DataTracking.vue';

export default defineComponent({
  name: 'BaseConfiguration',
  components: {
    SceneConfiguration,
    BasicTags,
    CompositeTags,
    TagGroups,
    DataTracking
  },
  setup() {
    const activeTab = ref('dataTracking');

    return {
      activeTab
    };
  }
});
</script>

<style scoped>
.base-configuration {
  padding: 16px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
}
</style> 