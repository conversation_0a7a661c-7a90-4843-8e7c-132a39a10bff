<template>
  <div class="performance-dashboard">
    <h2 class="page-title">效果大盘</h2>
    
    <!-- Tab 切换 -->
    <a-tabs v-model:activeKey="activeTab" class="dashboard-tabs">
      <a-tab-pane key="business-overview" tab="业务总览">
        <!-- 业务线总览 -->
        <a-card class="overview-card" title="业务线总览">
          <!-- 添加时间筛选器 -->
          <div class="overview-filter">
            <a-radio-group v-model:value="overviewTimeRange" @change="handleOverviewTimeChange">
              <a-radio-button value="7">近7天</a-radio-button>
              <a-radio-button value="14">近14天</a-radio-button>
              <a-radio-button value="30">近30天</a-radio-button>
            </a-radio-group>
          </div>
          <a-row :gutter="[16, 16]">
            <!-- 外客在线 -->
            <a-col :span="8">
              <a-card class="business-line-card">
                <template #title>
                  <div class="business-line-title">
                    <span>外客在线</span>
                    <a-tag color="blue">在线</a-tag>
                  </div>
                </template>
                <div class="business-line-content">
                  <div class="metrics">
                    <div class="metric-item">
                      <span class="label">转人工率</span>
                      <span class="value">23.5%</span>
                      <span class="trend down">↓ 2.1%</span>
                    </div>
                    <div class="metric-item">
                      <span class="label">解决率</span>
                      <span class="value">78.2%</span>
                      <span class="trend up">↑ 3.5%</span>
                    </div>
                  </div>
                  <div class="charts-container">
                    <div class="trend-chart">
                      <div ref="waimaiOnlineTrendChart" class="chart-container"></div>
                    </div>
                    <div class="issue-distribution">
                      <div ref="waimaiOnlinePieChart" class="pie-chart"></div>
                    </div>
                  </div>
                  <!-- 问题归因分析 -->
                  <div class="problem-analysis">
                    <div class="section-title">问题归因分析</div>
                    <a-row :gutter="[16, 16]">
                      <a-col :span="12">
                        <div class="chart-wrapper">
                          <div ref="waimaiOnlineProblemPie" style="height: 200px;"></div>
                        </div>
                      </a-col>
                      <a-col :span="12">
                        <div class="chart-wrapper">
                          <div ref="waimaiOnlineProblemTrend" style="height: 200px;"></div>
                        </div>
                      </a-col>
                    </a-row>
                    <a-button type="link" @click="showProblemDetail('waimai-online')">查看详情</a-button>
                  </div>
                </div>
              </a-card>
            </a-col>
            
            <!-- 外客电话 -->
            <a-col :span="8">
              <a-card class="business-line-card">
                <template #title>
                  <div class="business-line-title">
                    <span>外客电话</span>
                    <a-tag color="green">电话</a-tag>
                  </div>
                </template>
                <div class="business-line-content">
                  <div class="metrics">
                    <div class="metric-item">
                      <span class="label">转人工率</span>
                      <span class="value">18.2%</span>
                      <span class="trend down">↓ 1.5%</span>
                    </div>
                    <div class="metric-item">
                      <span class="label">解决率</span>
                      <span class="value">82.5%</span>
                      <span class="trend up">↑ 2.8%</span>
                    </div>
                  </div>
                  <div class="charts-container">
                    <div class="trend-chart">
                      <div ref="waimaiPhoneTrendChart" class="chart-container"></div>
                    </div>
                    <div class="issue-distribution">
                      <div ref="waimaiPhonePieChart" class="pie-chart"></div>
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>
            
            <!-- 拼好饭在线 -->
            <a-col :span="8">
              <a-card class="business-line-card">
                <template #title>
                  <div class="business-line-title">
                    <span>拼好饭在线</span>
                    <a-tag color="orange">在线</a-tag>
                  </div>
                </template>
                <div class="business-line-content">
                  <div class="metrics">
                    <div class="metric-item">
                      <span class="label">转人工率</span>
                      <span class="value">25.8%</span>
                      <span class="trend up">↑ 1.2%</span>
                    </div>
                    <div class="metric-item">
                      <span class="label">解决率</span>
                      <span class="value">75.6%</span>
                      <span class="trend down">↓ 0.8%</span>
                    </div>
                  </div>
                  <div class="charts-container">
                    <div class="trend-chart">
                      <div ref="pinhfOnlineTrendChart" class="chart-container"></div>
                    </div>
                    <div class="issue-distribution">
                      <div ref="pinhfOnlinePieChart" class="pie-chart"></div>
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>
            
            <!-- 骑手在线 -->
            <a-col :span="8">
              <a-card class="business-line-card">
                <template #title>
                  <div class="business-line-title">
                    <span>骑手在线</span>
                    <a-tag color="purple">在线</a-tag>
                  </div>
                </template>
                <div class="business-line-content">
                  <div class="metrics">
                    <div class="metric-item">
                      <span class="label">转人工率</span>
                      <span class="value">20.1%</span>
                      <span class="trend down">↓ 1.8%</span>
                    </div>
                    <div class="metric-item">
                      <span class="label">解决率</span>
                      <span class="value">79.8%</span>
                      <span class="trend up">↑ 2.2%</span>
                    </div>
                  </div>
                  <div class="charts-container">
                    <div class="trend-chart">
                      <div ref="riderOnlineTrendChart" class="chart-container"></div>
                    </div>
                    <div class="issue-distribution">
                      <div ref="riderOnlinePieChart" class="pie-chart"></div>
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>
            
            <!-- 骑手电话 -->
            <a-col :span="8">
              <a-card class="business-line-card">
                <template #title>
                  <div class="business-line-title">
                    <span>骑手电话</span>
                    <a-tag color="cyan">电话</a-tag>
                  </div>
                </template>
                <div class="business-line-content">
                  <div class="metrics">
                    <div class="metric-item">
                      <span class="label">转人工率</span>
                      <span class="value">15.6%</span>
                      <span class="trend down">↓ 2.3%</span>
                    </div>
                    <div class="metric-item">
                      <span class="label">解决率</span>
                      <span class="value">85.2%</span>
                      <span class="trend up">↑ 3.1%</span>
                    </div>
                  </div>
                  <div class="charts-container">
                    <div class="trend-chart">
                      <div ref="riderPhoneTrendChart" class="chart-container"></div>
                    </div>
                    <div class="issue-distribution">
                      <div ref="riderPhonePieChart" class="pie-chart"></div>
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>
            
            <!-- 外商在线 -->
            <a-col :span="8">
              <a-card class="business-line-card">
                <template #title>
                  <div class="business-line-title">
                    <span>外商在线</span>
                    <a-tag color="magenta">在线</a-tag>
                  </div>
                </template>
                <div class="business-line-content">
                  <div class="metrics">
                    <div class="metric-item">
                      <span class="label">转人工率</span>
                      <span class="value">22.3%</span>
                      <span class="trend up">↑ 0.9%</span>
                    </div>
                    <div class="metric-item">
                      <span class="label">解决率</span>
                      <span class="value">77.8%</span>
                      <span class="trend down">↓ 1.2%</span>
                    </div>
                  </div>
                  <div class="charts-container">
                    <div class="trend-chart">
                      <div ref="merchantOnlineTrendChart" class="chart-container"></div>
                    </div>
                    <div class="issue-distribution">
                      <div ref="merchantOnlinePieChart" class="pie-chart"></div>
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </a-card>

        <!-- 问题归因分析 -->
        <a-card class="analysis-card" title="问题归因分析">
          <a-row :gutter="[16, 16]">
            <a-col :span="12">
              <div class="analysis-section">
                <h3>主要问题类型分布</h3>
                <div ref="issueTypeChart" class="chart-container"></div>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="analysis-section">
                <h3>问题趋势分析</h3>
                <div ref="issueTrendChart" class="chart-container"></div>
              </div>
            </a-col>
          </a-row>
          <a-row :gutter="[16, 16]" style="margin-top: 16px">
            <a-col :span="24">
              <div class="analysis-section">
                <h3>问题详情列表</h3>
                <a-table
                  :columns="issueColumns"
                  :data-source="issueData"
                  :pagination="{ pageSize: 5 }"
                >
                  <template #bodyCell="{ column, text, record }">
                    <template v-if="column.dataIndex === 'status'">
                      <a-tag :color="getStatusColor(record.status)">
                        {{ text }}
                      </a-tag>
                    </template>
                    <template v-if="column.dataIndex === 'trend'">
                      <span :class="['trend', record.trend]">
                        {{ text }}
                      </span>
                    </template>
                  </template>
                </a-table>
              </div>
            </a-col>
          </a-row>
        </a-card>
      </a-tab-pane>
      
      <a-tab-pane key="performance-analysis" tab="详细场景大盘">
        <!-- 原有的效果分析内容 -->
        <!-- 对比模式开关 -->
        <div class="comparison-switch">
          <a-switch
            v-model:checked="isComparisonMode"
            checked-children="对比模式"
            un-checked-children="普通模式"
            @change="handleComparisonModeChange"
          />
        </div>
        
        <!-- 筛选条件 -->
        <a-card class="filter-card">
          <a-row :gutter="[16, 16]">
            <!-- 查询模式选择器 - 优化样式 -->
            <a-col :span="24">
              <div class="search-mode-tabs">
                <div 
                  class="search-mode-tab" 
                  :class="{ active: searchMode === 'precise' }"
                  @click="setSearchMode('precise')"
                >
                  <SearchOutlined class="tab-icon" />
                  <span class="tab-text">精准查询</span>
                </div>
                <div 
                  class="search-mode-tab" 
                  :class="{ active: searchMode === 'intelligent' }"
                  @click="setSearchMode('intelligent')"
                >
                  <ThunderboltOutlined class="tab-icon" />
                  <span class="tab-text">智能分析</span>
                </div>
              </div>
            </a-col>
            
            <!-- 智能分析模式 - 智能搜索输入框 - 增强样式 -->
            <a-col :span="24" v-if="searchMode === 'intelligent'">
              <div class="ai-search-container">
                <div class="ai-search-header">
                  <span class="ai-badge">AI</span>
                  <span class="ai-search-title">智能分析搜索</span>
                </div>
                <a-form-item>
                  <a-input-search
                    v-model:value="smartSearch"
                    placeholder="请输入自然语言描述，如：3-5号外客履约有退款的转人工率"
                    enter-button="智能分析"
                    size="large"
                    @search="handleSmartSearch"
                    allow-clear
                    @clear="handleClearSmartSearch"
                    class="ai-input"
                  >
                    <template #enterButton>
                      <div class="ai-button">
                        <ThunderboltOutlined class="ai-icon" />
                        <span>智能分析</span>
                      </div>
                    </template>
                  </a-input-search>
                  <div class="ai-search-hint">
                    <InfoCircleOutlined style="margin-right: 4px;" />
                    提示：您可以使用自然语言描述想要分析的数据，例如"上周外客流程的转人工率"
                  </div>
                </a-form-item>
              </div>
            </a-col>
            
            <!-- 结构化查询展示 -->
            <a-col :span="24" v-if="structuredQuery">
              <div class="structured-query-container">
                <div class="structured-query-title">
                  <InfoCircleOutlined /> 智能分析结果
                </div>
                <div class="structured-query-tags">
                  <a-tag v-if="structuredQuery.dateRange" color="blue">
                    <ClockCircleOutlined /> 搜索时间: {{ structuredQuery.dateRange }}
                  </a-tag>
                  <a-tag v-if="structuredQuery.scenario" color="green">
                    <AppstoreOutlined /> 场景: {{ structuredQuery.scenario }}
                  </a-tag>
                  <a-tag v-if="structuredQuery.action" color="orange">
                    <ThunderboltOutlined /> 执行动作: {{ structuredQuery.action }}
                  </a-tag>
                  <a-tag v-if="structuredQuery.metric" color="purple">
                    <BarChartOutlined /> 指标: {{ structuredQuery.metric }}
                  </a-tag>
                </div>
              </div>
            </a-col>
            
            <!-- 精准查询模式 - 条件筛选 -->
            <template v-if="searchMode === 'precise' && !isComparisonMode">
              <a-col :span="24">
                <a-form-item label="场景">
                  <a-select
                    v-model:value="filters.scenario"
                    placeholder="请选择场景"
                    style="width: 100%"
                    @change="handleScenarioChange"
                  >
                    <a-select-option value="all">全部场景</a-select-option>
                    <a-select-option value="外客在线智能">外客在线智能</a-select-option>
                    <a-select-option value="骑手在线智能">骑手在线智能</a-select-option>
                    <a-select-option value="拼好饭在线智能">拼好饭在线智能</a-select-option>
                    <a-select-option value="拼好饭在线虚拟客服">拼好饭在线虚拟客服</a-select-option>
                    <a-select-option value="外商在线智能">外商在线智能</a-select-option>
                    <a-select-option value="外客直出">外客直出</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="时间范围">
                  <a-range-picker
                    v-model:value="filters.dateRange"
                    :format="dateFormat"
                    style="width: 100%"
                    @change="handleDateChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="指标类型">
                  <a-select
                    v-model:value="filters.metricType"
                    placeholder="请选择指标类型"
                    style="width: 100%"
                  >
                    <a-select-option value="transferRate">转人工率</a-select-option>
                    <a-select-option value="resolutionRate">解决率</a-select-option>
                    <a-select-option value="refundRate">退款率</a-select-option>
                    <a-select-option value="compensationRate">赔付率</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="执行动作">
                  <a-select
                    v-model:value="filters.modelExecutionPlan"
                    placeholder="请选择执行动作"
                    style="width: 100%"
                  >
                    <a-select-option value="">全部</a-select-option>
                    <a-select-option value="包含退款">包含退款</a-select-option>
                    <a-select-option value="不包含退款">不包含退款</a-select-option>
                    <a-select-option value="包含赔付">包含赔付</a-select-option>
                    <a-select-option value="需要转人工">需要转人工</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <!-- 新增实验组筛选 -->
              <a-col :span="6">
                <a-form-item label="实验组">
                  <a-select v-model:value="filters.experimentGroup" placeholder="请选择实验组" style="width: 100%">
                    <a-select-option v-for="group in experimentGroups[filters.scenario] || experimentGroups.default" :key="group" :value="group">{{ group }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <!-- 新增模型筛选 -->
              <a-col :span="6">
                <a-form-item label="模型">
                  <a-select v-model:value="filters.modelVersion" placeholder="请选择模型" style="width: 100%">
                    <a-select-option v-for="model in modelVersions[filters.scenario] || modelVersions.default" :key="model" :value="model">{{ model }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <!-- 在实验组/模型后面插入 -->
              <a-col :span="8">
                <a-form-item label="统计因子">
                  <a-select
                    v-model:value="filters.selectedFactors"
                    mode="multiple"
                    placeholder="请选择统计因子"
                    style="width: 100%"
                  >
                    <a-select-option v-for="factor in factorOptions" :key="factor.value" :value="factor.value">
                      {{ factor.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </template>
            
            <!-- 对比模式下的两组筛选条件 -->
            <template v-else-if="isComparisonMode">
              <!-- 第一组条件 -->
              <a-col :span="12">
                <div class="comparison-group">
                  <div class="comparison-title">条件一</div>
                  <a-form-item label="场景">
                    <a-select
                      v-model:value="comparisonFilters.group1.scenario"
                      placeholder="请选择场景"
                      style="width: 100%"
                      @change="handleComparisonScenarioChange(1)"
                    >
                      <a-select-option value="all">全部场景</a-select-option>
                      <a-select-option value="外客在线智能">外客在线智能</a-select-option>
                      <a-select-option value="骑手在线智能">骑手在线智能</a-select-option>
                      <a-select-option value="拼好饭在线智能">拼好饭在线智能</a-select-option>
                      <a-select-option value="拼好饭在线虚拟客服">拼好饭在线虚拟客服</a-select-option>
                      <a-select-option value="外商在线智能">外商在线智能</a-select-option>
                      <a-select-option value="外客直出">外客直出</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="订单状态">
                    <a-select
                      v-model:value="comparisonFilters.group1.orderStatus"
                      placeholder="请选择订单状态"
                      style="width: 100%"
                    >
                      <a-select-option value="all">全部状态</a-select-option>
                      <a-select-option value="completed">已完成</a-select-option>
                      <a-select-option value="delivering">配送中</a-select-option>
                      <a-select-option value="pending">待处理</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="时间范围">
                    <a-range-picker
                      v-model:value="comparisonFilters.group1.dateRange"
                      :format="dateFormat"
                      style="width: 100%"
                    />
                  </a-form-item>
                </div>
              </a-col>
              
              <!-- 第二组条件 -->
              <a-col :span="12">
                <div class="comparison-group">
                  <div class="comparison-title">条件二</div>
                  <a-form-item label="场景">
                    <a-select
                      v-model:value="comparisonFilters.group2.scenario"
                      placeholder="请选择场景"
                      style="width: 100%"
                      @change="handleComparisonScenarioChange(2)"
                    >
                      <a-select-option value="all">全部场景</a-select-option>
                      <a-select-option value="外客在线智能">外客在线智能</a-select-option>
                      <a-select-option value="骑手在线智能">骑手在线智能</a-select-option>
                      <a-select-option value="拼好饭在线智能">拼好饭在线智能</a-select-option>
                      <a-select-option value="拼好饭在线虚拟客服">拼好饭在线虚拟客服</a-select-option>
                      <a-select-option value="外商在线智能">外商在线智能</a-select-option>
                      <a-select-option value="外客直出">外客直出</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="订单状态">
                    <a-select
                      v-model:value="comparisonFilters.group2.orderStatus"
                      placeholder="请选择订单状态"
                      style="width: 100%"
                    >
                      <a-select-option value="all">全部状态</a-select-option>
                      <a-select-option value="completed">已完成</a-select-option>
                      <a-select-option value="delivering">配送中</a-select-option>
                      <a-select-option value="pending">待处理</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="时间范围">
                    <a-range-picker
                      v-model:value="comparisonFilters.group2.dateRange"
                      :format="dateFormat"
                      style="width: 100%"
                    />
                  </a-form-item>
                </div>
              </a-col>
            </template>
          </a-row>
          
          <a-row>
            <a-col :span="24" style="text-align: right">
              <a-button 
                type="primary" 
                @click="handleSearch" 
                style="margin-right: 8px"
                :class="{'ai-search-button': searchMode === 'intelligent'}"
              >
                <template v-if="searchMode === 'intelligent'">
                  <ThunderboltOutlined />
                  <span>智能分析</span>
                </template>
                <template v-else>
                  <SearchOutlined />
                  <span>精准查询</span>
                </template>
              </a-button>
              <a-button @click="resetFilters">
                重置
              </a-button>
            </a-col>
          </a-row>
          
          <!-- 结构化查询条件显示 -->
          <a-row v-if="filters.isAnalyzed && filters.structuredQuery" style="margin-top: 16px">
            <a-col :span="24">
              <div class="structured-query">
                <div class="structured-query-header">
                  <a-tag color="blue">大模型分析结果</a-tag>
                </div>
                <div class="structured-query-content">
                  <template v-if="filters.structuredQuery.dateRange">
                    <a-tag color="green">时间范围: {{ filters.structuredQuery.dateRangeText }}</a-tag>
                  </template>
                  <template v-if="filters.structuredQuery.scenario">
                    <a-tag color="purple">场景: {{ filters.structuredQuery.scenario }}</a-tag>
                  </template>
                  <template v-if="filters.structuredQuery.modelExecutionPlan">
                    <a-tag color="orange">执行动作: {{ filters.structuredQuery.modelExecutionPlan }}</a-tag>
                  </template>
                  <template v-if="filters.structuredQuery.metricType">
                    <a-tag color="cyan">指标: {{ 
                      filters.structuredQuery.metricType === 'transferRate' ? '转人工率' : 
                      filters.structuredQuery.metricType === 'resolutionRate' ? '解决率' : 
                      filters.structuredQuery.metricType === 'refundRate' ? '退款率' : 
                      filters.structuredQuery.metricType === 'compensationRate' ? '赔付率' : ''
                    }}</a-tag>
                  </template>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-card>
        
        <!-- 核心指标卡片 -->
        <div class="metric-cards">
          <a-row :gutter="[16, 16]">
            <template v-if="!isComparisonMode">
              <a-col :span="6" v-for="(card, index) in filteredMetricCards" :key="index">
                <a-card class="metric-card" :class="{ 'warning': card.trend === 'up' && card.isNegative }">
                  <div class="metric-header">
                    <span class="metric-title">{{ card.title }}</span>
                    <a-tooltip :title="card.description">
                      <QuestionCircleOutlined class="help-icon" />
                    </a-tooltip>
                  </div>
                  <div class="metric-value">
                    {{ card.value }}
                    <span class="trend-icon" :class="getTrendClass(card)">
                      <component :is="getTrendIcon(card)" />
                      {{ card.change }}
                    </span>
                  </div>
                  <div class="metric-footer">
                    较{{ filters.dateRange ? '所选时间段' : '上月' }}
                  </div>
                </a-card>
              </a-col>
            </template>
            <template v-else>
              <a-col :span="6" v-for="(card, index) in comparisonMetricCards" :key="index">
                <a-card class="metric-card comparison-card">
                  <div class="metric-header">
                    <span class="metric-title">{{ card.title }}</span>
                    <a-tooltip :title="card.description">
                      <QuestionCircleOutlined class="help-icon" />
                    </a-tooltip>
                  </div>
                  <div class="metric-comparison">
                    <div class="comparison-value">
                      <div class="value-label">条件一</div>
                      <div class="value-number">{{ card.group1.value }}</div>
                      <div class="value-trend" :class="getTrendClass(card.group1)">
                        <component :is="getTrendIcon(card.group1)" />
                        {{ card.group1.change }}
                      </div>
                    </div>
                    <div class="comparison-value">
                      <div class="value-label">条件二</div>
                      <div class="value-number">{{ card.group2.value }}</div>
                      <div class="value-trend" :class="getTrendClass(card.group2)">
                        <component :is="getTrendIcon(card.group2)" />
                        {{ card.group2.change }}
                      </div>
                    </div>
                    <div class="comparison-diff" :class="getDiffClass(card)">
                      <span>差异: {{ card.diff }}</span>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </template>
          </a-row>
        </div>
        
        <!-- 场景指标对比表格 -->
        <a-card class="data-card" :title="isComparisonMode ? '场景指标对比' : '场景指标对比'">
          <a-table
            :columns="isComparisonMode ? comparisonColumns : columns"
            :data-source="isComparisonMode ? comparisonTableData : filteredTableData"
            :pagination="{ pageSize: 5 }"
            :scroll="{ x: 1200 }"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'scenario'">
                <a-tag 
                  :color="getScenarioColor(record.scenario)"
                  class="scenario-tag"
                  @click="handleScenarioClick(record)"
                >
                  {{ text }}
                  <RightOutlined v-if="hasChildren(record.scenario)" />
                </a-tag>
              </template>
              <template v-else-if="['transferRate', 'resolutionRate', 'refundRate', 'compensationRate'].includes(column.dataIndex)">
                <div>
                  {{ text }}%
                  <span class="trend-indicator" :class="record[column.dataIndex + 'Trend'] === 'up' ? (column.isNegative ? 'negative' : 'positive') : (column.isNegative ? 'positive' : 'negative')">
                    <component :is="record[column.dataIndex + 'Trend'] === 'up' ? 'ArrowUpOutlined' : 'ArrowDownOutlined'" />
                    {{ record[column.dataIndex + 'Change'] }}%
                  </span>
                </div>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 指标趋势分析卡片，移动到这里 -->
        <a-card class="data-card" title="指标趋势分析">
          <div class="trend-analysis-header">
            <span class="trend-title">
              {{ getTrendAnalysisTitle() }}
            </span>
            <div class="trend-legend">
              <span v-for="(item, index) in getTrendAnalysisLegend()" :key="index" class="legend-item">
                <span class="legend-color" :style="{ backgroundColor: item.color }"></span>
                <span class="legend-text">{{ item.name }}</span>
              </span>
            </div>
          </div>
          <a-tabs v-model:activeKey="activeChartTab" @change="handleTabChange">
            <a-tab-pane key="transferRate" tab="转人工率趋势">
              <div class="chart-container" ref="transferRateChart"></div>
            </a-tab-pane>
            <a-tab-pane key="resolutionRate" tab="解决率趋势">
              <div class="chart-container" ref="resolutionRateChart"></div>
            </a-tab-pane>
            <a-tab-pane key="refundRate" tab="退款率趋势">
              <div class="chart-container" ref="refundRateChart"></div>
            </a-tab-pane>
            <a-tab-pane key="compensationRate" tab="赔付率趋势">
              <div class="chart-container" ref="compensationRateChart"></div>
            </a-tab-pane>
          </a-tabs>
        </a-card>

        <!-- 多因子组合分组统计卡片 -->
        <a-card class="data-card" title="多因子组合分组统计" style="margin-bottom: 24px;">
          <a-row :gutter="[16, 16]">
            <a-col :span="12">
              <a-table :columns="factorStatsColumns" :data-source="factorStatsData" :pagination="false" />
              <a-modal v-model:visible="factorDetailVisible" :title="factorDetailTitle" width="800px" footer="null">
                <a-table :columns="Object.keys(tableData[0]||{}).map(k=>({title:k,label:k,dataIndex:k,key:k}))" :data-source="factorDetailList" :pagination="{ pageSize: 10 }" />
              </a-modal>
            </a-col>
            <a-col :span="12">
              <div ref="factorPieChart" style="height: 300px; min-width: 300px;"></div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 问题归因分析卡片，保持在趋势分析卡片之后 -->
        <a-card class="analysis-card" title="问题归因分析" style="margin-top: 24px;">
          <a-row :gutter="[16, 16]">
            <a-col :span="12" style="min-width:320px;min-height:320px;">
              <div ref="problemPieChart" id="problemPieChart" style="height: 300px; min-width: 300px;"></div>
            </a-col>
            <a-col :span="12" v-if="selectedProblemType">
              <div ref="problemSubPieChart" id="problemSubPieChart" style="height: 300px; min-width: 300px;"></div>
            </a-col>
          </a-row>
          <a-table
            :columns="problemColumns"
            :data-source="problemTableComputedData"
            :pagination="{ pageSize: 5 }"
            style="margin-top: 24px;"
          />
        </a-card>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, nextTick, onUnmounted, computed, watch, h } from 'vue';
import { 
  SearchOutlined, 
  QuestionCircleOutlined, 
  ArrowUpOutlined, 
  ArrowDownOutlined,
  RightOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined,
  AppstoreOutlined,
  ThunderboltOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue';
import * as echarts from 'echarts';
import { PieChart } from 'echarts/charts';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';

echarts.use([PieChart]);

export default defineComponent({
  name: 'PerformanceDashboard',
  components: {
    SearchOutlined,
    QuestionCircleOutlined,
    ArrowUpOutlined,
    ArrowDownOutlined,
    RightOutlined,
    InfoCircleOutlined,
    ClockCircleOutlined,
    AppstoreOutlined,
    ThunderboltOutlined,
    BarChartOutlined
  },
  setup() {
    // 日期格式
    const dateFormat = 'YYYY-MM-DD';
    
    // 添加搜索模式状态
    const searchMode = ref('precise'); // 'precise' 精准查询 or 'intelligent' 智能分析
    
    // 筛选条件
    const filters = reactive({
      scenario: 'all',
      description: '',
      dateRange: null,
      metricType: 'transferRate',
      modelExecutionPlan: '',
      structuredQuery: null,
      isAnalyzed: false,
      experimentGroup: '',
      modelVersion: '',
      selectedFactors: ['orderStatus', 'deliveryStatus']
    });

    const metricCards = ref([]);
    const tableData = ref([]);
    const factorRawData = ref([]); // 👈 提前声明，避免引用未初始化
    const activeChartTab = ref('transferRate');
    const selectedScenarioPath = ref([]); // 只保留这一份
    
    // 图表选项
    const chartOptions = reactive({
      transferRate: {
        title: { text: '转人工率趋势' },
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: [] },
        yAxis: { type: 'value', axisLabel: { formatter: '{value}%' } },
        series: [{ data: [], type: 'line', smooth: true }]
      },
      resolutionRate: {
        title: { text: '解决率趋势' },
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: [] },
        yAxis: { type: 'value', axisLabel: { formatter: '{value}%' } },
        series: [{ data: [], type: 'line', smooth: true }]
      },
      refundRate: {
        title: { text: '退款率趋势' },
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: [] },
        yAxis: { type: 'value', axisLabel: { formatter: '{value}%' } },
        series: [{ data: [], type: 'line', smooth: true }]
      },
      compensationRate: {
        title: { text: '赔付率趋势' },
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: [] },
        yAxis: { type: 'value', axisLabel: { formatter: '{value}%' } },
        series: [{ data: [], type: 'line', smooth: true }]
      }
    });

    // 场景层级数据
    const scenarioHierarchy = {
      '外客在线智能': {
        name: '外客在线智能',
        children: {
          '外客-履约': {
            name: '外客-履约',
            children: {
              '标问-订单超时': { name: '标问-订单超时' },
              '标问-无骑手接单': { name: '标问-无骑手接单' },
              '标问-订单未到': { name: '标问-订单未到' }
            }
          },
          '外客-售后': {
            name: '外客-售后',
            children: {
              '标问-餐品撒漏': { name: '标问-餐品撒漏' },
              '标问-餐品少送': { name: '标问-餐品少送' }
            }
          },
          '外客-全场景': {
            name: '外客-全场景',
            children: {
              '标问-订单超时': { name: '标问-订单超时' },
              '标问-无骑手接单': { name: '标问-无骑手接单' },
              '标问-订单未到': { name: '标问-订单未到' },
              '标问-餐品撒漏': { name: '标问-餐品撒漏' },
              '标问-餐品少送': { name: '标问-餐品少送' }
            }
          }
        }
      }
    }; // 只保留这一份

    // 图表实例
    const charts = reactive({
      transferRate: null,
      resolutionRate: null,
      refundRate: null,
      compensationRate: null
    });

    // 对比模式相关状态
    const isComparisonMode = ref(false);
    const comparisonFilters = reactive({
      group1: {
        scenario: 'all',
        orderStatus: 'all',
        dateRange: null
      },
      group2: {
        scenario: 'all',
        orderStatus: 'all',
        dateRange: null
      }
    });
    
    // 对比模式下的指标卡片数据
    const comparisonMetricCards = computed(() => {
      // 获取两组条件的数据
      const group1Data = getComparisonData(comparisonFilters.group1);
      const group2Data = getComparisonData(comparisonFilters.group2);
      
      return [
        {
          title: '转人工率',
          description: '智能客服无法解决转由人工客服处理的比率',
          group1: {
            value: `${group1Data.transferRate}%`,
            change: `${group1Data.transferRateChange}%`,
            trend: group1Data.transferRateTrend
          },
          group2: {
            value: `${group2Data.transferRate}%`,
            change: `${group2Data.transferRateChange}%`,
            trend: group2Data.transferRateTrend
          },
          diff: `${(group2Data.transferRate - group1Data.transferRate).toFixed(1)}%`,
          isNegative: true
        },
        {
          title: '解决率',
          description: '智能客服成功解决客户问题的比率',
          group1: {
            value: `${group1Data.resolutionRate}%`,
            change: `${group1Data.resolutionRateChange}%`,
            trend: group1Data.resolutionRateTrend
          },
          group2: {
            value: `${group2Data.resolutionRate}%`,
            change: `${group2Data.resolutionRateChange}%`,
            trend: group2Data.resolutionRateTrend
          },
          diff: `${(group2Data.resolutionRate - group1Data.resolutionRate).toFixed(1)}%`,
          isNegative: false
        },
        {
          title: '退款率',
          description: '客户要求退款的订单比率',
          group1: {
            value: `${group1Data.refundRate}%`,
            change: `${group1Data.refundRateChange}%`,
            trend: group1Data.refundRateTrend
          },
          group2: {
            value: `${group2Data.refundRate}%`,
            change: `${group2Data.refundRateChange}%`,
            trend: group2Data.refundRateTrend
          },
          diff: `${(group2Data.refundRate - group1Data.refundRate).toFixed(1)}%`,
          isNegative: true
        },
        {
          title: '赔付率',
          description: '需要赔付的订单比率',
          group1: {
            value: `${group1Data.compensationRate}%`,
            change: `${group1Data.compensationRateChange}%`,
            trend: group1Data.compensationRateTrend
          },
          group2: {
            value: `${group2Data.compensationRate}%`,
            change: `${group2Data.compensationRateChange}%`,
            trend: group2Data.compensationRateTrend
          },
          diff: `${(group2Data.compensationRate - group1Data.compensationRate).toFixed(1)}%`,
          isNegative: true
        }
      ];
    });
    
    // 对比模式下的表格列定义
    const comparisonColumns = [
      {
        title: '指标',
        dataIndex: 'metric',
        key: 'metric',
        fixed: 'left',
        width: 120
      },
      {
        title: '条件一',
        dataIndex: 'group1',
        key: 'group1',
        width: 200
      },
      {
        title: '条件二',
        dataIndex: 'group2',
        key: 'group2',
        width: 200
      },
      {
        title: '差异',
        dataIndex: 'diff',
        key: 'diff',
        width: 120
      }
    ];
    
    // 对比模式下的表格数据
    const comparisonTableData = computed(() => {
      const group1Data = getComparisonData(comparisonFilters.group1);
      const group2Data = getComparisonData(comparisonFilters.group2);
      
      return [
        {
          key: '1',
          metric: '转人工率',
          group1: `${group1Data.transferRate}%`,
          group2: `${group2Data.transferRate}%`,
          diff: `${(group2Data.transferRate - group1Data.transferRate).toFixed(1)}%`
        },
        {
          key: '2',
          metric: '解决率',
          group1: `${group1Data.resolutionRate}%`,
          group2: `${group2Data.resolutionRate}%`,
          diff: `${(group2Data.resolutionRate - group1Data.resolutionRate).toFixed(1)}%`
        },
        {
          key: '3',
          metric: '退款率',
          group1: `${group1Data.refundRate}%`,
          group2: `${group2Data.refundRate}%`,
          diff: `${(group2Data.refundRate - group1Data.refundRate).toFixed(1)}%`
        },
        {
          key: '4',
          metric: '赔付率',
          group1: `${group1Data.compensationRate}%`,
          group2: `${group2Data.compensationRate}%`,
          diff: `${(group2Data.compensationRate - group1Data.compensationRate).toFixed(1)}%`
        }
      ];
    });
    
    // 获取对比数据
    const getComparisonData = (filters) => {
      // 根据筛选条件生成基础数据
      const baseData = {
        transferRate: Math.floor(Math.random() * 10) + 15,
        resolutionRate: Math.floor(Math.random() * 10) + 70,
        refundRate: Math.floor(Math.random() * 5) + 5,
        compensationRate: Math.floor(Math.random() * 3) + 3
      };

      // 根据场景调整数据
      if (filters.scenario && filters.scenario !== 'all') {
        const scenarioAdjustments = {
          '外客在线智能': { transferRate: 2, resolutionRate: -3, refundRate: 1, compensationRate: 0.5 },
          '骑手在线智能': { transferRate: -1, resolutionRate: 2, refundRate: -0.5, compensationRate: -0.5 },
          '拼好饭在线智能': { transferRate: 1, resolutionRate: -2, refundRate: 0.5, compensationRate: 0.3 },
          '拼好饭在线虚拟客服': { transferRate: 2, resolutionRate: -4, refundRate: 1, compensationRate: 0.8 },
          '外商在线智能': { transferRate: -0.5, resolutionRate: 1, refundRate: -0.3, compensationRate: -0.2 },
          '外客直出': { transferRate: -2, resolutionRate: 3, refundRate: -1, compensationRate: -0.8 }
        };

        const adjustment = scenarioAdjustments[filters.scenario] || {};
        baseData.transferRate += adjustment.transferRate || 0;
        baseData.resolutionRate += adjustment.resolutionRate || 0;
        baseData.refundRate += adjustment.refundRate || 0;
        baseData.compensationRate += adjustment.compensationRate || 0;
      }

      // 根据订单状态调整数据
      if (filters.orderStatus && filters.orderStatus !== 'all') {
        const statusAdjustments = {
          'completed': { transferRate: -1, resolutionRate: 2, refundRate: -0.5, compensationRate: -0.3 },
          'delivering': { transferRate: 1, resolutionRate: -1, refundRate: 0.3, compensationRate: 0.2 },
          'pending': { transferRate: 2, resolutionRate: -2, refundRate: 0.5, compensationRate: 0.4 }
        };

        const adjustment = statusAdjustments[filters.orderStatus] || {};
        baseData.transferRate += adjustment.transferRate || 0;
        baseData.resolutionRate += adjustment.resolutionRate || 0;
        baseData.refundRate += adjustment.refundRate || 0;
        baseData.compensationRate += adjustment.compensationRate || 0;
      }

      // 根据时间范围调整数据
      if (filters.dateRange && filters.dateRange.length === 2) {
        const daysDiff = dayjs(filters.dateRange[1]).diff(dayjs(filters.dateRange[0]), 'day');
        if (daysDiff > 0) {
          baseData.transferRate = Math.max(10, baseData.transferRate - (daysDiff * 0.2));
          baseData.resolutionRate = Math.min(95, baseData.resolutionRate + (daysDiff * 0.3));
          baseData.refundRate = Math.max(2, baseData.refundRate - (daysDiff * 0.1));
          baseData.compensationRate = Math.max(1, baseData.compensationRate - (daysDiff * 0.05));
        }
      }

      // 生成趋势数据
      const trend = Math.random() > 0.5 ? 'up' : 'down';
      const change = (Math.random() * 2).toFixed(1);

      return {
        ...baseData,
        transferRateChange: change,
        transferRateTrend: trend,
        resolutionRateChange: change,
        resolutionRateTrend: trend,
        refundRateChange: change,
        refundRateTrend: trend,
        compensationRateChange: change,
        compensationRateTrend: trend
      };
    };
    
    // 处理对比模式切换
    const handleComparisonModeChange = (checked) => {
      isComparisonMode.value = checked;
      if (checked) {
        // 切换到对比模式时，重置对比筛选条件
        comparisonFilters.group1 = {
          scenario: 'all',
          orderStatus: 'all',
          dateRange: null
        };
        comparisonFilters.group2 = {
          scenario: 'all',
          orderStatus: 'all',
          dateRange: null
        };
      }
    };
    
    // 处理对比场景变更
    const handleComparisonScenarioChange = (group) => {
      console.log(`对比组${group}场景变更:`, comparisonFilters[`group${group}`].scenario);
    };
    
    // 获取差异样式类
    const getDiffClass = (card) => {
      const diff = parseFloat(card.diff);
      if (card.isNegative) {
        return diff < 0 ? 'positive' : 'negative';
      } else {
        return diff > 0 ? 'positive' : 'negative';
      }
    };
    
    // 初始化数据
    onMounted(() => {
      // 1. 恢复业务指标mock数据，专供"场景指标对比"表格使用
      tableData.value = [
        {
          key: '1',
          scenario: '外客在线智能',
          transferRate: 23.5,
          transferRateTrend: 'down',
          transferRateChange: 2.1,
          resolutionRate: 78.2,
          resolutionRateTrend: 'up',
          resolutionRateChange: 3.5,
          refundRate: 8.7,
          refundRateTrend: 'down',
          refundRateChange: 1.2,
          compensationRate: 5.3,
          compensationRateTrend: 'down',
          compensationRateChange: 0.8,
          satisfaction: 4.2,
          dailyConversations: 15680
        },
        {
          key: '2',
          scenario: '骑手在线智能',
          transferRate: 18.2,
          transferRateTrend: 'down',
          transferRateChange: 1.5,
          resolutionRate: 82.5,
          resolutionRateTrend: 'up',
          resolutionRateChange: 2.8,
          refundRate: 6.5,
          refundRateTrend: 'down',
          refundRateChange: 0.8,
          compensationRate: 4.1,
          compensationRateTrend: 'down',
          compensationRateChange: 0.5,
          satisfaction: 4.5,
          dailyConversations: 12800
        },
        {
          key: '3',
          scenario: '拼好饭在线智能',
          transferRate: 25.8,
          transferRateTrend: 'up',
          transferRateChange: 1.2,
          resolutionRate: 75.6,
          resolutionRateTrend: 'down',
          resolutionRateChange: 0.8,
          refundRate: 9.2,
          refundRateTrend: 'up',
          refundRateChange: 1.5,
          compensationRate: 6.8,
          compensationRateTrend: 'up',
          compensationRateChange: 1.2,
          satisfaction: 4.0,
          dailyConversations: 9800
        },
        {
          key: '4',
          scenario: '拼好饭在线虚拟客服',
          transferRate: 28.4,
          transferRateTrend: 'up',
          transferRateChange: 2.3,
          resolutionRate: 72.1,
          resolutionRateTrend: 'down',
          resolutionRateChange: 1.5,
          refundRate: 10.5,
          refundRateTrend: 'up',
          refundRateChange: 2.1,
          compensationRate: 7.2,
          compensationRateTrend: 'up',
          compensationRateChange: 1.5,
          satisfaction: 3.8,
          dailyConversations: 8500
        },
        {
          key: '5',
          scenario: '外商在线智能',
          transferRate: 20.3,
          transferRateTrend: 'down',
          transferRateChange: 1.8,
          resolutionRate: 80.5,
          resolutionRateTrend: 'up',
          resolutionRateChange: 2.2,
          refundRate: 7.8,
          refundRateTrend: 'down',
          refundRateChange: 0.9,
          compensationRate: 4.9,
          compensationRateTrend: 'down',
          compensationRateChange: 0.6,
          satisfaction: 4.3,
          dailyConversations: 11200
        },
        {
          key: '6',
          scenario: '外客直出',
          transferRate: 15.2,
          transferRateTrend: 'down',
          transferRateChange: 2.5,
          resolutionRate: 85.7,
          resolutionRateTrend: 'up',
          resolutionRateChange: 3.2,
          refundRate: 5.3,
          refundRateTrend: 'down',
          refundRateChange: 1.5,
          compensationRate: 3.2,
          compensationRateTrend: 'down',
          compensationRateChange: 1.2,
          satisfaction: 4.6,
          dailyConversations: 9200
        }
      ];
      // 2. 新增多因子组合原始数据，专供"多因子组合分组统计"使用
      factorRawData.value = generateMockFactorData();

      // 初始化指标卡片数据
      metricCards.value = [
        {
          title: '转人工率',
          value: '21.9%',
          change: '1.7%',
          trend: 'down',
          isNegative: true,
          description: '智能客服无法解决转由人工客服处理的比率'
        },
        {
          title: '解决率',
          value: '79.1%',
          change: '2.2%',
          trend: 'up',
          isNegative: false,
          description: '智能客服成功解决客户问题的比率'
        },
        {
          title: '退款率',
          value: '8.0%',
          change: '1.2%',
          trend: 'down',
          isNegative: true,
          description: '客户要求退款的订单比率'
        },
        {
          title: '赔付率',
          value: '5.3%',
          change: '0.8%',
          trend: 'down',
          isNegative: true,
          description: '需要赔付的订单比率'
        }
      ];

      // 初始化图表
      nextTick(() => {
        initChart(activeChartTab.value);
      });
    });

    // 组件卸载时清理资源
    onUnmounted(() => {
      Object.keys(charts).forEach(type => {
        if (charts[type]) {
          charts[type].dispose();
          charts[type] = null;
        }
      });
    });
    
    // 表格列定义
    const columns = [
      {
        title: '场景',
        dataIndex: 'scenario',
        key: 'scenario',
        fixed: 'left',
        width: 180
      },
      {
        title: '转人工率',
        dataIndex: 'transferRate',
        key: 'transferRate',
        sorter: (a, b) => a.transferRate - b.transferRate,
        isNegative: true
      },
      {
        title: '解决率',
        dataIndex: 'resolutionRate',
        key: 'resolutionRate',
        sorter: (a, b) => a.resolutionRate - b.resolutionRate,
        isNegative: false
      },
      {
        title: '退款率',
        dataIndex: 'refundRate',
        key: 'refundRate',
        sorter: (a, b) => a.refundRate - b.refundRate,
        isNegative: true
      },
      {
        title: '赔付率',
        dataIndex: 'compensationRate',
        key: 'compensationRate',
        sorter: (a, b) => a.compensationRate - b.compensationRate,
        isNegative: true
      },
      {
        title: '用户满意度',
        dataIndex: 'satisfaction',
        key: 'satisfaction',
        sorter: (a, b) => a.satisfaction - b.satisfaction,
        isNegative: false
      },
      {
        title: '日均对话量',
        dataIndex: 'dailyConversations',
        key: 'dailyConversations',
        sorter: (a, b) => a.dailyConversations - b.dailyConversations,
        isNegative: false
      }
    ];
    
    // 过滤后的表格数据
    const filteredTableData = computed(() => {
      if (filters.scenario === 'all') {
        return tableData.value;
      } else {
        return tableData.value.filter(item => item.scenario === filters.scenario);
      }
    });
    
    // 过滤后的指标卡片数据
    const filteredMetricCards = computed(() => {
      if (filters.scenario === 'all') {
        return metricCards.value;
      } else {
        // 获取选中场景的数据
        const scenarioData = tableData.value.find(item => item.scenario === filters.scenario);
        if (!scenarioData) return metricCards.value;
        
        // 更新指标卡片数据
        return [
          {
            title: '转人工率',
            value: `${scenarioData.transferRate}%`,
            change: `${scenarioData.transferRateChange}%`,
            trend: scenarioData.transferRateTrend,
            isNegative: true,
            description: '智能客服无法解决转由人工客服处理的比率'
          },
          {
            title: '解决率',
            value: `${scenarioData.resolutionRate}%`,
            change: `${scenarioData.resolutionRateChange}%`,
            trend: scenarioData.resolutionRateTrend,
            isNegative: false,
            description: '智能客服成功解决客户问题的比率'
          },
          {
            title: '退款率',
            value: `${scenarioData.refundRate}%`,
            change: `${scenarioData.refundRateChange}%`,
            trend: scenarioData.refundRateTrend,
            isNegative: true,
            description: '客户要求退款的订单比率'
          },
          {
            title: '赔付率',
            value: `${scenarioData.compensationRate}%`,
            change: `${scenarioData.compensationRateChange}%`,
            trend: scenarioData.compensationRateTrend,
            isNegative: true,
            description: '需要赔付的订单比率'
          }
        ];
      }
    });
    
    
    // 获取转人工率趋势图表的options
    const getTransferRateOptions = () => {
      const timeRange = ['1月', '2月', '3月', '4月', '5月', '6月'];
      
      // 如果选择了特定场景，只显示该场景的数据
      const series = [];
      if (filters.scenario !== 'all') {
        const scenarioData = {
          '外客在线智能': [24.5, 25.1, 24.8, 23.9, 23.1, 23.5],
          '骑手在线智能': [21.3, 20.5, 19.8, 19.2, 18.9, 18.7],
          '拼好饭在线智能': [23.5, 24.2, 25.1, 25.5, 25.9, 25.8],
          '拼好饭在线虚拟客服': [25.8, 26.5, 27.1, 27.8, 28.2, 28.4],
          '外商在线智能': [22.1, 21.8, 21.3, 20.8, 20.5, 20.3],
          '外客直出': [19.2, 18.1, 17.0, 16.5, 15.8, 15.2]
        };
        
        series.push({
          name: filters.scenario,
          type: 'line',
          data: scenarioData[filters.scenario] || [],
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3
          }
        });
      } else {
        // 显示所有场景数据
        series.push(
          {
            name: '外客在线智能',
            type: 'line',
            data: [24.5, 25.1, 24.8, 23.9, 23.1, 23.5],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '骑手在线智能',
            type: 'line',
            data: [21.3, 20.5, 19.8, 19.2, 18.9, 18.7],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '拼好饭在线智能',
            type: 'line',
            data: [23.5, 24.2, 25.1, 25.5, 25.9, 25.8],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '拼好饭在线虚拟客服',
            type: 'line',
            data: [25.8, 26.5, 27.1, 27.8, 28.2, 28.4],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '外商在线智能',
            type: 'line',
            data: [22.1, 21.8, 21.3, 20.8, 20.5, 20.3],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '外客直出',
            type: 'line',
            data: [19.2, 18.1, 17.0, 16.5, 15.8, 15.2],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          }
        );
      }
      
      return {
        title: {
          text: '转人工率趋势',
          left: 'center',
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: filters.scenario !== 'all' ? [filters.scenario] : ['外客在线智能', '骑手在线智能', '拼好饭在线智能', '拼好饭在线虚拟客服', '外商在线智能', '外客直出'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: timeRange
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        series: series
      };
    };
    
    // 获取解决率趋势图表的options
    const getResolutionRateOptions = () => {
      const timeRange = ['1月', '2月', '3月', '4月', '5月', '6月'];
      
      // 如果选择了特定场景，只显示该场景的数据
      const series = [];
      if (filters.scenario !== 'all') {
        const scenarioData = {
          '外客在线智能': [75.2, 75.8, 76.4, 77.1, 77.8, 78.2],
          '骑手在线智能': [79.1, 79.8, 80.5, 81.2, 81.8, 82.1],
          '拼好饭在线智能': [77.2, 76.8, 76.5, 76.0, 75.6, 75.3],
          '拼好饭在线虚拟客服': [74.5, 74.0, 73.5, 73.0, 72.5, 72.1],
          '外商在线智能': [78.1, 78.6, 79.2, 79.8, 80.2, 80.5],
          '外客直出': [81.2, 82.3, 83.5, 84.2, 85.0, 85.7]
        };
        
        series.push({
          name: filters.scenario,
          type: 'line',
          data: scenarioData[filters.scenario] || [],
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3
          }
        });
      } else {
        // 显示所有场景数据
        series.push(
          {
            name: '外客在线智能',
            type: 'line',
            data: [75.2, 75.8, 76.4, 77.1, 77.8, 78.2],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '骑手在线智能',
            type: 'line',
            data: [79.1, 79.8, 80.5, 81.2, 81.8, 82.1],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '拼好饭在线智能',
            type: 'line',
            data: [77.2, 76.8, 76.5, 76.0, 75.6, 75.3],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '拼好饭在线虚拟客服',
            type: 'line',
            data: [74.5, 74.0, 73.5, 73.0, 72.5, 72.1],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '外商在线智能',
            type: 'line',
            data: [78.1, 78.6, 79.2, 79.8, 80.2, 80.5],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '外客直出',
            type: 'line',
            data: [81.2, 82.3, 83.5, 84.2, 85.0, 85.7],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          }
        );
      }
      
      return {
        title: {
          text: '解决率趋势',
          left: 'center',
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: filters.scenario !== 'all' ? [filters.scenario] : ['外客在线智能', '骑手在线智能', '拼好饭在线智能', '拼好饭在线虚拟客服', '外商在线智能', '外客直出'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: timeRange
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        series: series
      };
    };
    
    // 获取退款率趋势图表的options
    const getRefundRateOptions = () => {
      const timeRange = ['1月', '2月', '3月', '4月', '5月', '6月'];
      
      // 如果选择了特定场景，只显示该场景的数据
      const series = [];
      if (filters.scenario !== 'all') {
        const scenarioData = {
          '外客在线智能': [9.9, 9.7, 9.4, 9.1, 8.9, 8.7],
          '骑手在线智能': [7.4, 7.2, 7.0, 6.8, 6.6, 6.5],
          '拼好饭在线智能': [8.5, 8.7, 8.9, 9.0, 9.1, 9.2],
          '拼好饭在线虚拟客服': [9.2, 9.5, 9.8, 10.1, 10.3, 10.5],
          '外商在线智能': [8.4, 8.2, 8.1, 8.0, 7.9, 7.8],
          '外客直出': [6.8, 6.4, 6.1, 5.8, 5.5, 5.3]
        };
        
        series.push({
          name: filters.scenario,
          type: 'line',
          data: scenarioData[filters.scenario] || [],
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3
          }
        });
      } else {
        // 显示所有场景数据
        series.push(
          {
            name: '外客在线智能',
            type: 'line',
            data: [9.9, 9.7, 9.4, 9.1, 8.9, 8.7],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '骑手在线智能',
            type: 'line',
            data: [7.4, 7.2, 7.0, 6.8, 6.6, 6.5],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '拼好饭在线智能',
            type: 'line',
            data: [8.5, 8.7, 8.9, 9.0, 9.1, 9.2],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '拼好饭在线虚拟客服',
            type: 'line',
            data: [9.2, 9.5, 9.8, 10.1, 10.3, 10.5],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '外商在线智能',
            type: 'line',
            data: [8.4, 8.2, 8.1, 8.0, 7.9, 7.8],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '外客直出',
            type: 'line',
            data: [6.8, 6.4, 6.1, 5.8, 5.5, 5.3],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          }
        );
      }
      
      return {
        title: {
          text: '退款率趋势',
          left: 'center',
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: filters.scenario !== 'all' ? [filters.scenario] : ['外客在线智能', '骑手在线智能', '拼好饭在线智能', '拼好饭在线虚拟客服', '外商在线智能', '外客直出'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: timeRange
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        series: series
      };
    };
    
    // 获取赔付率趋势图表的options
    const getCompensationRateOptions = () => {
      const timeRange = ['1月', '2月', '3月', '4月', '5月', '6月'];
      
      // 如果选择了特定场景，只显示该场景的数据
      const series = [];
      if (filters.scenario !== 'all') {
        const scenarioData = {
          '外客在线智能': [6.1, 6.0, 5.8, 5.6, 5.4, 5.3],
          '骑手在线智能': [5.2, 5.0, 4.8, 4.5, 4.3, 4.1],
          '拼好饭在线智能': [6.3, 6.4, 6.5, 6.6, 6.7, 6.8],
          '拼好饭在线虚拟客服': [6.3, 6.5, 6.8, 7.0, 7.1, 7.2],
          '外商在线智能': [5.3, 5.2, 5.1, 5.0, 4.9, 4.9],
          '外客直出': [4.4, 4.1, 3.8, 3.6, 3.4, 3.2]
        };
        
        series.push({
          name: filters.scenario,
          type: 'line',
          data: scenarioData[filters.scenario] || [],
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3
          }
        });
      } else {
        // 显示所有场景数据
        series.push(
          {
            name: '外客在线智能',
            type: 'line',
            data: [6.1, 6.0, 5.8, 5.6, 5.4, 5.3],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '骑手在线智能',
            type: 'line',
            data: [5.2, 5.0, 4.8, 4.5, 4.3, 4.1],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '拼好饭在线智能',
            type: 'line',
            data: [6.3, 6.4, 6.5, 6.6, 6.7, 6.8],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '拼好饭在线虚拟客服',
            type: 'line',
            data: [6.3, 6.5, 6.8, 7.0, 7.1, 7.2],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '外商在线智能',
            type: 'line',
            data: [5.3, 5.2, 5.1, 5.0, 4.9, 4.9],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          },
          {
            name: '外客直出',
            type: 'line',
            data: [4.4, 4.1, 3.8, 3.6, 3.4, 3.2],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2
            }
          }
        );
      }
      
      return {
        title: {
          text: '赔付率趋势',
          left: 'center',
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: filters.scenario !== 'all' ? [filters.scenario] : ['外客在线智能', '骑手在线智能', '拼好饭在线智能', '拼好饭在线虚拟客服', '外商在线智能', '外客直出'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: timeRange
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        series: series
      };
    };
    
    // 初始化图表
    const initChart = (type) => {
      const chartRef = {
        transferRate: transferRateChart,
        resolutionRate: resolutionRateChart,
        refundRate: refundRateChart,
        compensationRate: compensationRateChart
      }[type];

      if (!chartRef.value) {
        console.warn(`Chart element ${type}Chart not found`);
        return;
      }

      // 确保容器有正确的尺寸
      chartRef.value.style.width = '100%';
      chartRef.value.style.height = '400px';

      // 如果已经有实例，先销毁
      if (charts[type]) {
        charts[type].dispose();
      }

      // 初始化图表
      try {
        charts[type] = echarts.init(chartRef.value);
        let options;

        // 根据类型设置选项
        switch (type) {
          case 'transferRate':
            options = getTransferRateOptions();
            break;
          case 'resolutionRate':
            options = getResolutionRateOptions();
            break;
          case 'refundRate':
            options = getRefundRateOptions();
            break;
          case 'compensationRate':
            options = getCompensationRateOptions();
            break;
        }

        if (options) {
          charts[type].setOption(options);
          
          // 添加点击事件，实现下钻功能
          if (type === 'transferRate' || type === 'resolutionRate') {
            // 先移除之前可能存在的事件监听，避免重复注册
            charts[type].off('click');
            
            // 重新注册点击事件
            charts[type].on('click', (params) => {
              handleChartClick(type, params);
            });
          }
        }
      } catch (error) {
        console.error(`Error initializing ${type} chart:`, error);
      }
    };
    
    // 图表点击处理，实现下钻功能
    const handleChartClick = (type, params) => {
      // 确保获取到了有效的点击参数
      if (!params || !params.seriesName || !params.name) {
        console.error('Invalid chart click parameters:', params);
        return;
      }
      
      // 构建跳转参数
      const queryParams = {
        scenario: params.seriesName,
        month: params.name,
        metricType: type
      };
      
      console.log('图表点击，下钻到详情:', queryParams);
      
      // 跳转到用户查询页面
      navigateToUserQueries(queryParams);
    };
    
    // 跳转到用户查询页面
    const navigateToUserQueries = (params) => {
      // 构建跳转URL
      const title = params.metricType === 'transferRate' ? '转人工' : '未解决';
      const redirectMessage = `正在跳转到会话分析 > 会话查询页面，查看${params.scenario}在${params.month}的${title}案例详情...`;
      
      // 先清除可能已存在的消息框
      const existingMessage = document.querySelector('.drill-down-message');
      if (existingMessage) {
        document.body.removeChild(existingMessage);
      }
      
      // 创建一个临时消息框
      const message = document.createElement('div');
      message.className = 'drill-down-message';
      message.innerHTML = `
        <div class="message-content">
          <div class="message-title">下钻到详情</div>
          <div class="message-text">${redirectMessage}</div>
          <div class="message-loading">
            <span class="loading-dot"></span>
            <span class="loading-dot"></span>
            <span class="loading-dot"></span>
          </div>
        </div>
      `;
      document.body.appendChild(message);
      
      // 模拟用户查询页面地址
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');
      const targetUrl = `/effect-analysis/user-behavior/user-queries?${queryString}`;
      
      // 3秒后移除消息并跳转
      setTimeout(() => {
        // 清除消息框
        document.body.removeChild(message);
        
        // 实际项目中使用路由跳转
        // router.push({
        //   path: '/effect-analysis/user-behavior/user-queries',
        //   query: params
        // });
        
        console.log('需要导航到:', targetUrl);
        
        // 由于这是个示例，我们使用window.location模拟跳转
        // 这里仅显示一个新的消息框来模拟跳转效果
        alert(`跳转到会话查询页面：${targetUrl}\n\n在实际项目中这里会使用Vue Router进行实际页面跳转`);
      }, 2000);
    };
    
    // 处理标签页变更
    const handleTabChange = (key) => {
      console.log('Tab changed to:', key);
      // 延迟一下，确保DOM已经渲染
      setTimeout(() => {
        initChart(key);
      }, 0);
    };
    
    // 获取趋势图标样式
    const getTrendClass = (card) => {
      if (card.trend === 'up') {
        return card.isNegative ? 'negative' : 'positive';
      } else {
        return card.isNegative ? 'positive' : 'negative';
      }
    };
    
    // 获取趋势图标
    const getTrendIcon = (card) => {
      return card.trend === 'up' ? 'ArrowUpOutlined' : 'ArrowDownOutlined';
    };
    
    // 获取场景颜色
    const getScenarioColor = (scenario) => {
      const colorMap = {
        '外客在线智能': 'blue',
        '骑手在线智能': 'green',
        '拼好饭在线智能': 'orange',
        '拼好饭在线虚拟客服': 'purple',
        '外商在线智能': 'cyan',
        '外客直出': 'magenta'
      };
      return colorMap[scenario] || 'blue';
    };
    
    // 场景变更处理
    const handleScenarioChange = (value) => {
      console.log('场景变更:', value);
      filters.scenario = value;
      
      // 重新初始化当前活跃的图表
      nextTick(() => {
        initChart(activeChartTab.value);
      });
    };
    
    // 日期变更处理
    const handleDateChange = (dates) => {
      console.log('日期变更:', dates);
      // 这里应该重新获取数据
    };
    
    // 搜索处理
    const handleSearch = () => {
      console.log('搜索按钮被点击');
      console.log('当前筛选条件:', filters);
      
      // 如果有描述文本且没有分析过，先进行分析
      if (filters.description.trim() && !filters.isAnalyzed) {
        console.log('开始分析描述文本:', filters.description);
        handleDescriptionAnalysis();
        return;
      }
      
      // 更新指标卡片数据
      updateMetricCards();
      
      // 更新图表数据
      updateCharts();
      
      // 即使没有通过描述分析，也展示当前筛选条件
      if (!filters.structuredQuery) {
        // 创建结构化查询条件以展示当前筛选项
        filters.structuredQuery = {};
        
        // 添加时间范围信息
        if (filters.dateRange && filters.dateRange.length === 2) {
          const formatDate = (date) => {
            if (dayjs.isDayjs(date)) {
              return date.format('YYYY-MM-DD');
            }
            return dayjs(date).format('YYYY-MM-DD');
          };
          
          filters.structuredQuery.dateRange = filters.dateRange;
          filters.structuredQuery.dateRangeText = `${formatDate(filters.dateRange[0])}至${formatDate(filters.dateRange[1])}`;
        }
        
        // 添加场景信息
        if (filters.scenario && filters.scenario !== 'all') {
          filters.structuredQuery.scenario = filters.scenario;
        }
        
        // 添加模型执行方案信息
        if (filters.modelExecutionPlan) {
          filters.structuredQuery.modelExecutionPlan = filters.modelExecutionPlan;
        }
        
        // 添加当前选中的指标类型
        filters.structuredQuery.metricType = activeChartTab.value;
        
        // 标记为已分析，确保显示结构化查询条件
        filters.isAnalyzed = true;
      }
      
      // 如果有选定的指标类型，自动切换到对应的图表
      if (filters.structuredQuery && filters.structuredQuery.metricType) {
        activeChartTab.value = filters.structuredQuery.metricType;
        nextTick(() => {
          initChart(activeChartTab.value);
        });
      }
    };
    
    // 分析指标描述并提取结构化条件
    const handleDescriptionAnalysis = () => {
      console.log('开始分析描述文本');
      if (!filters.description.trim()) {
        console.log('描述文本为空');
        return;
      }
      
      try {
        // 模拟大模型分析处理
        const result = analyzeDescription(filters.description);
        console.log('分析结果:', result);
        
        // 更新筛选条件
        if (result.dateRange) {
          filters.dateRange = result.dateRange;
        }
        
        if (result.scenario) {
          filters.scenario = result.scenario;
        }
        
        if (result.modelExecutionPlan) {
          filters.modelExecutionPlan = result.modelExecutionPlan;
        }
        
        // 设置结构化查询结果
        filters.structuredQuery = {
          ...result,
          dateRangeText: result.dateRangeText || ''
        };
        filters.isAnalyzed = true;
        
        // 分析完成后自动应用筛选条件
        handleSearch();
      } catch (error) {
        console.error('分析过程出错:', error);
        // 显示错误提示
        message.error('分析描述文本时出错，请重试');
      }
    };
    
    // 模拟大模型分析描述文本
    const analyzeDescription = (description) => {
      console.log('开始分析描述:', description);
      
      // 提取日期范围
      const dateRangeMatch = description.match(/(\d+)[-到](\d+)\s*号/);
      let dateRange = null;
      let dateRangeText = '';
      
      if (dateRangeMatch) {
        const startDay = parseInt(dateRangeMatch[1]);
        const endDay = parseInt(dateRangeMatch[2]);
        const currentYear = new Date().getFullYear();
        const currentMonth = new Date().getMonth() + 1;
        
        dateRange = [
          new Date(currentYear, currentMonth - 1, startDay),
          new Date(currentYear, currentMonth - 1, endDay)
        ];
        dateRangeText = `${currentMonth}月${startDay}日-${currentMonth}月${endDay}日`;
      }
      
      // 提取场景
      let scenario = 'all';
      if (/外客在线智能|外客在线|外客智能/i.test(description)) {
        scenario = '外客在线智能';
      } else if (/骑手在线智能|骑手在线|骑手智能/i.test(description)) {
        scenario = '骑手在线智能';
      } else if (/拼好饭在线智能|拼好饭智能/i.test(description)) {
        scenario = '拼好饭在线智能';
      } else if (/拼好饭(在线)?虚拟客服/i.test(description)) {
        scenario = '拼好饭在线虚拟客服';
      } else if (/外商在线智能|外商在线|外商智能/i.test(description)) {
        scenario = '外商在线智能';
      } else if (/外客直出/i.test(description)) {
        scenario = '外客直出';
      } else if (/外客履约/i.test(description)) {
        scenario = '外客在线智能'; // 外客履约属于外客在线智能的子场景
      }
      
      // 提取模型执行方案
      let modelExecutionPlan = '';
      if (/包含退款|有退款/i.test(description)) {
        modelExecutionPlan = '包含退款';
      } else if (/不包含退款|无退款/i.test(description)) {
        modelExecutionPlan = '不包含退款';
      } else if (/包含赔付|有赔付/i.test(description)) {
        modelExecutionPlan = '包含赔付';
      } else if (/需要转人工/i.test(description)) {
        modelExecutionPlan = '需要转人工';
      }
      
      // 提取指标类型
      let metricType = '';
      if (/转人工率/i.test(description)) {
        metricType = 'transferRate';
      } else if (/解决率/i.test(description)) {
        metricType = 'resolutionRate';
      } else if (/退款率/i.test(description)) {
        metricType = 'refundRate';
      } else if (/赔付率/i.test(description)) {
        metricType = 'compensationRate';
      }
      
      console.log('分析结果:', {
        dateRange,
        dateRangeText,
        scenario,
        modelExecutionPlan,
        metricType
      });
      
      return {
        dateRange,
        dateRangeText,
        scenario,
        modelExecutionPlan,
        metricType
      };
    };
    
    // 重置筛选条件
    const resetFilters = () => {
      console.log('重置筛选条件');
      
      if (searchMode.value === 'intelligent') {
        // 重置智能分析模式
        smartSearch.value = '';
        structuredQuery.value = null;
      } else {
        // 重置精准查询模式
        resetPreciseFilters();
      }
      
      // 重置所有数据到初始状态
      resetToInitialData();
    };
    
    // 处理窗口大小变化
    const handleResize = () => {
      for (const type in charts) {
        if (charts[type]) {
          charts[type].resize();
        }
      }
    };
    
    // 组件挂载时初始化图表和添加窗口大小变化监听
    onMounted(() => {
      console.log('Component mounted');
      // 初始第一个图表
      nextTick(() => {
        initChart(activeChartTab.value);
      });
      
      // 监听窗口大小变化
      window.addEventListener('resize', handleResize);
    });
    
    // 组件卸载时清除图表实例和窗口大小监听
    onUnmounted(() => {
      // 确保在组件卸载前清理所有资源
      try {
        // 清理所有图表实例
        if (charts) {
          Object.keys(charts).forEach(type => {
            if (charts[type] && typeof charts[type].dispose === 'function') {
              try {
                charts[type].dispose();
              } catch (error) {
                console.error(`Error disposing ${type} chart:`, error);
              }
              charts[type] = null;
            }
          });
        }
        
        // 移除窗口大小变化监听器
        window.removeEventListener('resize', handleResize);
        
        // 清理可能存在的消息框
        const existingMessage = document.querySelector('.drill-down-message');
        if (existingMessage && existingMessage.parentNode) {
          existingMessage.parentNode.removeChild(existingMessage);
        }
      } catch (error) {
        console.error('Error during component cleanup:', error);
      }
    });
    
    // 生成场景选项
    const getScenarioOptions = (path = []) => {
      let current = scenarioHierarchy;
      for (const p of path) {
        if (current[p] && current[p].children) {
          current = current[p].children;
        }
      }
      
      return Object.keys(current).map(key => ({
        value: key,
        label: current[key].name
      }));
    };

    // 显示子场景数据
    const showSubScenarios = (parentScenario, subScenario = null) => {
      let subScenarios;
      if (subScenario) {
        // 如果是第三层，显示标问数据
        subScenarios = scenarioHierarchy[parentScenario].children[subScenario].children;
      } else {
        // 如果是第二层，显示子场景数据
        subScenarios = scenarioHierarchy[parentScenario].children;
      }

      // 生成子场景数据
      const subScenarioData = Object.keys(subScenarios).map(key => {
        const scenarioName = subScenarios[key].name;
        // 根据查询条件生成数据
        const baseData = {
          key: key,
          scenario: scenarioName,
          transferRate: Math.floor(Math.random() * 10) + 15,
          transferRateTrend: Math.random() > 0.5 ? 'up' : 'down',
          transferRateChange: (Math.random() * 2).toFixed(1),
          resolutionRate: Math.floor(Math.random() * 10) + 70,
          resolutionRateTrend: Math.random() > 0.5 ? 'up' : 'down',
          resolutionRateChange: (Math.random() * 2).toFixed(1),
          refundRate: Math.floor(Math.random() * 5) + 5,
          refundRateTrend: Math.random() > 0.5 ? 'up' : 'down',
          refundRateChange: (Math.random() * 1).toFixed(1),
          compensationRate: Math.floor(Math.random() * 3) + 3,
          compensationRateTrend: Math.random() > 0.5 ? 'up' : 'down',
          compensationRateChange: (Math.random() * 1).toFixed(1),
          satisfaction: (Math.random() * 1 + 3.5).toFixed(1),
          dailyConversations: Math.floor(Math.random() * 5000) + 5000
        };

        // 根据查询条件调整数据
        if (filters.dateRange) {
          const daysDiff = dayjs(filters.dateRange[1]).diff(dayjs(filters.dateRange[0]), 'day');
          if (daysDiff > 0) {
            baseData.transferRate = Math.max(10, baseData.transferRate - (daysDiff * 0.2));
            baseData.resolutionRate = Math.min(95, baseData.resolutionRate + (daysDiff * 0.3));
            baseData.refundRate = Math.max(2, baseData.refundRate - (daysDiff * 0.1));
            baseData.compensationRate = Math.max(1, baseData.compensationRate - (daysDiff * 0.05));
          }
        }

        if (filters.modelExecutionPlan) {
          switch (filters.modelExecutionPlan) {
            case '包含退款':
              baseData.refundRate = Math.min(15, baseData.refundRate * 1.2);
              baseData.transferRate = Math.min(30, baseData.transferRate * 1.1);
              break;
            case '包含赔付':
              baseData.compensationRate = Math.min(10, baseData.compensationRate * 1.3);
              baseData.transferRate = Math.min(35, baseData.transferRate * 1.15);
              break;
            case '需要转人工':
              baseData.transferRate = Math.min(40, baseData.transferRate * 1.2);
              baseData.resolutionRate = Math.max(60, baseData.resolutionRate * 0.9);
              break;
          }
        }

        // 根据层级调整数据
        if (subScenario) {
          // 如果是第三层（标问层），数据波动更大
          baseData.transferRate = Math.floor(Math.random() * 15) + 20;
          baseData.resolutionRate = Math.floor(Math.random() * 15) + 65;
          baseData.refundRate = Math.floor(Math.random() * 8) + 3;
          baseData.compensationRate = Math.floor(Math.random() * 5) + 2;
        } else {
          // 如果是第二层，数据相对稳定
          baseData.transferRate = Math.floor(Math.random() * 8) + 18;
          baseData.resolutionRate = Math.floor(Math.random() * 10) + 70;
          baseData.refundRate = Math.floor(Math.random() * 5) + 4;
          baseData.compensationRate = Math.floor(Math.random() * 3) + 3;
        }

        return baseData;
      });

      // 获取父层数据
      const parentData = tableData.value.find(item => item.scenario === parentScenario);
      
      // 更新表格数据，保留父层数据
      if (parentData) {
        tableData.value = [parentData, ...subScenarioData];
      } else {
        tableData.value = subScenarioData;
      }

      // 更新指标卡片数据
      updateMetricCardsForScenarios(tableData.value);
    };

    // 根据场景数据更新指标卡片
    const updateMetricCardsForScenarios = (scenarios) => {
      const metrics = {
        transferRate: { sum: 0, count: 0 },
        resolutionRate: { sum: 0, count: 0 },
        refundRate: { sum: 0, count: 0 },
        compensationRate: { sum: 0, count: 0 }
      };

      scenarios.forEach(scenario => {
        metrics.transferRate.sum += scenario.transferRate;
        metrics.transferRate.count++;
        metrics.resolutionRate.sum += scenario.resolutionRate;
        metrics.resolutionRate.count++;
        metrics.refundRate.sum += scenario.refundRate;
        metrics.refundRate.count++;
        metrics.compensationRate.sum += scenario.compensationRate;
        metrics.compensationRate.count++;
      });

      // 更新指标卡片数据
      updateMetricCards({
        transferRate: metrics.transferRate.sum / metrics.transferRate.count,
        resolutionRate: metrics.resolutionRate.sum / metrics.resolutionRate.count,
        refundRate: metrics.refundRate.sum / metrics.refundRate.count,
        compensationRate: metrics.compensationRate.sum / metrics.compensationRate.count
      });
    };

    // 更新指标卡片
    const updateMetricCards = () => {
      // 根据筛选条件生成新的指标数据
      const baseData = {
        transferRate: Math.floor(Math.random() * 10) + 15,
        resolutionRate: Math.floor(Math.random() * 10) + 70,
        refundRate: Math.floor(Math.random() * 5) + 5,
        compensationRate: Math.floor(Math.random() * 3) + 3
      };

      // 根据场景调整数据
      if (filters.scenario && filters.scenario !== 'all') {
        const scenarioAdjustments = {
          '外客在线智能': { transferRate: 2, resolutionRate: -3, refundRate: 1, compensationRate: 0.5 },
          '骑手在线智能': { transferRate: -1, resolutionRate: 2, refundRate: -0.5, compensationRate: -0.5 },
          '拼好饭在线智能': { transferRate: 1, resolutionRate: -2, refundRate: 0.5, compensationRate: 0.3 },
          '拼好饭在线虚拟客服': { transferRate: 2, resolutionRate: -4, refundRate: 1, compensationRate: 0.8 },
          '外商在线智能': { transferRate: -0.5, resolutionRate: 1, refundRate: -0.3, compensationRate: -0.2 },
          '外客直出': { transferRate: -2, resolutionRate: 3, refundRate: -1, compensationRate: -0.8 }
        };

        const adjustment = scenarioAdjustments[filters.scenario] || {};
        baseData.transferRate += adjustment.transferRate || 0;
        baseData.resolutionRate += adjustment.resolutionRate || 0;
        baseData.refundRate += adjustment.refundRate || 0;
        baseData.compensationRate += adjustment.compensationRate || 0;
      }

      // 根据执行动作调整数据
      if (filters.modelExecutionPlan) {
        switch (filters.modelExecutionPlan) {
          case '包含退款':
            baseData.refundRate = Math.min(15, baseData.refundRate * 1.2);
            baseData.transferRate = Math.min(30, baseData.transferRate * 1.1);
            break;
          case '包含赔付':
            baseData.compensationRate = Math.min(10, baseData.compensationRate * 1.3);
            baseData.transferRate = Math.min(35, baseData.transferRate * 1.15);
            break;
          case '需要转人工':
            baseData.transferRate = Math.min(40, baseData.transferRate * 1.2);
            baseData.resolutionRate = Math.max(60, baseData.resolutionRate * 0.9);
            break;
        }
      }

      // 更新指标卡片数据
      metricCards.value = [
        {
          title: '转人工率',
          value: `${baseData.transferRate.toFixed(1)}%`,
          change: `${(Math.random() * 2).toFixed(1)}%`,
          trend: Math.random() > 0.5 ? 'up' : 'down',
          isNegative: true,
          description: '智能客服无法解决转由人工客服处理的比率'
        },
        {
          title: '解决率',
          value: `${baseData.resolutionRate.toFixed(1)}%`,
          change: `${(Math.random() * 2).toFixed(1)}%`,
          trend: Math.random() > 0.5 ? 'up' : 'down',
          isNegative: false,
          description: '智能客服成功解决客户问题的比率'
        },
        {
          title: '退款率',
          value: `${baseData.refundRate.toFixed(1)}%`,
          change: `${(Math.random() * 1).toFixed(1)}%`,
          trend: Math.random() > 0.5 ? 'up' : 'down',
          isNegative: true,
          description: '客户要求退款的订单比率'
        },
        {
          title: '赔付率',
          value: `${baseData.compensationRate.toFixed(1)}%`,
          change: `${(Math.random() * 1).toFixed(1)}%`,
          trend: Math.random() > 0.5 ? 'up' : 'down',
          isNegative: true,
          description: '需要赔付的订单比率'
        }
      ];
    };

    // 更新图表数据
    const updateCharts = () => {
      // 获取当前选中的指标类型
      const currentMetric = activeChartTab.value;
      
      // 根据筛选条件生成新的图表数据
      const generateChartData = (baseValue) => {
        return Array.from({ length: 6 }, () => baseValue + (Math.random() * 4 - 2));
      };

      // 基础数据
      const baseData = {
        transferRate: Math.floor(Math.random() * 10) + 15,
        resolutionRate: Math.floor(Math.random() * 10) + 70,
        refundRate: Math.floor(Math.random() * 5) + 5,
        compensationRate: Math.floor(Math.random() * 3) + 3
      };

      // 根据场景调整数据
      if (filters.scenario && filters.scenario !== 'all') {
        const scenarioAdjustments = {
          '外客在线智能': { transferRate: 2, resolutionRate: -3, refundRate: 1, compensationRate: 0.5 },
          '骑手在线智能': { transferRate: -1, resolutionRate: 2, refundRate: -0.5, compensationRate: -0.5 },
          '拼好饭在线智能': { transferRate: 1, resolutionRate: -2, refundRate: 0.5, compensationRate: 0.3 },
          '拼好饭在线虚拟客服': { transferRate: 2, resolutionRate: -4, refundRate: 1, compensationRate: 0.8 },
          '外商在线智能': { transferRate: -0.5, resolutionRate: 1, refundRate: -0.3, compensationRate: -0.2 },
          '外客直出': { transferRate: -2, resolutionRate: 3, refundRate: -1, compensationRate: -0.8 }
        };

        const adjustment = scenarioAdjustments[filters.scenario] || {};
        baseData.transferRate += adjustment.transferRate || 0;
        baseData.resolutionRate += adjustment.resolutionRate || 0;
        baseData.refundRate += adjustment.refundRate || 0;
        baseData.compensationRate += adjustment.compensationRate || 0;
      }

      // 根据执行动作调整数据
      if (filters.modelExecutionPlan) {
        switch (filters.modelExecutionPlan) {
          case '包含退款':
            baseData.refundRate = Math.min(15, baseData.refundRate * 1.2);
            baseData.transferRate = Math.min(30, baseData.transferRate * 1.1);
            break;
          case '包含赔付':
            baseData.compensationRate = Math.min(10, baseData.compensationRate * 1.3);
            baseData.transferRate = Math.min(35, baseData.transferRate * 1.15);
            break;
          case '需要转人工':
            baseData.transferRate = Math.min(40, baseData.transferRate * 1.2);
            baseData.resolutionRate = Math.max(60, baseData.resolutionRate * 0.9);
            break;
        }
      }

      // 更新当前图表的选项
      const chartOptions = {
        transferRate: {
          title: { text: '转人工率趋势' },
          series: [{ data: generateChartData(baseData.transferRate) }]
        },
        resolutionRate: {
          title: { text: '解决率趋势' },
          series: [{ data: generateChartData(baseData.resolutionRate) }]
        },
        refundRate: {
          title: { text: '退款率趋势' },
          series: [{ data: generateChartData(baseData.refundRate) }]
        },
        compensationRate: {
          title: { text: '赔付率趋势' },
          series: [{ data: generateChartData(baseData.compensationRate) }]
        }
      };

      // 更新当前图表
      if (charts[currentMetric]) {
        charts[currentMetric].setOption(chartOptions[currentMetric]);
      }
    };

    // 处理场景点击
    const handleScenarioClick = (record) => {
      const scenario = record.scenario;
      
      // 如果点击的是父层场景
      if (scenarioHierarchy[scenario]) {
        selectedScenarioPath.value = [scenario];
        showSubScenarios(scenario);
      } 
      // 如果点击的是子场景
      else if (selectedScenarioPath.value.length > 0) {
        const parentScenario = selectedScenarioPath.value[0];
        if (scenarioHierarchy[parentScenario]?.children[scenario]) {
          selectedScenarioPath.value = [parentScenario, scenario];
          showSubScenarios(parentScenario, scenario);
        }
      }
    };

    // 处理面包屑点击
    const handleBreadcrumbClick = (index) => {
      if (index === 0) {
        // 点击"全部场景"，重置到初始状态
        resetFilters();
        return;
      }
      
      // 截取到点击的位置
      selectedScenarioPath.value = selectedScenarioPath.value.slice(0, index + 1);
      
      // 显示对应层级的场景数据
      if (index === 0) {
        showSubScenarios(selectedScenarioPath.value[0]);
      } else {
        showSubScenarios(selectedScenarioPath.value[0], selectedScenarioPath.value[1]);
      }
    };

    // 检查场景是否有子场景
    const hasChildren = (scenario) => {
      if (scenarioHierarchy[scenario]) {
        return true;
      }
      const parentScenario = selectedScenarioPath.value[0];
      return scenarioHierarchy[parentScenario]?.children[scenario]?.children;
    };

    // 在 setup 函数中添加
    const pieCharts = reactive({
      waimaiOnline: null,
      waimaiPhone: null,
      pinhfOnline: null,
      riderOnline: null,
      riderPhone: null,
      merchantOnline: null
    });

    // 问题分类数据
    const issueCategories = {
      model: {
        name: '模型问题',
        children: {
          'model-repetition': '模型-话术重复',
          'model-process': '模型-未推进流程'
        }
      },
      engineering: {
        name: '工程问题',
        children: {
          'eng-task-timeout': '工程-task超时',
          'eng-llm-timeout': '工程-大模型超时'
        }
      },
      circuit: {
        name: '熔断问题',
        children: {
          'circuit-double-transfer': '用户两次转人工',
          'circuit-complaint': '用户投诉关键词'
        }
      },
      other: {
        name: '其他问题',
        children: {
          'other-signal-missing': '信号缺失',
          'other-signal-conflict': '信号矛盾',
          'other-process-mismatch': '业务流程不符'
        }
      }
    };

    // 初始化饼图
    const initPieCharts = () => {
      const chartRefs = {
        waimaiOnline: waimaiOnlinePieChart,
        waimaiPhone: waimaiPhonePieChart,
        pinhfOnline: pinhfOnlinePieChart,
        riderOnline: riderOnlinePieChart,
        riderPhone: riderPhonePieChart,
        merchantOnline: merchantOnlinePieChart
      };

      Object.entries(chartRefs).forEach(([key, ref]) => {
        if (!ref.value) {
          console.warn(`Pie chart element ${key} not found`);
          return;
        }

        // 确保容器有正确的尺寸
        ref.value.style.width = '100%';
        ref.value.style.height = '200px';

        // 初始化图表
        pieCharts[key] = echarts.init(ref.value);
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}%'
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center',
            type: 'scroll'
          },
          series: [
            {
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['40%', '50%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: false
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '14',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 35, name: '模型问题', itemStyle: { color: '#1890ff' } },
                { value: 25, name: '工程问题', itemStyle: { color: '#52c41a' } },
                { value: 20, name: '熔断问题', itemStyle: { color: '#faad14' } },
                { value: 20, name: '其他问题', itemStyle: { color: '#f5222d' } }
              ]
            }
          ]
        };

        pieCharts[key].setOption(option);
        
        // 添加点击事件
        pieCharts[key].on('click', (params) => {
          handlePieChartClick(key, params);
        });
      });
    };

    // 在 onMounted 中确保图表初始化
    onMounted(() => {
      // ... existing mounted code ...
      
      // 初始化饼图
      nextTick(() => {
        initPieCharts();
        
        // 添加窗口大小变化监听
        window.addEventListener('resize', () => {
          Object.values(pieCharts).forEach(chart => {
            if (chart) {
              chart.resize();
            }
          });
        });
      });
    });

    // 在 onUnmounted 中添加
    onUnmounted(() => {
      // ... existing code ...
      
      // 清理饼图实例
      Object.values(pieCharts).forEach(chart => {
        if (chart) {
          chart.dispose();
        }
      });
    });

    // 新增 Tab 相关状态
    const activeTab = ref('business-overview');

    // 问题归因相关数据
    const issueColumns = [
      {
        title: '问题类型',
        dataIndex: 'type',
        key: 'type',
      },
      {
        title: '影响范围',
        dataIndex: 'scope',
        key: 'scope',
      },
      {
        title: '问题数量',
        dataIndex: 'count',
        key: 'count',
        sorter: (a, b) => a.count - b.count,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
      },
      {
        title: '趋势',
        dataIndex: 'trend',
        key: 'trend',
      },
      {
        title: '解决方案',
        dataIndex: 'solution',
        key: 'solution',
      }
    ];

    const issueData = ref([
      {
        key: '1',
        type: '模型问题',
        scope: '外客在线',
        count: 156,
        status: '处理中',
        trend: '下降',
        solution: '优化模型响应逻辑'
      },
      {
        key: '2',
        type: '工程问题',
        scope: '骑手在线',
        count: 89,
        status: '已解决',
        trend: '稳定',
        solution: '修复系统超时问题'
      },
      {
        key: '3',
        type: '熔断问题',
        scope: '拼好饭在线',
        count: 45,
        status: '待处理',
        trend: '上升',
        solution: '调整熔断阈值'
      }
    ]);

    // 获取状态颜色
    const getStatusColor = (status) => {
      const colorMap = {
        '处理中': 'processing',
        '已解决': 'success',
        '待处理': 'warning'
      };
      return colorMap[status] || 'default';
    };

    // 初始化问题类型图表
    const initIssueTypeChart = () => {
      if (!issueTypeChart.value) {
        console.warn('Issue type chart DOM element not found');
        return;
      }

      // 确保容器有正确的尺寸
      issueTypeChart.value.style.width = '100%';
      issueTypeChart.value.style.height = '300px';

      const chart = echarts.init(issueTypeChart.value);
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          type: 'scroll'
        },
        series: [
          {
            name: '问题类型分布',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['40%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 156, name: '模型问题', itemStyle: { color: '#1890ff' } },
              { value: 89, name: '工程问题', itemStyle: { color: '#52c41a' } },
              { value: 45, name: '熔断问题', itemStyle: { color: '#faad14' } },
              { value: 78, name: '其他问题', itemStyle: { color: '#f5222d' } }
            ]
          }
        ]
      };
      chart.setOption(option);

      // 添加点击事件
      chart.on('click', (params) => {
        handleIssueTypeClick(params);
      });
    };

    // 初始化问题趋势图表
    const initIssueTrendChart = () => {
      if (!issueTrendChart.value) {
        console.warn('Issue trend chart DOM element not found');
        return;
      }

      // 确保容器有正确的尺寸
      issueTrendChart.value.style.width = '100%';
      issueTrendChart.value.style.height = '300px';

      const chart = echarts.init(issueTrendChart.value);
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['模型问题', '工程问题', '熔断问题', '其他问题']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '模型问题',
            type: 'line',
            data: [120, 132, 101, 134, 90, 230, 210]
          },
          {
            name: '工程问题',
            type: 'line',
            data: [220, 182, 191, 234, 290, 330, 310]
          },
          {
            name: '熔断问题',
            type: 'line',
            data: [150, 232, 201, 154, 190, 330, 410]
          },
          {
            name: '其他问题',
            type: 'line',
            data: [320, 332, 301, 334, 390, 330, 320]
          }
        ]
      };
      chart.setOption(option);
    };

    // 在 onMounted 中确保图表初始化
    onMounted(() => {
      // ... existing mounted code ...
      
      // 初始化问题分析图表
      nextTick(() => {
        initIssueTypeChart();
        initIssueTrendChart();
      });

      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        const issueTypeChartDom = document.getElementById('issueTypeChart');
        const issueTrendChartDom = document.getElementById('issueTrendChart');
        
        if (issueTypeChartDom) {
          const issueTypeChart = echarts.getInstanceByDom(issueTypeChartDom);
          if (issueTypeChart) {
            issueTypeChart.resize();
          }
        }
        
        if (issueTrendChartDom) {
          const issueTrendChart = echarts.getInstanceByDom(issueTrendChartDom);
          if (issueTrendChart) {
            issueTrendChart.resize();
          }
        }
      });
    });

    // 在 onUnmounted 中清理图表实例
    onUnmounted(() => {
      // ... existing unmounted code ...
      
      // 清理问题分析图表
      const issueTypeChartDom = document.getElementById('issueTypeChart');
      const issueTrendChartDom = document.getElementById('issueTrendChart');
      
      if (issueTypeChartDom) {
        const issueTypeChart = echarts.getInstanceByDom(issueTypeChartDom);
        if (issueTypeChart) {
          issueTypeChart.dispose();
        }
      }
      
      if (issueTrendChartDom) {
        const issueTrendChart = echarts.getInstanceByDom(issueTrendChartDom);
        if (issueTrendChart) {
          issueTrendChart.dispose();
        }
      }
    });

    // 处理饼图点击事件
    const handlePieChartClick = (businessLine, params) => {
      const issueType = params.name;
      const issueTypeData = issueCategories[getCategoryKey(issueType)];
      
      if (issueTypeData && issueTypeData.children) {
        // 显示子分类数据
        showIssueSubCategories(issueType, issueTypeData.children);
      }
    };

    // 获取问题类型键值
    const getCategoryKey = (name) => {
      const keyMap = {
        '模型问题': 'model',
        '工程问题': 'engineering',
        '熔断问题': 'circuit',
        '其他问题': 'other'
      };
      return keyMap[name] || 'other';
    };

    // 处理问题类型点击事件
    const handleIssueTypeClick = (params) => {
      const issueType = params.name;
      const issueTypeData = issueCategories[getCategoryKey(issueType)];
      
      if (issueTypeData && issueTypeData.children) {
        // 显示子分类数据
        showIssueSubCategories(issueType, issueTypeData.children);
      }
    };

    // 显示问题子分类数据
    const showIssueSubCategories = (parentType, children) => {
      const chartDom = document.getElementById('issueTypeChart');
      if (!chartDom) return;

      const chart = echarts.init(chartDom);
      const subData = Object.entries(children).map(([key, name]) => ({
        value: Math.floor(Math.random() * 30) + 10, // 模拟数据
        name: name,
        itemStyle: { color: getIssueTypeColor(key) }
      }));

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          type: 'scroll'
        },
        series: [
          {
            name: `${parentType}子分类`,
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['40%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: subData
          }
        ]
      };

      chart.clear();
      chart.setOption(option);
    };

    // 获取问题类型颜色
    const getIssueTypeColor = (type) => {
      const colorMap = {
        'model': '#1890ff',
        'engineering': '#52c41a',
        'circuit': '#faad14',
        'other': '#f5222d'
      };
      return colorMap[type] || '#1890ff';
    };

    // 添加refs
    const transferRateChart = ref(null);
    const resolutionRateChart = ref(null);
    const refundRateChart = ref(null);
    const compensationRateChart = ref(null);
    const waimaiOnlinePieChart = ref(null);
    const waimaiPhonePieChart = ref(null);
    const pinhfOnlinePieChart = ref(null);
    const riderOnlinePieChart = ref(null);
    const riderPhonePieChart = ref(null);
    const merchantOnlinePieChart = ref(null);
    const issueTypeChart = ref(null);
    const issueTrendChart = ref(null);
    const waimaiOnlineTrendChart = ref(null);
    const waimaiPhoneTrendChart = ref(null);
    const pinhfOnlineTrendChart = ref(null);
    const riderOnlineTrendChart = ref(null);
    const riderPhoneTrendChart = ref(null);
    const merchantOnlineTrendChart = ref(null);
    
    // 添加业务总览时间范围状态
    const overviewTimeRange = ref('7');
    
    // 业务总览图表实例
    const overviewCharts = reactive({
      waimaiOnline: { trend: null, pie: null },
      waimaiPhone: { trend: null, pie: null },
      pinhfOnline: { trend: null, pie: null },
      riderOnline: { trend: null, pie: null },
      riderPhone: { trend: null, pie: null },
      merchantOnline: { trend: null, pie: null },
      problemAnalysis: {
        pieData: [
          { value: 45, name: '模型问题' },
          { value: 30, name: '工程问题' },
          { value: 25, name: '熔断问题' }
        ],
        detailList: [
          { key: '1', type: '模型问题', time: '2024-03-20 10:00:00', scene: '骑手在线智能', impact: '高', count: 15 },
          { key: '2', type: '工程问题', time: '2024-03-20 09:30:00', scene: '骑手在线智能', impact: '中', count: 10 },
          { key: '3', type: '熔断问题', time: '2024-03-20 09:00:00', scene: '骑手在线智能', impact: '低', count: 8 },
          { key: '4', type: '模型问题', time: '2024-03-20 08:30:00', scene: '骑手在线智能', impact: '高', count: 12 },
          { key: '5', type: '工程问题', time: '2024-03-20 08:00:00', scene: '骑手在线智能', impact: '中', count: 9 },
          { key: '6', type: '熔断问题', time: '2024-03-20 07:30:00', scene: '骑手在线智能', impact: '低', count: 7 },
          { key: '7', type: '模型问题', time: '2024-03-20 07:00:00', scene: '骑手在线智能', impact: '高', count: 18 },
          { key: '8', type: '工程问题', time: '2024-03-20 06:30:00', scene: '骑手在线智能', impact: '中', count: 11 },
          { key: '9', type: '熔断问题', time: '2024-03-20 06:00:00', scene: '骑手在线智能', impact: '低', count: 10 },
          { key: '10', type: '模型问题', time: '2024-03-20 05:30:00', scene: '骑手在线智能', impact: '高', count: 14 }
        ]
      }
    });
    
    // 处理业务总览时间范围变更
    const handleOverviewTimeChange = (e) => {
      const days = parseInt(e.target.value);
      updateOverviewCharts(days);
    };
    
    // 更新业务总览图表
    const updateOverviewCharts = (days) => {
      const chartRefs = {
        waimaiOnline: { trend: waimaiOnlineTrendChart, pie: waimaiOnlinePieChart },
        waimaiPhone: { trend: waimaiPhoneTrendChart, pie: waimaiPhonePieChart },
        pinhfOnline: { trend: pinhfOnlineTrendChart, pie: pinhfOnlinePieChart },
        riderOnline: { trend: riderOnlineTrendChart, pie: riderOnlinePieChart },
        riderPhone: { trend: riderPhoneTrendChart, pie: riderPhonePieChart },
        merchantOnline: { trend: merchantOnlineTrendChart, pie: merchantOnlinePieChart }
      };
      
      // 生成日期数据
      const dates = Array.from({ length: days }, (_, i) => {
        const date = dayjs().subtract(days - 1 - i, 'day');
        return date.format('MM-DD');
      });
      
      // 为每个业务线更新图表
      Object.entries(chartRefs).forEach(([key, refs]) => {
        // 更新趋势图
        if (refs.trend.value) {
          // 确保容器有正确的尺寸
          refs.trend.value.style.width = '100%';
          refs.trend.value.style.height = '150px';
          
          // 如果已经有实例，先销毁
          if (overviewCharts[key].trend) {
            overviewCharts[key].trend.dispose();
          }
          
          // 初始化趋势图
          overviewCharts[key].trend = echarts.init(refs.trend.value);
          
          // 生成模拟数据
          const transferRateData = Array.from({ length: days }, () => 
            Math.floor(Math.random() * 10) + 15
          );
          const resolutionRateData = Array.from({ length: days }, () => 
            Math.floor(Math.random() * 10) + 70
          );
          
          const trendOption = {
            tooltip: {
              trigger: 'axis',
              formatter: function(params) {
                const date = params[0].axisValue;
                let result = `${date}<br/>`;
                params.forEach(param => {
                  result += `${param.seriesName}: ${param.value}%<br/>`;
                });
                return result;
              }
            },
            legend: {
              data: ['转人工率', '解决率'],
              bottom: 0,
              textStyle: {
                fontSize: 10
              }
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '15%',
              top: '5%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: dates,
              axisLabel: {
                interval: Math.floor(days / 7),
                fontSize: 10
              }
            },
            yAxis: {
              type: 'value',
              axisLabel: {
                formatter: '{value}%',
                fontSize: 10
              }
            },
            series: [
              {
                name: '转人工率',
                type: 'line',
                data: transferRateData,
                smooth: true,
                symbol: 'circle',
                symbolSize: 4,
                itemStyle: {
                  color: '#1890ff'
                }
              },
              {
                name: '解决率',
                type: 'line',
                data: resolutionRateData,
                smooth: true,
                symbol: 'circle',
                symbolSize: 4,
                itemStyle: {
                  color: '#52c41a'
                }
              }
            ]
          };
          
          overviewCharts[key].trend.setOption(trendOption);
        }
        
        // 更新饼图
        if (refs.pie.value) {
          // 确保容器有正确的尺寸
          refs.pie.value.style.width = '100%';
          refs.pie.value.style.height = '150px';
          
          // 如果已经有实例，先销毁
          if (overviewCharts[key].pie) {
            overviewCharts[key].pie.dispose();
          }
          
          // 初始化饼图
          overviewCharts[key].pie = echarts.init(refs.pie.value);
          
          // 生成模拟数据
          const pieData = [
            { value: Math.floor(Math.random() * 30) + 20, name: '模型问题', itemStyle: { color: '#1890ff' } },
            { value: Math.floor(Math.random() * 20) + 15, name: '工程问题', itemStyle: { color: '#52c41a' } },
            { value: Math.floor(Math.random() * 15) + 10, name: '熔断问题', itemStyle: { color: '#faad14' } },
            { value: Math.floor(Math.random() * 15) + 10, name: '其他问题', itemStyle: { color: '#f5222d' } }
          ];
          
          const pieOption = {
            tooltip: {
              trigger: 'item',
              formatter: '{b}: {c}%'
            },
            legend: {
              orient: 'vertical',
              right: 10,
              top: 'center',
              type: 'scroll',
              textStyle: {
                fontSize: 10
              }
            },
            series: [
              {
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['40%', '50%'],
                avoidLabelOverlap: false,
                itemStyle: {
                  borderRadius: 10,
                  borderColor: '#fff',
                  borderWidth: 2
                },
                label: {
                  show: false
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: 12,
                    fontWeight: 'bold'
                  }
                },
                labelLine: {
                  show: false
                },
                data: pieData
              }
            ]
          };
          
          overviewCharts[key].pie.setOption(pieOption);
        }
      });

      // 初始化问题归因分析饼图
      if (problemPieChart.value) {
        // 强制设置宽高
        problemPieChart.value.style.width = '100%';
        problemPieChart.value.style.height = '300px';
        // 先销毁旧实例
        echarts.dispose(problemPieChart.value);
        // 新建实例
        const chart = echarts.init(problemPieChart.value);

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center',
            type: 'scroll'
          },
          series: [
            {
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['40%', '50%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: false
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '14',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: problemAnalysisData.pieData
            }
          ]
        };
        chart.setOption(option);
      }
    };
    
    // 在组件挂载时初始化图表
    onMounted(() => {
      // ... existing mounted code ...
      
      // 初始化业务总览图表
      nextTick(() => {
        updateOverviewCharts(parseInt(overviewTimeRange.value));
      });
    });
    
    // 在组件卸载时清理图表实例
    onUnmounted(() => {
      // ... existing unmounted code ...
      
      // 清理业务总览图表实例
      Object.values(overviewCharts).forEach(charts => {
        if (charts.trend) charts.trend.dispose();
        if (charts.pie) charts.pie.dispose();
      });
    });
    
    // 智能分析相关数据
    const structuredQuery = ref(null);
    const smartSearch = ref('');

    // 处理搜索模式变更
    const handleSearchModeChange = (e) => {
      const mode = e.target.value;
      console.log('搜索模式变更:', mode);
      
      // 切换模式时重置相关状态
      if (mode === 'intelligent') {
        // 切换到智能分析模式，清空精准查询的筛选条件
        if (searchMode.value === 'precise') {
          resetPreciseFilters();
        }
      } else {
        // 切换到精准查询模式，清空智能搜索框
        smartSearch.value = '';
        structuredQuery.value = null;
      }
    };
    
    // 重置精准查询筛选条件
    const resetPreciseFilters = () => {
      filters.scenario = 'all';
      filters.dateRange = null;
      filters.metricType = 'transferRate';
      filters.modelExecutionPlan = '';
      filters.structuredQuery = null;
      filters.isAnalyzed = false;
    };
    
    // 解析自然语言查询
    const parseNaturalLanguageQuery = (query) => {
      // 这里是模拟的解析逻辑
      // 实际项目中应该使用更复杂的NLP处理
      const dateRegex = /(\d+)-(\d+)号/;
      const dateMatch = query.match(dateRegex);
      
      const parsed = {
        dateRange: dateMatch ? `2025-05-${dateMatch[1]}~2025-05-${dateMatch[2]}` : null,
        scenario: query.includes('外客') ? '外客在线智能' : null,
        action: query.includes('退款') ? '包含退款' : null,
        metric: query.includes('转人工率') ? '转人工率' : null
      };
      
      return parsed;
    };

    // 处理智能搜索清空
    const handleClearSmartSearch = () => {
      console.log('清空智能搜索');
      smartSearch.value = '';
      structuredQuery.value = null;
      
      // 回到初始数据状态
      resetToInitialData();
    };
    
    // 重置到初始数据状态
    const resetToInitialData = () => {
      console.log('重置到初始数据状态');
      
      // 重置筛选条件
      resetPreciseFilters();
      
      // 重新初始化指标卡片数据
      initMetricCards();
      
      // 更新图表数据
      nextTick(() => {
        initChart(activeChartTab.value);
      });
      
      // 如果是对比模式，也重置对比筛选条件
      if (isComparisonMode.value) {
        comparisonFilters.group1 = {
          scenario: 'all',
          orderStatus: 'all',
          dateRange: null
        };
        comparisonFilters.group2 = {
          scenario: 'all',
          orderStatus: 'all',
          dateRange: null
        };
      }
    };
    
    // 初始化指标卡片数据
    const initMetricCards = () => {
      metricCards.value = [
        {
          title: '转人工率',
          value: '21.9%',
          change: '1.7%',
          trend: 'down',
          isNegative: true,
          description: '智能客服无法解决转由人工客服处理的比率'
        },
        {
          title: '解决率',
          value: '79.1%',
          change: '2.2%',
          trend: 'up',
          isNegative: false,
          description: '智能客服成功解决客户问题的比率'
        },
        {
          title: '退款率',
          value: '8.0%',
          change: '1.2%',
          trend: 'down',
          isNegative: true,
          description: '客户要求退款的订单比率'
        },
        {
          title: '赔付率',
          value: '5.3%',
          change: '0.8%',
          trend: 'down',
          isNegative: true,
          description: '需要赔付的订单比率'
        }
      ];
    };

    // 处理智能搜索
    const handleSmartSearch = async (value) => {
      if (!value) {
        structuredQuery.value = null;
        resetToInitialData();
        return;
      }
      
      // 解析查询
      const parsedQuery = parseNaturalLanguageQuery(value);
      structuredQuery.value = parsedQuery;
      
      // 更新筛选条件
      if (parsedQuery.dateRange) {
        const [start, end] = parsedQuery.dateRange.split('~');
        filters.dateRange = [dayjs(start), dayjs(end)];
      }
      
      if (parsedQuery.scenario) {
        filters.scenario = parsedQuery.scenario;
      }
      
      if (parsedQuery.action) {
        filters.modelExecutionPlan = parsedQuery.action;
      }
      
      if (parsedQuery.metric) {
        switch (parsedQuery.metric) {
          case '转人工率':
            filters.metricType = 'transferRate';
            activeChartTab.value = 'transferRate';
            break;
          case '解决率':
            filters.metricType = 'resolutionRate';
            activeChartTab.value = 'resolutionRate';
            break;
          case '退款率':
            filters.metricType = 'refundRate';
            activeChartTab.value = 'refundRate';
            break;
          case '赔付率':
            filters.metricType = 'compensationRate';
            activeChartTab.value = 'compensationRate';
            break;
        }
      }
      
      // 更新图表数据
      await updateChartsWithQuery(parsedQuery);
      
      // 标记为已分析
      filters.isAnalyzed = true;
      
      // 构建结构化查询条件
      filters.structuredQuery = {
        dateRange: filters.dateRange,
        dateRangeText: parsedQuery.dateRange ? parsedQuery.dateRange.replace('~', '至') : '',
        scenario: filters.scenario,
        modelExecutionPlan: filters.modelExecutionPlan,
        metricType: filters.metricType
      };
      
      // 更新指标卡片数据
      updateMetricCards();
    };

    // 根据查询更新图表数据
    const updateChartsWithQuery = async (query) => {
      // 生成模拟数据
      const mockData = generateMockData(query);
      
      // 更新图表
      Object.keys(charts).forEach(chartKey => {
        if (charts[chartKey]) {
          chartOptions[chartKey].xAxis.data = mockData.dates;
          chartOptions[chartKey].series[0].data = mockData.values;
          charts[chartKey].setOption(chartOptions[chartKey]);
        }
      });
      
      // 更新表格数据
      tableData.value = mockData.tableData;
    };

    // 生成模拟数据
    const generateMockData = (query) => {
      const dates = [];
      const values = [];
      const tableData = [];
      
      if (query.dateRange) {
        const [start, end] = query.dateRange.split('~');
        const startDate = new Date(start);
        const endDate = new Date(end);
        
        for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
          dates.push(dayjs(d).format('MM-DD'));
          values.push(Math.random() * 30 + 10); // 模拟10-40之间的数据
        }
        
        // 生成表格数据
        tableData.push({
          key: '1',
          scenario: query.scenario || '外客在线智能',
          transferRate: (Math.random() * 30 + 10).toFixed(2),
          transferRateTrend: Math.random() > 0.5 ? 'up' : 'down',
          transferRateChange: (Math.random() * 5).toFixed(2),
          resolutionRate: (Math.random() * 20 + 70).toFixed(2),
          resolutionRateTrend: Math.random() > 0.5 ? 'up' : 'down',
          resolutionRateChange: (Math.random() * 5).toFixed(2),
          refundRate: (Math.random() * 8 + 5).toFixed(2),
          refundRateTrend: Math.random() > 0.5 ? 'up' : 'down',
          refundRateChange: (Math.random() * 3).toFixed(2),
          compensationRate: (Math.random() * 5 + 3).toFixed(2),
          compensationRateTrend: Math.random() > 0.5 ? 'up' : 'down',
          compensationRateChange: (Math.random() * 2).toFixed(2),
          satisfaction: (Math.random() * 1 + 3.5).toFixed(1),
          dailyConversations: Math.floor(Math.random() * 5000) + 5000
        });
      }
      
      return {
        dates,
        values,
        tableData
      };
    };
    
    // 设置搜索模式
    const setSearchMode = (mode) => {
      searchMode.value = mode;
      handleSearchModeChange({ target: { value: mode } });
    };
    
    // 获取趋势分析标题
    const getTrendAnalysisTitle = () => {
      let title = '全部数据';
      
      if (filters.scenario && filters.scenario !== 'all') {
        title = filters.scenario;
      }
      
      if (filters.dateRange && filters.dateRange.length === 2) {
        const startDate = dayjs(filters.dateRange[0]).format('MM-DD');
        const endDate = dayjs(filters.dateRange[1]).format('MM-DD');
        title += ` (${startDate}至${endDate})`;
      }
      
      if (filters.modelExecutionPlan) {
        title += ` - ${filters.modelExecutionPlan}`;
      }
      
      return title;
    };
    
    // 获取趋势分析图例
    const getTrendAnalysisLegend = () => {
      if (filters.scenario !== 'all') {
        return [
          { name: filters.scenario, color: getScenarioColor(filters.scenario) }
        ];
      }
      
      return [
        { name: '外客在线智能', color: '#1890ff' },
        { name: '骑手在线智能', color: '#52c41a' },
        { name: '拼好饭在线智能', color: '#faad14' },
        { name: '拼好饭在线虚拟客服', color: '#f5222d' },
        { name: '外商在线智能', color: '#722ed1' },
        { name: '外客直出', color: '#13c2c2' }
      ];
    };
    
    // 问题归因分析图表ref
    const waimaiOnlineProblemPie = ref(null);
    const waimaiOnlineProblemTrend = ref(null);

    // 初始化问题归因分析图表（业务总览用）
    const initProblemAnalysisCharts = () => {
      if (waimaiOnlineProblemPie.value) {
        const pieChart = echarts.init(waimaiOnlineProblemPie.value);
        pieChart.setOption({
          title: {
            text: '问题类型分布',
            textStyle: { fontSize: 12, fontWeight: 'normal' }
          },
          tooltip: { trigger: 'item', formatter: '{b}: {c} ({d}%)' },
          legend: {
            orient: 'vertical', right: 10, top: 'center', itemWidth: 8, itemHeight: 8, textStyle: { fontSize: 10 }
          },
          series: [{
            type: 'pie', radius: ['40%', '70%'], avoidLabelOverlap: false, label: { show: false },
            data: [
              { value: 335, name: '模型理解' },
              { value: 310, name: '业务规则' },
              { value: 234, name: '系统异常' },
              { value: 135, name: '其他' }
            ]
          }]
        });
      }
      if (waimaiOnlineProblemTrend.value) {
        const trendChart = echarts.init(waimaiOnlineProblemTrend.value);
        trendChart.setOption({
          title: { text: '问题趋势', textStyle: { fontSize: 12, fontWeight: 'normal' } },
          tooltip: { trigger: 'axis' },
          legend: { data: ['模型理解', '业务规则', '系统异常', '其他'], bottom: 0, textStyle: { fontSize: 10 } },
          grid: { top: '15%', left: '3%', right: '4%', bottom: '20%', containLabel: true },
          xAxis: { type: 'category', boundaryGap: false, data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'], axisLabel: { fontSize: 10 } },
          yAxis: { type: 'value', axisLabel: { fontSize: 10 } },
          series: [
            { name: '模型理解', type: 'line', data: [120, 132, 101, 134, 90, 230, 210] },
            { name: '业务规则', type: 'line', data: [220, 182, 191, 234, 290, 330, 310] },
            { name: '系统异常', type: 'line', data: [150, 232, 201, 154, 190, 330, 410] },
            { name: '其他', type: 'line', data: [320, 332, 301, 334, 390, 330, 320] }
          ]
        });
      }
    };

    // 初始化问题归因分析图表（效果分析tab用）
    const initEffectProblemAnalysisCharts = () => {
      if (problemPieChart.value) {
        const pieChart = echarts.init(problemPieChart.value);
        pieChart.setOption({
          title: { text: '问题类型分布', left: 'center' },
          tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' },
          legend: { orient: 'vertical', left: 'left' },
          series: [{
            name: '问题类型', type: 'pie', radius: '50%',
            data: [
              { value: 235, name: '模型理解错误' },
              { value: 274, name: '业务规则缺失' },
              { value: 310, name: '数据质量问题' },
              { value: 335, name: '系统异常' },
              { value: 400, name: '其他' },
            ],
            emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' } },
          }],
        });
      }
      if (problemTrendChart.value) {
        const trendChart = echarts.init(problemTrendChart.value);
        trendChart.setOption({
          title: { text: '问题趋势', left: 'center' },
          tooltip: { trigger: 'axis' },
          legend: { data: ['模型理解错误', '业务规则缺失', '数据质量问题', '系统异常', '其他'], bottom: 0 },
          xAxis: { type: 'category', data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'] },
          yAxis: { type: 'value' },
          series: [
            { name: '模型理解错误', type: 'line', data: [120, 132, 101, 134, 90, 230, 210] },
            { name: '业务规则缺失', type: 'line', data: [220, 182, 191, 234, 290, 330, 310] },
            { name: '数据质量问题', type: 'line', data: [150, 232, 201, 154, 190, 330, 410] },
            { name: '系统异常', type: 'line', data: [320, 332, 301, 334, 390, 330, 320] },
            { name: '其他', type: 'line', data: [820, 932, 901, 934, 1290, 1330, 1320] },
          ],
        });
      }
    };

    onMounted(() => {
      // ... existing code ...
      nextTick(() => {
        initProblemAnalysisCharts(); // 业务总览用
        initEffectProblemAnalysisCharts(); // 效果分析tab用
      });
    });

    // 显示问题详情
    const showProblemDetail = () => {
      // 实现问题详情弹窗逻辑
    };

    // 问题归因分析表格数据
    const problemColumns = [
      { title: '问题类型', dataIndex: 'type', key: 'type',
        customRender: ({ record }) => record.subType ? `${record.type}/${record.subType}` : record.type },
      { title: '发生时间', dataIndex: 'time', key: 'time' },
      { title: '场景', dataIndex: 'scene', key: 'scene' },
      { title: '影响范围', dataIndex: 'impact', key: 'impact' },
      { title: '问题数量', dataIndex: 'count', key: 'count' },
      { title: '操作', key: 'action', customRender: ({ record }) => h('a', {
        style: 'color:#1890ff;cursor:pointer;',
        onClick: () => gotoSessionQueryByProblem(record)
      }, '查看详情') },
    ];

    // 跳转到会话查询页，带上问题类型等参数
    function gotoSessionQueryByProblem(record) {
      const params = {
        problemType: record.type,
        subType: record.subType,
        scene: record.scene,
        impact: record.impact
      };
      const query = Object.entries(params)
        .filter(([, v]) => v)
        .map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v)}`)
        .join('&');
      window.location.href = `/effect-analysis/user-behavior/user-queries?${query}`;
    }

    // 问题归因分析图表ref（效果分析tab用）
    const problemPieChart = ref(null);
    const problemTrendChart = ref(null);

    // 问题归因分析数据
    const problemAnalysisData = reactive({
      pieData: [
        { value: 45, name: '模型问题' },
        { value: 30, name: '工程问题' },
        { value: 25, name: '熔断问题' },
        { value: 15, name: '其他' }
      ],
      detailList: [
        // 模型问题
        { key: '1', type: '模型问题', subType: '话术重复', time: '2024-03-20 10:00:00', scene: '骑手在线智能', impact: '高', count: 15 },
        { key: '2', type: '模型问题', subType: '幻觉', time: '2024-03-20 09:30:00', scene: '骑手在线智能', impact: '中', count: 10 },
        { key: '3', type: '模型问题', subType: '预测出不存在的动作', time: '2024-03-20 09:00:00', scene: '骑手在线智能', impact: '低', count: 8 },
        { key: '4', type: '模型问题', subType: '未推进流程', time: '2024-03-20 08:30:00', scene: '骑手在线智能', impact: '高', count: 12 },
        // 工程问题
        { key: '5', type: '工程问题', subType: 'task超时', time: '2024-03-20 08:00:00', scene: '骑手在线智能', impact: '中', count: 9 },
        { key: '6', type: '工程问题', subType: 'agent超时', time: '2024-03-20 07:30:00', scene: '骑手在线智能', impact: '低', count: 7 },
        // 熔断问题
        { key: '7', type: '熔断问题', subType: '命中用户消息熔断规则', time: '2024-03-20 07:00:00', scene: '骑手在线智能', impact: '高', count: 18 },
        { key: '8', type: '熔断问题', subType: '命中模型回复熔断规则', time: '2024-03-20 06:30:00', scene: '骑手在线智能', impact: '中', count: 11 },
        { key: '9', type: '熔断问题', subType: '用户连续转人工', time: '2024-03-20 06:00:00', scene: '骑手在线智能', impact: '低', count: 10 },
        // 其他
        { key: '10', type: '其他', subType: '直转人工', time: '2024-03-20 05:30:00', scene: '骑手在线智能', impact: '高', count: 14 },
        { key: '11', type: '其他', subType: '其他', time: '2024-03-20 05:00:00', scene: '骑手在线智能', impact: '中', count: 6 }
      ]
    });

    // 新增：问题类型二级分类数据
    const problemTypeMap = {
      '模型问题': [
        { value: 12, name: '话术重复' },
        { value: 10, name: '幻觉' },
        { value: 13, name: '预测出不存在的动作' },
        { value: 10, name: '未推进流程' }
      ],
      '工程问题': [
        { value: 15, name: 'task超时' },
        { value: 15, name: 'agent超时' }
      ],
      '熔断问题': [
        { value: 10, name: '命中用户消息熔断规则' },
        { value: 8, name: '命中模型回复熔断规则' },
        { value: 7, name: '用户连续转人工' }
      ],
      '其他': [
        { value: 8, name: '直转人工' },
        { value: 7, name: '其他' }
      ]
    };
    // 新增：当前饼图层级和下钻类型
    // const problemPieLevel = ref(1); // 1: 一级，2: 二级
    // const problemPieSubType = ref('');

    // 初始化所有图表
    const initCharts = () => {
      // ... existing code ...
      // 问题归因分析饼图（效果分析tab用）
      if (problemPieChart.value) {
        console.log('initCharts called', problemPieChart.value);
        problemPieChart.value.style.width = '100%';
        problemPieChart.value.style.height = '300px';
        echarts.dispose(problemPieChart.value); // 彻底销毁
        const chart = echarts.init(problemPieChart.value);

        let pieData, pieTitle, legendData;
        pieData = problemAnalysisData.pieData;
        pieTitle = '问题类型分布';
        legendData = problemAnalysisData.pieData.map(i => i.name);
        const option = {
          title: {
            text: pieTitle,
            left: 'center',
            top: 20,
            textStyle: { fontSize: 16, fontWeight: 'bold', color: '#1890ff' }
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center',
            type: 'scroll',
            data: legendData
          },
          series: [
            {
              name: '问题类型',
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['40%', '50%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: false
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '14',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: pieData
            }
          ]
        };
        chart.setOption(option);

        // 极端测试：只注册一次事件，alert弹窗+下钻逻辑
        chart.on('click', (params) => {
          selectedProblemType.value = params.name;
          selectedSubProblemType.value = '';
          nextTick(() => {
            initSubPieChart();
          });
        });
      }
      // 二级饼图
      if (problemSubPieChart.value && selectedProblemType.value) {
        problemSubPieChart.value.style.width = '100%';
        problemSubPieChart.value.style.height = '300px';
        echarts.dispose(problemSubPieChart.value);
        const chart = echarts.init(problemSubPieChart.value);
        const pieData = problemTypeMap[selectedProblemType.value] || [];
        const option = {
          title: { text: selectedProblemType.value + '子类型分布', left: 'center', top: 20, textStyle: { fontSize: 16, fontWeight: 'bold', color: '#1890ff' } },
          tooltip: { trigger: 'item', formatter: '{b}: {c} ({d}%)' },
          legend: { orient: 'vertical', right: 10, top: 'center', type: 'scroll', data: pieData.map(i => i.name) },
          series: [{ name: selectedProblemType.value + '子类型', type: 'pie', radius: ['40%', '70%'], center: ['40%', '50%'], avoidLabelOverlap: false, itemStyle: { borderRadius: 10, borderColor: '#fff', borderWidth: 2 }, label: { show: false }, emphasis: { label: { show: true, fontSize: '14', fontWeight: 'bold' } }, labelLine: { show: false }, data: pieData }]
        };
        chart.setOption(option);
        // 注册二级饼图点击事件
        chart.on('click', (params) => {
          selectedSubProblemType.value = params.name;
        });
      }
    };

    // 更新问题列表数据
    const problemTableComputedData = computed(() => {
      if (!selectedProblemType.value) {
        return problemAnalysisData.detailList;
      } else if (!selectedSubProblemType.value) {
        return problemAnalysisData.detailList.filter(item => item.type === selectedProblemType.value);
      } else {
        return problemAnalysisData.detailList.filter(item => item.type === selectedProblemType.value && item.subType === selectedSubProblemType.value);
      }
    });

    onMounted(() => {
      nextTick(() => {
        initCharts();
        window.addEventListener('resize', handleResize);
      });
    });

    // 监听tab切换，切到效果分析时重新初始化饼图
    watch(activeTab, (newTab) => {
      if (newTab === 'performance-analysis') {
        nextTick(() => {
          initCharts();
        });
      }
    });

    // 新增变量
    const selectedProblemType = ref('');
    const selectedSubProblemType = ref('');
    const problemSubPieChart = ref(null);

    // 新增：二级饼图渲染函数
    const initSubPieChart = () => {
      if (problemSubPieChart.value && selectedProblemType.value) {
        problemSubPieChart.value.style.width = '100%';
        problemSubPieChart.value.style.height = '300px';
        echarts.dispose(problemSubPieChart.value);
        const chart = echarts.init(problemSubPieChart.value);
        const pieData = problemTypeMap[selectedProblemType.value] || [];
        const option = {
          title: { text: selectedProblemType.value + '子类型分布', left: 'center', top: 20, textStyle: { fontSize: 16, fontWeight: 'bold', color: '#1890ff' } },
          tooltip: { trigger: 'item', formatter: '{b}: {c} ({d}%)' },
          legend: { orient: 'vertical', right: 10, top: 'center', type: 'scroll', data: pieData.map(i => i.name) },
          series: [{ name: selectedProblemType.value + '子类型', type: 'pie', radius: ['40%', '70%'], center: ['40%', '50%'], avoidLabelOverlap: false, itemStyle: { borderRadius: 10, borderColor: '#fff', borderWidth: 2 }, label: { show: false }, emphasis: { label: { show: true, fontSize: '14', fontWeight: 'bold' } }, labelLine: { show: false }, data: pieData }]
        };
        chart.setOption(option);
        // 注册二级饼图点击事件
        chart.on('click', (params) => {
          selectedSubProblemType.value = params.name;
        });
      }
    };

    // 新增实验组和模型选项
    const experimentGroups = {
      '外客在线智能': ['V1', 'V2', 'V3'],
      '外客履约': ['V2', 'V3', 'V4', 'V5', 'V6', 'V7', 'V8'],
      '骑手在线智能': ['A', 'B', 'C'],
      '拼好饭在线智能': ['X', 'Y', 'Z'],
      '外商在线智能': ['G1', 'G2'],
      default: ['V1', 'V2', 'V3']
    };
    const modelVersions = {
      '外客在线智能': ['sftV1', 'sftV2', 'sftV3'],
      '外客履约': ['sftV2', 'sftV3', 'sftV4', 'sftV5', 'sftV6', 'sftV7', 'sftV8'],
      '骑手在线智能': ['modelA', 'modelB'],
      '拼好饭在线智能': ['modelX', 'modelY'],
      '外商在线智能': ['modelG1', 'modelG2'],
      default: ['sftV1', 'sftV2', 'sftV3']
    };

    const factorOptions = [
      { value: 'orderStatus', label: '订单状态' },
      { value: 'hasIM', label: '是否有IM隐私号' },
      { value: 'predictRefund', label: '模型是否预测出退款' },
      { value: 'hasComment', label: '是否有评价' },
      { value: 'isResolved', label: '是否已解决' },
      { value: 'isTransfer', label: '是否转人工' },
      { value: 'deliveryStatus', label: '配送状态' },
      { value: 'deliveryType', label: '配送类型' }
    ];

    const factorStatsData = computed(() => {
      const factors = filters.selectedFactors;
      if (!factors.length) return [];
      const data = factorRawData.value || [];
      const groupMap = {};
      data.forEach(item => {
        const key = factors.map(f => `${factorOptions.find(opt => opt.value === f)?.label || f}:${item[f] ?? '无'}`).join('+');
        if (!groupMap[key]) groupMap[key] = 0;
        groupMap[key]++;
      });
      const total = data.length || 1;
      return Object.entries(groupMap).map(([combination, count]) => ({
        combination,
        count,
        percent: +(count / total * 100).toFixed(2)
      }));
    });

    const factorPieChart = ref(null);

    watch(factorStatsData, (newData) => {
      nextTick(() => {
        if (!factorPieChart.value) return;
        // 获取已有实例，避免重复dispose
        let chart = echarts.getInstanceByDom(factorPieChart.value);
        if (chart) {
          chart.dispose();
        }
        chart = echarts.init(factorPieChart.value);
        if (!newData || newData.length === 0) {
          // 没有数据时显示空图
          chart.clear();
          chart.setOption({
            title: { text: '暂无数据', left: 'center', top: 'center', textStyle: { fontSize: 18, color: '#ccc' } }
          });
          return;
        }
        const option = {
          title: {
            text: '组合占比分布',
            left: 'center',
            top: 20,
            textStyle: { fontSize: 16, fontWeight: 'bold', color: '#1890ff' }
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center',
            type: 'scroll',
            data: newData.map(i => i.combination)
          },
          series: [
            {
              name: '组合',
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['40%', '50%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: { show: false },
              emphasis: {
                label: { show: true, fontSize: '14', fontWeight: 'bold' }
              },
              labelLine: { show: false },
              data: newData.map(i => ({ value: i.count, name: i.combination }))
            }
          ]
        };
        chart.setOption(option);
      });
    }, { immediate: true });

    // 多因子组合分组统计表格列定义，增加"查看列表"按钮
    const factorStatsColumns = [
      { title: '组合', dataIndex: 'combination', key: 'combination' },
      { title: '数量', dataIndex: 'count', key: 'count' },
      { title: '占比', dataIndex: 'percent', key: 'percent', customRender: ({ text }) => text + '%' },
      { title: '操作', key: 'action', customRender: ({ record }) => h('a', { style: 'color:#1890ff;cursor:pointer;', onClick: () => showFactorDetail(record.combination) }, '查看列表') }
    ];

    // 弹窗相关
    const factorDetailVisible = ref(false);
    const factorDetailList = ref([]);
    const factorDetailTitle = ref('');
    function showFactorDetail(combination) {
      // 解析组合条件，拼接参数
      const conds = combination.split('+').map(s => s.split(':'));
      const params = {};
      conds.forEach(([label, val]) => {
        const opt = factorOptions.find(f => f.label === label);
        if (opt) params[opt.value] = val;
      });
      // 拼接query string
      const query = Object.entries(params).map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v)}`).join('&');
      // 跳转到会话查询页
      window.location.href = `/effect-analysis/user-behavior/user-queries?${query}`;
    }

    // 在setup顶部添加
    // const factorRawData = ref([]);
    function generateMockFactorData() {
      const scenarios = ['外客在线智能', '骑手在线智能', '拼好饭在线智能', '外商在线智能'];
      const orderStatusArr = ['已完成', '配送中', '待处理'];
      const hasIMArr = ['有', '无'];
      const predictRefundArr = ['是', '否'];
      const hasCommentArr = ['有', '无'];
      const isResolvedArr = ['是', '否'];
      const isTransferArr = ['是', '否'];
      const deliveryStatusArr = ['已送达', '配送中', '异常'];
      const deliveryTypeArr = ['普通', '加急'];
      const arr = [];
      // 生成20组全排列组合
      for (let i = 0; i < 20; i++) {
        arr.push({
          key: i + 1,
          scenario: scenarios[i % scenarios.length],
          orderStatus: orderStatusArr[i % orderStatusArr.length],
          hasIM: hasIMArr[i % hasIMArr.length],
          predictRefund: predictRefundArr[i % predictRefundArr.length],
          hasComment: hasCommentArr[(i + 1) % hasCommentArr.length],
          isResolved: isResolvedArr[(i + 2) % isResolvedArr.length],
          isTransfer: isTransferArr[(i + 3) % isTransferArr.length],
          deliveryStatus: deliveryStatusArr[i % deliveryStatusArr.length],
          deliveryType: deliveryTypeArr[i % deliveryTypeArr.length],
        });
      }
      // 再补充30组随机组合
      for (let i = 20; i < 50; i++) {
        arr.push({
          key: i + 1,
          scenario: scenarios[Math.floor(Math.random() * scenarios.length)],
          orderStatus: orderStatusArr[Math.floor(Math.random() * orderStatusArr.length)],
          hasIM: hasIMArr[Math.floor(Math.random() * hasIMArr.length)],
          predictRefund: predictRefundArr[Math.floor(Math.random() * predictRefundArr.length)],
          hasComment: hasCommentArr[Math.floor(Math.random() * hasCommentArr.length)],
          isResolved: isResolvedArr[Math.floor(Math.random() * isResolvedArr.length)],
          isTransfer: isTransferArr[Math.floor(Math.random() * isTransferArr.length)],
          deliveryStatus: deliveryStatusArr[Math.floor(Math.random() * deliveryStatusArr.length)],
          deliveryType: deliveryTypeArr[Math.floor(Math.random() * deliveryTypeArr.length)],
        });
      }
      return arr;
    }

    // 用mock数据替换tableData
    tableData.value = generateMockFactorData();

    return {
      dateFormat,
      filters,
      filteredTableData,
      handleScenarioClick,
      selectedScenarioPath,
      handleBreadcrumbClick,
      hasChildren,
      chartOptions,
      columns,
      filteredMetricCards,
      handleTabChange,
      getTrendClass,
      getTrendIcon,
      getScenarioColor,
      handleScenarioChange,
      handleDateChange,
      getScenarioOptions,
      metricCards,
      activeChartTab,
      tableData,
      charts,
      scenarioHierarchy,
      isComparisonMode,
      comparisonFilters,
      comparisonMetricCards,
      comparisonColumns,
      comparisonTableData,
      handleComparisonModeChange,
      handleComparisonScenarioChange,
      getDiffClass,
      pieCharts,
      issueCategories,
      activeTab,
      issueColumns,
      issueData,
      getStatusColor,
      transferRateChart,
      resolutionRateChart,
      refundRateChart,
      compensationRateChart,
      waimaiOnlinePieChart,
      waimaiPhonePieChart,
      pinhfOnlinePieChart,
      riderOnlinePieChart,
      riderPhonePieChart,
      merchantOnlinePieChart,
      issueTypeChart,
      issueTrendChart,
      waimaiOnlineTrendChart,
      waimaiPhoneTrendChart,
      pinhfOnlineTrendChart,
      riderOnlineTrendChart,
      riderPhoneTrendChart,
      merchantOnlineTrendChart,
      overviewTimeRange,
      handleOverviewTimeChange,
      updateOverviewCharts,
      structuredQuery,
      smartSearch,
      handleSmartSearch,
      parseNaturalLanguageQuery,
      // 新增
      searchMode,
      handleSearchModeChange,
      handleClearSmartSearch,
      resetToInitialData,
      initMetricCards,
      resetPreciseFilters,
      setSearchMode,
      getTrendAnalysisTitle,
      getTrendAnalysisLegend,
      showProblemDetail,
      waimaiOnlineProblemPie,
      waimaiOnlineProblemTrend,
      problemPieChart,
      problemTrendChart,
      problemColumns,
      problemTableComputedData,
      problemAnalysisData,
      handleSearch,
      selectedProblemType,
      selectedSubProblemType,
      problemSubPieChart,
      experimentGroups,
      modelVersions,
      factorOptions,
      factorStatsData,
      factorPieChart,
      factorStatsColumns,
      factorDetailVisible,
      factorDetailList,
      factorDetailTitle,
      showFactorDetail,
    };
  }
});
</script>

<style scoped>
.performance-dashboard {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-size: 22px;
  color: rgba(0, 0, 0, 0.85);
}

.filter-card {
  margin-bottom: 24px;
}

.metric-cards {
  margin-bottom: 24px;
}

.metric-card {
  height: 100%;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.metric-card.warning {
  border-left: 3px solid #f5222d;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.metric-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.help-icon {
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
}

.metric-value {
  font-size: 28px;
  font-weight: 500;
  margin-bottom: 8px;
  display: flex;
  align-items: baseline;
}

.trend-icon {
  font-size: 12px;
  margin-left: 8px;
}

.trend-icon.positive {
  color: #52c41a;
}

.trend-icon.negative {
  color: #f5222d;
}

.metric-footer {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.data-card {
  margin-bottom: 24px;
}

.chart-container {
  width: 100%;
  height: 400px;
  margin-bottom: 24px;
}

.trend-indicator {
  font-size: 12px;
  margin-left: 4px;
}

.trend-indicator.positive {
  color: #52c41a;
}

.trend-indicator.negative {
  color: #f5222d;
}

.drill-down-message {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.message-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 80%;
  text-align: center;
  z-index: 1001; /* 确保消息框在最上层 */
}

.message-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #1890ff;
}

.message-text {
  margin-bottom: 20px;
  color: rgba(0, 0, 0, 0.65);
}

.message-loading {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.loading-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  margin: 0 4px;
  border-radius: 50%;
  background-color: #1890ff;
  animation: loading 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.structured-query {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  margin-top: 8px;
}

.structured-query-header {
  margin-bottom: 12px;
}

.structured-query-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.structured-query .ant-tag {
  margin-right: 8px;
  margin-bottom: 4px;
  padding: 4px 8px;
  font-size: 14px;
}

.scenario-breadcrumb {
  margin-bottom: 16px;
}

.scenario-breadcrumb :deep(.ant-breadcrumb-link) {
  cursor: pointer;
}

.scenario-breadcrumb :deep(.ant-breadcrumb-link:hover) {
  color: #1890ff;
}

.scenario-tag {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s;
}

.scenario-tag:hover {
  transform: translateX(2px);
}

.scenario-tag .anticon {
  font-size: 12px;
}

.comparison-switch {
  margin-bottom: 16px;
  text-align: right;
}

.comparison-group {
  background-color: #fafafa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.comparison-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #1890ff;
}

.comparison-card {
  background-color: #fafafa;
}

.metric-comparison {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.comparison-value {
  display: flex;
  align-items: center;
  gap: 8px;
}

.value-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  width: 60px;
}

.value-number {
  font-size: 20px;
  font-weight: 500;
  flex: 1;
}

.value-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.comparison-diff {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed #f0f0f0;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
}

.comparison-diff.positive {
  color: #52c41a;
}

.comparison-diff.negative {
  color: #f5222d;
}

/* 业务线总览样式 */
.overview-card {
  margin-bottom: 24px;
}

.business-line-card {
  height: 100%;
  transition: all 0.3s;
}

.business-line-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.business-line-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.business-line-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-item .label {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  width: 70px;
}

.metric-item .value {
  font-size: 16px;
  font-weight: 500;
  flex: 1;
}

.metric-item .trend {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 2px;
}

.metric-item .trend.up {
  color: #52c41a;
  background-color: #f6ffed;
}

.metric-item .trend.down {
  color: #f5222d;
  background-color: #fff1f0;
}

.issue-distribution {
  height: 200px;
  width: 100%;
  position: relative;
}

.pie-chart {
  width: 100%;
  height: 200px;
  margin-bottom: 16px;
}

.dashboard-tabs {
  margin-bottom: 24px;
}

.analysis-card {
  margin-top: 24px;
}

.analysis-section {
  background: #fff;
  padding: 16px;
  border-radius: 4px;
}

.analysis-section h3 {
  margin-bottom: 16px;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
}

.chart-container {
  height: 300px;
  width: 100%;
}

.trend {
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 12px;
}

.trend.上升 {
  color: #f5222d;
  background-color: #fff1f0;
}

.trend.下降 {
  color: #52c41a;
  background-color: #f6ffed;
}

.trend.稳定 {
  color: #1890ff;
  background-color: #e6f7ff;
}

.overview-filter {
  margin-bottom: 16px;
  text-align: right;
}

.charts-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 320px;
}

.trend-chart {
  height: 150px;
  width: 100%;
  position: relative;
}

.issue-distribution {
  height: 150px;
  width: 100%;
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.pie-chart {
  width: 100%;
  height: 100%;
}

.structured-query-container {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;

  .structured-query-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .structured-query-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .ant-tag {
      margin-right: 0;
      padding: 4px 8px;
      font-size: 14px;
      
      .anticon {
        margin-right: 4px;
      }
    }
  }
}

/* 添加搜索模式选择器样式 */
.search-mode-selector {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
}

.search-mode-selector :deep(.ant-radio-button-wrapper) {
  width: 120px;
  text-align: center;
}

/* 优化搜索模式选择器样式 */
.search-mode-tabs {
  display: flex;
  margin-bottom: 24px;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-mode-tab {
  flex: 1;
  padding: 12px 0;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-bottom: 3px solid transparent;
}

.search-mode-tab.active {
  background-color: #fff;
  border-bottom: 3px solid #1890ff;
  font-weight: bold;
  color: #1890ff;
}

.tab-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.tab-text {
  font-size: 14px;
}

/* 智能分析搜索框样式 */
.ai-search-container {
  background-color: #f0f8ff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #d9e7ff;
}

.ai-search-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.ai-badge {
  display: inline-block;
  background: linear-gradient(90deg, #1890ff, #722ed1);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: bold;
  font-size: 12px;
  margin-right: 8px;
}

.ai-search-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.ai-input {
  margin-bottom: 8px;
}

.ai-input :deep(.ant-input) {
  border-color: #1890ff;
  padding: 12px;
  font-size: 14px;
}

.ai-input :deep(.ant-input:focus) {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ai-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #722ed1);
  border: none;
}

.ai-icon {
  margin-right: 4px;
}

.ai-search-hint {
  color: #666;
  font-size: 12px;
  margin-top: 8px;
}

.ai-search-button {
  background: linear-gradient(90deg, #1890ff, #722ed1);
  border: none;
}

.ai-search-button:hover {
  background: linear-gradient(90deg, #40a9ff, #8e59d4);
  border: none;
}

/* 趋势分析样式 */
.trend-analysis-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  justify-content: space-between;
}

.trend-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.trend-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 4px;
}

.legend-text {
  font-size: 12px;
  color: #666;
}

.problem-analysis {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.section-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.chart-wrapper {
  background: #fafafa;
  border-radius: 4px;
  padding: 8px;
}

[ref="problemPieChart"] {
  z-index: 9999 !important;
  pointer-events: auto !important;
  background: rgba(255,255,255,0.01) !important;
}
</style> 