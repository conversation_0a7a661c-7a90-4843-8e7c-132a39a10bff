<template>
  <a-modal
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    title="一键打标"
    width="650px"
    :footer="null"
    @cancel="onCancel"
    :maskClosable="false"
    class="tagging-modal"
  >
    <div v-if="selectedItems.length === 0" class="empty-selection">
      <a-empty description="请先选择需要打标的会话">
        <template #description>
          <span>请先选择需要打标的会话</span>
        </template>
        <a-button type="primary" @click="closeModal">
          返回选择
        </a-button>
      </a-empty>
    </div>
    
    <div v-else>
      <div class="selected-count">
        已选择 <a-tag color="blue">{{ selectedItems.length }}</a-tag> 条会话
        <a-button type="link" @click="clearSelection">清空</a-button>
      </div>
      
      <a-form layout="vertical" class="compact-form">
        <a-form-item label="打标标签" required :style="{marginBottom: '12px'}">
          <a-select
            v-model:value="selectedTagType"
            placeholder="请选择打标标签"
            style="width: 100%"
            @change="handleTagTypeChange"
          >
            <a-select-option value="userIntent">用户诉求</a-select-option>
            <a-select-option value="scenario">厘清场景</a-select-option>
          </a-select>
        </a-form-item>
        
        <!-- 用户诉求属性 -->
        <div v-if="selectedTagType === 'userIntent'" class="tag-attributes">
          <a-card :bordered="false" class="attributes-card compact-card">
            <template #title>
              <div class="attributes-card-title">
                <UserOutlined class="tag-title-icon" />
                <span>用户诉求属性</span>
              </div>
            </template>
            
            <a-row :gutter="[16, 0]">
              <a-col :span="24">
                <a-form-item label="打标方式" class="super-compact-form-item">
                  <a-select v-model:value="userIntentParams.method" style="width: 100%" @change="handleMethodChange">
                    <a-select-option value="llm">大模型</a-select-option>
                    <a-select-option value="rule">规则</a-select-option>
                    <a-select-option value="manual">人工</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            
            <!-- 当选择大模型时显示的联动字段 -->
            <div v-if="userIntentParams.method === 'llm'">
              <a-row :gutter="[16, 0]">
                <a-col :span="24">
                  <div class="field-group">
                    <div class="field-label">机器人配置</div>
                    <div class="field-value">
                      <a-tag color="blue">{{ getSpaceLabel(userIntentParams.robotSpace) }}</a-tag>
                      <a-tag color="green">{{ getRobotLabel(userIntentParams.robotSpace, userIntentParams.robot) }}</a-tag>
                    </div>
                  </div>
                </a-col>
              </a-row>
            </div>
            
            <a-row :gutter="[16, 0]">
              <a-col :span="12">
                <a-form-item label="输入字段" class="super-compact-form-item" tooltip="选择使用的历史消息记录">
                  <a-row :gutter="8" align="middle">
                    <a-col :span="10">
                      <div class="var-name">historyContext</div>
                    </a-col>
                    <a-col :span="14">
                      <a-select v-model:value="userIntentParams.historyContext" size="small">
                        <a-select-option value="last3">最近3条</a-select-option>
                        <a-select-option value="last5">最近5条</a-select-option>
                        <a-select-option value="last10">最近10条</a-select-option>
                        <a-select-option value="all">全部</a-select-option>
                      </a-select>
                    </a-col>
                  </a-row>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="输出字段" class="super-compact-form-item" tooltip="标签输出字段名称">
                  <a-row :gutter="8" align="middle">
                    <a-col :span="10">
                      <div class="var-name">outputField</div>
                    </a-col>
                    <a-col :span="14">
                      <a-input v-model:value="userIntentParams.outputField" size="small" />
                    </a-col>
                  </a-row>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </div>
        
        <!-- 厘清场景属性 -->
        <div v-if="selectedTagType === 'scenario'" class="tag-attributes">
          <a-card :bordered="false" class="attributes-card compact-card">
            <template #title>
              <div class="attributes-card-title">
                <AppstoreOutlined class="tag-title-icon" />
                <span>厘清场景属性</span>
              </div>
            </template>
            
            <a-row :gutter="[16, 0]">
              <a-col :span="24">
                <a-form-item label="打标方式" class="super-compact-form-item">
                  <a-select v-model:value="scenarioParams.method" style="width: 100%" @change="handleScenarioMethodChange">
                    <a-select-option value="llm">大模型</a-select-option>
                    <a-select-option value="rule">规则</a-select-option>
                    <a-select-option value="manual">人工</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            
            <!-- 当选择大模型时显示的联动字段 -->
            <div v-if="scenarioParams.method === 'llm'">
              <a-row :gutter="[16, 0]">
                <a-col :span="24">
                  <div class="field-group">
                    <div class="field-label">机器人配置</div>
                    <div class="field-value">
                      <a-tag color="blue">{{ getSpaceLabel(scenarioParams.robotSpace) }}</a-tag>
                      <a-tag color="green">{{ getRobotLabel(scenarioParams.robotSpace, scenarioParams.robot) }}</a-tag>
                    </div>
                  </div>
                </a-col>
              </a-row>
            </div>
            
            <a-row :gutter="[16, 0]">
              <a-col :span="12">
                <a-form-item label="会话ID" class="super-compact-form-item" tooltip="选择标注的会话ID">
                  <a-row :gutter="8" align="middle">
                    <a-col :span="10">
                      <div class="var-name">sessionId</div>
                    </a-col>
                    <a-col :span="14">
                      <a-select v-model:value="scenarioParams.sessionId" size="small">
                        <a-select-option v-for="id in selectedSessionIds" :key="id" :value="id">
                          {{ id.substring(0, 6) }}...
                        </a-select-option>
                      </a-select>
                    </a-col>
                  </a-row>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="输出字段" class="super-compact-form-item" tooltip="标签输出字段名称">
                  <a-row :gutter="8" align="middle">
                    <a-col :span="10">
                      <div class="var-name">outputField</div>
                    </a-col>
                    <a-col :span="14">
                      <a-input v-model:value="scenarioParams.outputField" size="small" />
                    </a-col>
                  </a-row>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </div>
      </a-form>
      
      <div class="action-buttons">
        <a-button @click="onCancel">取消</a-button>
        <a-button 
          type="primary" 
          @click="applyTags" 
          :disabled="!selectedTagType"
          :loading="loading"
        >
          <template #icon><CheckOutlined /></template>
          确认打标
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { defineComponent, ref, reactive, computed, watch } from 'vue';
import { UserOutlined, AppstoreOutlined, CheckOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  name: 'TaggingModal',
  components: {
    UserOutlined,
    AppstoreOutlined,
    CheckOutlined
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedItems: {
      type: Array,
      default: () => []
    },
    robotSpaces: {
      type: Array,
      default: () => []
    },
    robotsBySpace: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible', 'cancel', 'clear-selection', 'apply-tags'],
  setup(props, { emit }) {
    // 打标类型和参数
    const selectedTagType = ref('');
    
    // 用户诉求参数
    const userIntentParams = reactive({
      method: 'llm',  // 默认为大模型
      historyContext: 'last5',  // 默认为最近5条
      robotSpace: 'foodDelivery',  // 默认为外卖客服
      robot: 'tagRobot',  // 默认为外卖打标机器人
      outputField: '用户诉求'  // 默认输出字段
    });
    
    // 厘清场景参数
    const scenarioParams = reactive({
      method: 'llm',  // 默认为大模型
      sessionId: '',  // 默认为空，将根据选择设置
      robotSpace: 'foodDelivery',  // 默认为外卖客服
      robot: 'tagRobot',  // 默认为外卖打标机器人
      outputField: '场景类型'  // 默认输出字段
    });
    
    // 所选会话的SessionIDs
    const selectedSessionIds = computed(() => {
      return props.selectedItems;
    });
    
    // 获取空间标签
    const getSpaceLabel = (spaceValue) => {
      const space = props.robotSpaces.find(space => space.value === spaceValue);
      return space ? space.label : spaceValue;
    };
    
    // 获取机器人标签
    const getRobotLabel = (spaceValue, robotValue) => {
      const robots = props.robotsBySpace[spaceValue] || [];
      const robot = robots.find(robot => robot.value === robotValue);
      return robot ? robot.label : robotValue;
    };
    
    // 打标方式变更处理
    const handleMethodChange = (value) => {
      // 如果选择了非大模型方法，不需要特殊处理
      console.log('打标方式变更:', value);
    };
    
    // 场景打标方式变更
    const handleScenarioMethodChange = (value) => {
      // 如果选择了非大模型方法，不需要特殊处理
      console.log('场景打标方式变更:', value);
    };
    
    // 打标类型变更处理
    const handleTagTypeChange = (value) => {
      if (value === 'scenario' && selectedSessionIds.value.length > 0) {
        // 默认选择第一个sessionId
        scenarioParams.sessionId = selectedSessionIds.value[0];
      }
    };
    
    // 清空选择
    const clearSelection = () => {
      emit('clear-selection');
    };
    
    // 关闭模态框
    const closeModal = () => {
      emit('update:visible', false);
    };
    
    // 取消打标
    const onCancel = () => {
      emit('update:visible', false);
      emit('cancel');
    };
    
    // 应用标签
    const applyTags = () => {
      if (!selectedTagType.value) {
        return;
      }
      
      let tagData = {
        type: selectedTagType.value,
        sessions: props.selectedItems
      };
      
      // 根据类型添加不同参数
      if (selectedTagType.value === 'userIntent') {
        tagData.params = { ...userIntentParams };
      } else if (selectedTagType.value === 'scenario') {
        tagData.params = { ...scenarioParams };
      }
      
      emit('apply-tags', tagData);
    };
    
    // 监听visible变化，重置表单
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        selectedTagType.value = '';
      }
    });
    
    return {
      selectedTagType,
      userIntentParams,
      scenarioParams,
      selectedSessionIds,
      handleTagTypeChange,
      handleMethodChange,
      handleScenarioMethodChange,
      clearSelection,
      closeModal,
      onCancel,
      applyTags,
      getSpaceLabel,
      getRobotLabel
    };
  }
});
</script>

<style scoped>
/* 标签选择模态框样式 */
.tagging-modal :deep(.ant-modal-header) {
  border-bottom: none;
  padding: 20px 24px 0;
}

.tagging-modal :deep(.ant-modal-body) {
  padding: 20px 24px;
}

.tagging-modal :deep(.ant-modal-footer) {
  border-top: none;
}

.selected-count {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tag-attributes {
  margin-top: 16px;
  margin-bottom: 20px;
}

.attributes-card {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.attributes-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.attributes-card-title {
  display: flex;
  align-items: center;
}

.action-buttons {
  margin-top: 24px;
  text-align: right;
}

.action-buttons .ant-btn {
  margin-left: 8px;
}

.tag-title-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #1890ff;
}

.compact-form :deep(.ant-form-item-label) {
  padding-bottom: 4px;
}

.super-compact-form-item {
  margin-bottom: 8px;
}

.compact-card :deep(.ant-card-head) {
  min-height: 40px;
  padding: 0 12px;
}

.compact-card :deep(.ant-card-head-title) {
  padding: 8px 0;
}

.compact-card :deep(.ant-card-body) {
  padding: 12px;
}

.field-group {
  margin-bottom: 12px;
  padding: 8px;
  background: #f9f9fa;
  border-radius: 4px;
}

.field-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 4px;
  font-weight: 500;
}

.field-value {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.var-name {
  font-family: monospace;
  font-size: 12px;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  color: #333;
  border: 1px dashed #d9d9d9;
}
</style> 