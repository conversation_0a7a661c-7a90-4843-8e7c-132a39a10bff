export interface WorkbenchSessionChatInfo {
    /**
     * 消息ID
     */
    messageId: string;

    /**
     * 时间
     */
    time: string;  // ISO 8601 格式的日期字符串

    /**
     * 类型 1-用户 2-客服
     */
    senderType: number;

    /**
     * 消息
     */
    message: string;

    /**
     * 消息类型
     */
    messageType: string;

    /**
     * 原始消息，不展示，用于查看未支持的消息类型
     */
    originMessage: string;

    /**
     * 是否大模型
     */
    isLlm: boolean;

    /**
     * 人工点赞点踩信息
     */
    manualInspect: ManualInspect;

    /**
     * 评论信息
     */
    comment: Comment;

    /**
     * 该消息的aida应用相关信息
     */
    messageLevelAidaAppInfo: MessageLevelAidaAppInfoDTO;

    /**
     * 基本信息
     */
    basicInfo: InspectWorkbenchQueryDetailDTO.Detail.BasicInfo[];
}

export interface ManualInspect {
    /**
     * 质检结果 1-赞 2-踩
     */
    inspectResult: number;

    /**
     * 质检人
     */
    inspectMis: string;
}

export interface Comment {
    /**
     * 评论数量
     */
    commentCount: number;
}

export interface MessageLevelAidaAppInfoDTO {
    // 这里定义 messageLevelAidaAppInfo 的属性，根据实际情况扩展
}

export interface InspectWorkbenchQueryDetailDTO {
    Detail: {
        BasicInfo: {
            // 定义基本信息的属性
        };
    };
}
