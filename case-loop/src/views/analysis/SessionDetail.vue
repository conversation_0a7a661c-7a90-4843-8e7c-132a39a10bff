<template>
  <div class="session-detail-container">
    <!-- 会话信息区域 -->
    <mtd-collapse v-model="activeCollapse" class="session-info-collapse-container">
      <!-- 基本信息 -->
      <div class="info-section">
        <div class="section-title">基本信息</div>
        <mtd-form colon inline label-position="left" style="margin-left: 10px">
          <mtd-row>
            <mtd-col :span="6">
              <mtd-form-item label="会话ID">
                <span>{{ sessionBaseInfo?.sessionId }}</span>
              </mtd-form-item>
            </mtd-col>
            <mtd-col :span="6">
              <mtd-form-item label="会话时间">
                <span>{{ dayjs(sessionBaseInfo?.time).format('YYYY-MM-DD HH:mm:ss') }}</span>
              </mtd-form-item>
            </mtd-col>
            <mtd-col :span="6">
              <mtd-form-item label="用户ID">
                <span>{{ sessionBaseInfo?.userId || '--' }}</span>
              </mtd-form-item>
            </mtd-col>

            <mtd-col :span="6">
              <mtd-form-item label="场景名称">
                <mtd-tooltip
                    :content="sessionBaseInfo?.sceneName || '--'"
                    placement="top"
                    :disabled="!sessionBaseInfo?.sceneName || sessionBaseInfo?.sceneName.length < 10"
                >
                    <span class="visit-id-text">
                      {{
                        sessionBaseInfo?.sceneName && sessionBaseInfo?.sceneName.length > 0
                            ? sessionBaseInfo?.sceneName.substring(0, 25) +
                            (sessionBaseInfo?.sceneName.length > 25 ? '...' : '')
                            : '--'
                      }}
                    </span>
                </mtd-tooltip>
              </mtd-form-item>
            </mtd-col>
            <mtd-col :span="6">
              <mtd-form-item label="是否转人工">
                <span>{{ sessionBaseInfo?.transferStaff || '--' }}</span>
              </mtd-form-item>
            </mtd-col>

            <mtd-col :span="6">
              <mtd-form-item label="会话是否解决">
                <span>{{ sessionBaseInfo?.sessionSolved || '--' }}</span>
              </mtd-form-item>
            </mtd-col>
            <mtd-col :span="6">
              <mtd-form-item label="订单ID">
                <span>{{ sessionBaseInfo?.orderId || '--' }}</span>
              </mtd-form-item>
            </mtd-col>
            <mtd-col :span="6">
              <mtd-form-item label="满意度">
                <span>{{ sessionBaseInfo?.stars || '--' }}</span>
              </mtd-form-item>
            </mtd-col>
            <mtd-col :span="6">
              <mtd-form-item label="VisitID">
                <mtd-tooltip
                    :content="sessionBaseInfo?.visitIds || '--'"
                    placement="top"
                    :disabled="!sessionBaseInfo?.visitIds || sessionBaseInfo?.visitIds.length < 10"
                >
                    <span class="visit-id-text">
                      {{
                        sessionBaseInfo?.visitIds && sessionBaseInfo.visitIds.length > 0
                            ? sessionBaseInfo.visitIds.substring(0, 20) +
                            (sessionBaseInfo.visitIds.length > 20 ? '...' : '')
                            : '--'
                      }}
                    </span>
                </mtd-tooltip>
              </mtd-form-item>
            </mtd-col>

          </mtd-row>
        </mtd-form>
      </div>
    </mtd-collapse>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧对话详情 -->
      <div class="chat-section">
        <mtd-card shadow="hover" :border="false" class="chat-card">
          <template #title>
            <div style="display: flex; align-items: center; gap: 4px">
              对话详情
            </div>
          </template>
          <div class="session-chat-wrapper" v-if="sessionBaseInfo?.channel.includes(SessionSourceChannel.ONLINE)">
            <session-chat
                :session-chat-list="sessionChatList"
                :platform-type="sessionBaseInfo?.platformType"
                @llm-message-select="onLLMMessageSelect"
            />
          </div>
        </mtd-card>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <mtd-loading :loading="isLlmMessageLoading" :delay="300" type="line-scale" message-position="right">
          <div v-if="sessionBaseInfo" class="content-layout"
               :class="{ 'left-panel-collapsed': isSignalPanelCollapsed }">
            <!-- 左侧信息面板 -->
            <div class="signal-info-panel" :class="{ 'collapsed': isSignalPanelCollapsed }">
              <!-- 展开收起按钮 -->
              <div class="panel-toggle-btn" @click="toggleSignalPanel">
                <div class="toggle-bar" :class="{ 'collapsed': isSignalPanelCollapsed }">
                  <span class="toggle-arrow" :class="{ 'collapsed': isSignalPanelCollapsed }">▼</span>
                </div>
              </div>

              <!-- 面板内容 -->
              <div class="panel-content" :class="{ 'collapsed': isSignalPanelCollapsed }">
                <!-- 输入输出 -->
                <original-input-output-card
                    :cur-message-llm-info="curMessageLlmInfo"
                    :session-base-info="sessionBaseInfo"
                    v-model:aida-exec-path-modal-visible="aidaExecPathModalVisible"
                    @aida-exec-path-click="onAidaExecPathClick"
                />
                <!-- 方案执行 -->
                <execution-plan-card :cur-message-llm-info="curMessageLlmInfo"/>

                <!-- 信号信息 -->
                <aida-signal-card :query-message-llm-info="curMessageLlmInfo"/>
              </div>
            </div>

            <!-- 右侧模型信息面板 -->
            <div class="model-info-panel">
              <session-factor
                  :session-id="sessionId"
                  :current-message="curMessage"
              />
              <diagnosis-process
                  :session-id="sessionId"
                  :current-message="curMessage"
              />
              <session-review
                  :session-id="sessionId"
                  :current-user-mis="currentUserMis"
                  :current-message="curMessage"
              />
            </div>
          </div>
        </mtd-loading>
      </div>
    </div>
  </div>

  <!-- 加入评测集弹窗 -->
  <AddErrorSetModal
      v-if="addErrorSetModalVisible"
      :model-value.sync="addErrorSetModalVisible"
      :version-id="curMessageLlmInfo?.messageLevelAidaAppInfo?.applicationVersion || ''"
      :workspace-id="curMessageLlmInfo?.messageLevelAidaAppInfo?.workspaceId || ''"
      :application-id="curMessageLlmInfo?.messageLevelAidaAppInfo?.applicationId || ''"
      :application-name="curMessageLlmInfo?.messageLevelAidaAppInfo?.applicationName || ''"
      :message-detail="addErrorSetModalParams"
      :llm-message-id="curMessageLlmInfo?.id || ''"
      @close="onAddErrorSetModalClose"
  />
  <!-- 可视化执行链路弹窗 -->
  <AidaExecPathModal
      v-if="aidaExecPathModalVisible && currentLlmInfoMapMessage"
      :visible="aidaExecPathModalVisible"
      :platform="sessionBaseInfo?.platformType"
      :llm-session-id-list="sessionBaseInfo?.id"
      :session-id="sessionId"
      :message="currentLlmInfoMapMessage"
      @close="onAidaExecPathModalClose"
  />
</template>

<script lang="ts">
export default {name: 'AnalysisToolsSessionDetail'};
</script>

<script setup lang="ts">
/**
 * 官方文档参考：https://github.com/jbaysolutions/vue-grid-layout
 */
import {
  getSessionBaseInfo,
  getSessionChatList,
  getSessionChatLLMInfo,
  sessionChatMessageCollect,
  getSessionUserRoleResult,
  getAudioInfoApi,
  getCurrentUserInfo,

  getSessionBasicInfo,
} from '../../api/workbench-session';
import {message} from 'ant-design-vue';
import dayjs from 'dayjs';
import {ref, onMounted} from 'vue';
import {
  SessionChatLLMInspectType,
  UserRole,
  SessionChatClickType,
  SessionSourceChannel,
} from '@/constants/session-chat';
import {PlatformType} from '@/constants/platform';
import SessionChat from './components/session-chat.vue';
import AidaSignalCard from './components/aida-signal-card.vue';
import AddErrorSetModal from './components/add-error-set-modal.vue';
import SessionReview from './components/session-review.vue';
import SessionFactor from './components/session-factor.vue';
import DiagnosisProcess from './components/diagnosis-process.vue';
import AidaExecPathModal from './components/aida-exec-path-modal.vue';
import OriginalInputOutputCard from './components/original-input-output-card.vue';
import ExecutionPlanCard from './components/execution-plan-card.vue';
import {isFromLabelPlatform} from '../../utils/platform';
import {sendMessageToParent} from '../../utils/open-across-iframe';

import {useRoute} from 'vue-router';

const isFromLabel = ref(isFromLabelPlatform());
const route = useRoute();

const sessionId = ref(route.params.sessionId as string);

const sessionBaseInfo = ref<WorkbenchSessionBaseInfo>();
const sessionChatList = ref<Array<WorkbenchSessionChatInfo>>([]);

const curMessage = ref<WorkbenchSessionChatInfo>();
const curMessageLlmInfo = ref<WorkbenchSessionChatLLMInfo>();

const isLlmMessageLoading = ref(false);

const onLLMMessageSelect = async (
    message: WorkbenchSessionChatInfo,
    clickType: SessionChatClickType,
    payload?: { inspectType?: SessionChatLLMInspectType }
) => {
  try {
    if (isLlmMessageLoading.value) return;
    isLlmMessageLoading.value = true;
    curMessage.value = message;
    await updateMessageLLMInfo(message);
    isLlmMessageLoading.value = false;

    // 若为普通点击，即可结束逻辑
    if (clickType === SessionChatClickType.CLICK_MESSAGE) return;

    if (clickType === SessionChatClickType.ADD_ERROR_SET) {
      // 加入评测集
      onMessageCollect(message);
    }
  } catch (e) {
    console.error('选择消息失败:', e);
  } finally {
    isLlmMessageLoading.value = false;
  }
};

// 记录所有正在加入评测集的消息id，防止相同参数重复请求
const isCollectingMessageIdSet = ref(new Set<string>());
const onMessageCollect = async (message: WorkbenchSessionChatInfo) => {
  switch (sessionBaseInfo.value?.platformType) {
    case PlatformType.AIDA:
      onAidaMessageCollect(message);
      break;
    case PlatformType.PB:
      onPbMessageCollect(message);
      break;
    default:
      throw new TypeError('invalid platformType');
  }
};

const addErrorSetModalVisible = ref(false);
const addErrorSetModalParams = ref<{
  sessionId: string;
  messageId: string;
  platform: PlatformType;
  originMessage: string;
  /**
   * 透传getSessionBaseInfo响应即可
   */
  llmSessionIdList: string[];
  /**
   * 透传getSessionChatLLMInfo响应即可
   */
  workspaceId?: string;
  /**
   * 透传getSessionChatLLMInfo响应即可
   */
  applicationId?: string;
  /**
   * 透传getSessionChatLLMInfo响应即可
   */
  applicationName?: string;
}>();
const onAidaMessageCollect = (message: WorkbenchSessionChatInfo) => {
  const {messageId, originMessage} = message;

  if (
      !sessionBaseInfo.value?.platformType ||
      !sessionBaseInfo.value?.workspaceId ||
      !sessionBaseInfo.value?.applicationId
  ) {
    throw new TypeError('sessionBaseInfo is not exists');
  }

  addErrorSetModalParams.value = {
    messageId,
    originMessage,
    platform: sessionBaseInfo.value?.platformType,
    workspaceId: curMessageLlmInfo.value?.messageLevelAidaAppInfo?.workspaceId,
    applicationId: curMessageLlmInfo.value?.messageLevelAidaAppInfo?.applicationId,
    llmSessionIdList: sessionBaseInfo.value?.id,
    sessionId: sessionId.value,
    applicationName: curMessageLlmInfo.value?.messageLevelAidaAppInfo?.applicationName,
  };
  addErrorSetModalVisible.value = true;
};

const onPbMessageCollect = async (message: WorkbenchSessionChatInfo) => {
  const {messageId} = message;
  if (isCollectingMessageIdSet.value.has(messageId)) return;

  if (!sessionBaseInfo.value || !messageId || !curMessageLlmInfo.value) {
    throw new TypeError('sessionBaseInfo or messageId or llmInfo is not exists');
  }

  try {
    isCollectingMessageIdSet.value.add(messageId);
    await sessionChatMessageCollect({
      sessionId: sessionId.value,
      platformType: sessionBaseInfo.value.platformType,
      llmSessionId: sessionBaseInfo.value.id,
      messageId,
      llmMessageId: curMessageLlmInfo.value.id,
    });
    message.success('添加成功');
  } catch (e) {
    console.error('加入评测集失败:', e);
  } finally {
    isCollectingMessageIdSet.value.delete(messageId);
  }
};

const onAddErrorSetModalClose = () => {
  addErrorSetModalParams.value = undefined;
};

const initClientMemory = () => {
  /**
   * 读取信号模块和模型信息模块的位置
   * 注意：拖拽布局相关代码已移除，此函数保留以防其他地方调用
   */
  if (!sessionBaseInfo.value) return;
  // 拖拽布局相关的变量引用已移除
  console.log('initClientMemory called, but grid layout functionality has been removed');
};

const initBaseInfo = async () => {
  try {
    sessionBaseInfo.value = await getSessionBaseInfo(sessionId.value);

    // 判断是否需要调用新接口获取额外字段
    if (sessionBaseInfo.value?.channel && sessionBaseInfo.value.channel.includes('online')) {
      try {
        // 调用新接口获取额外字段
        const basicData = await getSessionBasicInfo({
          sessionId: sessionId.value,
          analysisType: 'online'
        });

        if (basicData) {
          // 使用新接口返回的字段更新sessionBaseInfo
          sessionBaseInfo.value = {
            ...sessionBaseInfo.value,
            visitIds: basicData.visitIds || sessionBaseInfo.value.visitIds,
            stars: basicData.stars || sessionBaseInfo.value.stars,
            sessionSolved: basicData.sessionSolved || sessionBaseInfo.value.sessionSolved,
            transferStaff: basicData.transferStaff || sessionBaseInfo.value.transferStaff,
            sceneName: basicData.sceneName || sessionBaseInfo.value.sceneName,
            standardQuestion: basicData.standardQuestion || sessionBaseInfo.value.standardQuestion,
            windowWidthSetUp: basicData.windowWidthSetUp || sessionBaseInfo.value.windowWidthSetUp,
            serviceProcessInfo: basicData.serviceProcessInfo || sessionBaseInfo.value.serviceProcessInfo,
          };
        }
      } catch (e) {
        console.error('获取会话额外信息失败:', e);
      }
    }

    // 打印serviceProcessInfo用于调试
    console.log('sessionBaseInfo:', sessionBaseInfo.value);
    if (sessionBaseInfo.value?.serviceProcessInfo) {
    } else {
      console.warn('serviceProcessInfo 不存在或为空');
    }
    initClientMemory();
  } catch (e) {
    console.error('获取会话基本信息失败:', e);
  }
};

// 大模型消息（curMessageLlmInfo）对应的门户消息id，大模型消息curMessageLlmInfo的原始id与当前消息id一致，则无需更新curMessageLlmInfo
const currentLlmInfoMapMessage = ref<WorkbenchSessionChatInfo | null>(null);
const updateMessageLLMInfo = async (message: WorkbenchSessionChatInfo) => {
  const {messageId, originMessage, messageLevelAidaAppInfo} = message;
  const {workspaceId, applicationId} = messageLevelAidaAppInfo || {};
  if (!messageId || !originMessage || !sessionBaseInfo.value?.platformType || !sessionId.value) return;
  if (currentLlmInfoMapMessage.value?.messageId === messageId) return;

  const params = {
    messageId,
    originMessage,
    platform: sessionBaseInfo.value?.platformType,
    workspaceId,
    applicationId,
    llmSessionIdList: sessionBaseInfo.value?.id,
    sessionId: sessionId.value,
    llmMessageId: messageLevelAidaAppInfo?.llmMessageId,
  };

  try {
    // ! 重新赋值前必须要置空，否则会引发vue运行时错误（目前的信号列表等v-for渲染的数据使用的key是index，走vue-diff算法会出问题）
    curMessageLlmInfo.value = undefined;
    const res = await getSessionChatLLMInfo(params);

    curMessageLlmInfo.value = res;
    currentLlmInfoMapMessage.value = message;

    // 如果是来自训练系统，则向父页面发送消息，传递messageId和当前会话信息
    if (isFromLabel.value) {
      sendMessageToParent({
        type: 'llmMessageSelected',
        data: {
          thirdMessageID: message.messageId,
          messageId: res?.messageLevelAidaAppInfo?.llmMessageId,
          messageContent: message.message,
          signal: res?.allSignal?.signalAfterList,
        },
      });
    }
  } catch (e) {
    console.error('获取大模型消息详情失败:', e);
  }
};

const updateSessionList = async () => {
  if (!sessionBaseInfo.value?.platformType || !sessionBaseInfo?.value.id) return;
  try {
    sessionChatList.value =
        (await getSessionChatList(sessionId.value, sessionBaseInfo.value.platformType, sessionBaseInfo.value.id)) || [];
    // 若会话来源为ivr，则序列化originMessage，方便ivr会话列表渲染
    if (sessionBaseInfo.value?.channel === SessionSourceChannel.IVR) {
      sessionChatList.value = sessionChatList.value.map((item) => {
        const serializedOriginMessage = JSON.parse(item.originMessage);
        // 如果存在extraMap，则将extraMap序列化
        if (serializedOriginMessage.extraMap) {
          serializedOriginMessage.extraMap = JSON.parse(serializedOriginMessage.extraMap);
        }
        return {
          ...item,
          serializedOriginMessage,
        };
      });
    }
  } catch (e) {
    console.error('获取会话消息列表失败:', e);
  }
};

/**
 * 查询用户角色
 */
const userRole = ref<UserRole>(UserRole.USER);
const getSessionUserRole = async () => {
  try {
    const res = await getSessionUserRoleResult();
    switch (res.data.name) {
      case 'user':
        userRole.value = UserRole.USER;
        break;
      case 'inspector':
        userRole.value = UserRole.INSPECTOR;
        break;
      default:
        throw new Error('未知的用户角色');
    }
  } catch (error) {
    console.error('获取用户角色失败:', error);
    message.error(error?.message || '获取用户角色失败');

  }
};

const init = () => {
  /**
   * 依赖于initBaseInfo的初始化逻辑
   */
  (async () => {
    await initBaseInfo();
    updateSessionList();
    getSessionUserRole();
  })();
};

init();


// 新增的响应式数据
const activeCollapse = ref(['session-info']); // 控制会话信息的折叠状态

// 信号面板展开收起状态
const isSignalPanelCollapsed = ref(false);

// 切换信号面板展开收起状态
const toggleSignalPanel = () => {
  isSignalPanelCollapsed.value = !isSignalPanelCollapsed.value;
};

const audio = ref<AudioInfoData>({} as AudioInfoData);
const staffAudioUrl = ref('');

onMounted(async () => {
  try {
    const res = await getAudioInfoApi({sessionId: sessionId.value});
    audio.value = JSON.parse(res);
    staffAudioUrl.value = audio.value?.staffVoiceInfo[0]?.recordUrl || '';
  } catch (error) {
    console.error('获取录音信息失败:', error);
    message.error(error?.message || '获取录音信息失败');
  }
});

const aidaExecPathModalVisible = ref(false);

const onAidaExecPathClick = () => {
  aidaExecPathModalVisible.value = true;
};

const onAidaExecPathModalClose = () => {
  aidaExecPathModalVisible.value = false;
};

const currentUserMis = ref('');

// 获取当前用户MIS
const getCurrentUserMis = async () => {
  try {
    const res = await getCurrentUserInfo();
    console.log('获取当前用户信息:', res);
    // 从用户信息中获取MIS
    currentUserMis.value = res.data.mis || '';
  } catch (error) {
    console.error('获取用户MIS失败:', error);
  }
};

// 初始化
onMounted(() => {
  getCurrentUserMis();
});
</script>

<style lang="scss" scoped>
.session-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow-y: auto;
  background-color: #f0f2f5;
  min-height: calc(100vh - 64px - 48px);
  max-height: calc(100vh - 64px - 48px);
  padding: 20px;

  /* 添加高亮消息的样式 */
  :deep(.highlighted-message) {
    animation: highlight-pulse 2s ease-in-out;
    /* 使用与active-llm-content相同的样式，不再使用单独的边框 */
    font-weight: 500 !important;
    color: #212529 !important;
    box-shadow: 0 0 12px 2px rgba(0, 116, 231, 0.55) !important;
    transform: translateY(-2px) !important;
  }

  @keyframes highlight-pulse {
    0% {
      background-color: rgba(0, 116, 231, 0.15);
    }
    50% {
      background-color: rgba(0, 116, 231, 0.05);
    }
    100% {
      background-color: transparent;
    }
  }

  :deep(.session-info-collapse-container.mtd-collapse) {
    color: #fff;
    border: none;

    .mtd-collapse-item {
      background-color: #fff;

      .mtd-collapse-item-header {
        background-color: #fff;
        // 会话信息区域样式
        .collapse-header {
          display: flex;
          align-items: center;
          gap: 2px;
          height: 20px;

          .header-title {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: 500;
            margin-left: 4px;
          }

          .summary-text {
            margin-left: 4px;
            color: rgba(0, 0, 0, 0.45);
            font-weight: normal;
            font-size: 12px;
          }

          .transfer-tag {
            margin-left: 8px;
            padding: 0 4px;
            height: 18px;
            line-height: 16px;
            font-size: 11px;
            border-radius: 2px;
            font-weight: 500;

            &.warning-tag {
              color: #fa8c16;
              background: #fff7e6;
              border-color: #ffd591;
            }

            &.success-tag {
              color: #52c41a;
              background: #f6ffed;
              border-color: #b7eb8f;
            }
          }
        }
      }

      .mtd-collapse-item-content {
        padding: 0;
      }
    }
  }
}

.main-content {
  display: flex;
  gap: 12px;
  flex: 1;
  min-height: 0;

  .chat-section {
    flex: 0 0 400px;

    /* 自定义tab样式，根据tab数量控制指示条宽度 */
    .custom-tabs {
      :deep(.mtd-tabs-nav-animated .mtd-tabs-bar) {
        width: var(--tab-bar-width) !important;
      }
    }

    .chat-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);

      .chat-tab-pane {
        height: 100%;
        overflow: auto;

        .staff-info {
          height: 100%;
          background-color: #f0f2f5;
          border-radius: 8px;
          padding: 16px;
        }
      }

      :deep(.mtd-card-header) {
        padding: 12px 16px;
        background: #fff;
        min-height: auto;
        height: 40px;
      }

      :deep(.mtd-card-body) {
        flex: 1;
        padding: 0 16px;
        /**
         * 计算卡片body的高度 = 100% - 卡片header的高度
         */
        height: calc(100% - 40px);

        .mtd-tabs {
          height: calc(100% - 30px);
          flex: 1;
          display: flex;
          flex-direction: column;
          min-height: 0;

          .mtd-tabs-nav {
            flex-shrink: 0;
            background: #fff;

            .mtd-tabs-nav-scroll {
              width: 100%;

              .mtd-tabs-nav-animated {
                width: 100%;

                .mtd-tabs-item {
                  flex: 1;
                  margin: 0;

                  .mtd-tabs-item-label {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                  }
                }
              }
            }
          }

          .mtd-tabs-content {
            overflow: visible;
            height: calc(100% - 16px);

            :deep(.mtd-tab-pane) {
              height: calc(100% - 30px);
            }
          }
        }
      }

      .session-chat-wrapper {
        padding: 16px 0;
        height: 100%;
      }
    }
  }

  .right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px; /* 减小间距 */
    overflow: hidden;

    .service-process-wrapper {
      width: 100%;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
      margin-bottom: 8px; /* 控制与下方grid-layout的间距 */
      transition: all 0.3s;
      overflow: hidden;
      max-height: 40px; /* 折叠时的高度，只显示标题栏 */

      &.expanded {
        max-height: 420px; /* 展开后的最大高度，增加以适应横向滚动布局 */

        /* 自定义服务过程中滚动条样式 */
        :deep(.timeline-scroll-container) {
          &::-webkit-scrollbar {
            height: 8px; /* 横向滚动条高度 */
          }

          &::-webkit-scrollbar-track {
            background: #f0f0f0;
            border-radius: 4px;
          }

          &::-webkit-scrollbar-thumb {
            background: #ccc;
            border-radius: 4px;

            &:hover {
              background: #aaa;
            }
          }
        }
      }
    }

    .content-layout {
      flex: 1;
      display: flex;
      gap: 8px;
      overflow: hidden;
      transition: all 0.3s ease;

      &.left-panel-collapsed {
        .model-info-panel {
          margin-left: -8px; // 向左移动，填补间隙
        }
      }

      .signal-info-panel {
        /* 自定义滚动条样式 */
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }

        flex: 2;
        display: flex;
        flex-direction: column;
        gap: 8px;
        overflow-y: auto;
        max-height: calc(100vh - 200px);
        padding-right: 4px;
        position: relative;
        transition: all 0.3s ease;

        /* 展开收起按钮 */
        .panel-toggle-btn {
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          z-index: 10;
          cursor: pointer;
          width: 20px;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #fff;
          border: 1px solid #e8e8e8;
          border-left: none;
          border-radius: 0 4px 4px 0;
          box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
          transition: all 0.3s ease;

          &:hover {
            background: #f5f5f5;
          }

          .toggle-bar {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            .toggle-arrow {
              font-size: 12px;
              color: #1890ff;
              transition: transform 0.3s ease;

              &.collapsed {
                transform: rotate(-90deg);
              }
            }
          }
        }

        /* 收起状态 */
        &.collapsed {
          flex: 0 0 20px;
          min-width: 20px;
          overflow: hidden;
          padding-right: 0;
          background: #fafafa;
          border: 1px solid #f0f0f0;
          border-radius: 8px;
          height: 100%;
          box-shadow: none;
          position: relative;

          .panel-content {
            opacity: 0;
            pointer-events: none;
            margin-left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #fff;
            box-shadow: none;
            border: none;

            .panel-inner-content {
              display: none;
            }
          }

          .panel-toggle-btn {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
            cursor: pointer;
            width: 12px;
            height: 60px;
            background: #fff;
            border: none;
            border-radius: 0 6px 6px 0;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            justify-content: center;

            .toggle-bar {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;

              .toggle-arrow {
                font-size: 12px;
                color: #1890ff;
                transition: transform 0.3s ease;

                &.collapsed {
                  transform: rotate(-90deg);
                }
              }
            }
          }
        }

        .panel-content {
          position: relative;
          margin-left: 16px;
          background: #fff;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
          transition: all 0.3s ease;
          padding: 16px;
          min-height: fit-content;
          height: auto;
          display: flex;
          flex-direction: column;
          gap: 8px;

          &.collapsed {
            opacity: 0;
            transform: translateX(-100%);
            pointer-events: none;
          }
        }

        .record-info-card {
          flex-shrink: 0;

          .mtd-card-header {
            padding: 12px 16px;
            background: #fff;
            min-height: auto;
            height: 40px;
          }

          .mtd-card-body {
            padding: 16px;

            .record-info-card-container {
              display: flex;
              flex-direction: column;
              gap: 8px;

              .record-info-card-content {
                display: flex;
                flex-direction: column;
                gap: 8px;

                &-item {
                  display: flex;
                  align-items: center;
                  gap: 4px;

                  &-label {
                    flex: 0 0 auto;
                    font-size: 14px;
                    font-weight: 500;
                    color: #111925a6;
                    margin-right: 5px;
                  }

                  &-value {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    overflow: hidden;
                    color: #000;

                    &-text {
                      font-size: 14px;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    }

                    .text-show-modal-style {
                      color: #166ff7;
                      font-weight: 700;
                      display: none;
                    }

                    &:hover {
                      .text-show-modal-style {
                        display: flex;
                        align-items: center;
                      }
                    }
                  }
                }
              }
            }
          }
        }

        .info-tabs-card {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;

          .mtd-card-body {
            height: 100%;
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
            padding: 0;
          }
        }
      }

      .model-info-panel {
        flex: 1;
        overflow: hidden;
        transition: all 0.3s ease;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 8px;
        overflow-y: auto;
        max-height: calc(100vh - 200px);
        padding-right: 4px;

        /* 自定义滚动条样式 */
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }
    }
  }
}

.info-section {
  margin-bottom: 8px;
  background: #fff;
  padding: 4px 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    padding-left: 8px;
    margin-bottom: 8px;
  }

  :deep(.mtd-form) {
    .mtd-row {
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .mtd-form-item {
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .mtd-form-item-label {
        padding-right: 4px;
        color: rgba(0, 0, 0, 0.65);
        font-size: 13px;
      }

      .mtd-form-item-content {
        color: rgba(0, 0, 0, 0.85);
        font-size: 13px;

        span {
          font-size: 13px;
        }
      }
    }
  }
}

.text-ellipse {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
  max-width: 100%;
}

// 调整折叠面板的样式
:deep(.mtd-collapse) {
  .mtd-collapse-item {
    .mtd-collapse-header {
      padding: 8px 12px;
    }

    .mtd-collapse-content {
      padding: 0;

      .mtd-tabs-content {
        padding: 0 !important;
      }
    }
  }
}

.mtd-loading-nested {
  flex: 1 1 0;
  display: flex;
  overflow: hidden;

  :deep(.mtd-loading-container) {
    width: 100%;
    gap: 12px;

    .vue-grid-layout {
      height: 100% !important;

      .vue-grid-item.signal-info-panel-grid-item {
        height: 100% !important;
        /**
         * ！！！让两个vue-grid-item项目之间的间距为10px
         * 注释：vue-grid-layout组件采用margin属性控制vue-grid-item项目之间的间距，但会影响vue-grid-layout距离边缘的距离，为了不影响，所以采用margin置为0，手动控制vue-grid-item之间的间距的方案
         */
        padding-right: 10px !important;
        transition: transform 0.3s !important; /* 增加过渡时间，与服务过程面板一致 */
        .signal-info-panel {
          /* 自定义滚动条样式 */
          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;

            &:hover {
              background: #a8a8a8;
            }
          }

          height: 100% !important;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          gap: 12px;
          max-height: calc(100vh - 200px);
          padding-right: 4px;
          height: 100%;

          .record-info-card {
            flex-shrink: 0;

            .mtd-card-header {
              padding: 12px 16px;
              background: #fff;
              min-height: auto;
              height: 40px;
            }

            .mtd-card-body {
              padding: 16px;

              .record-info-card-container {
                display: flex;
                flex-direction: column;
                gap: 8px;

                .record-info-card-content {
                  display: flex;
                  flex-direction: column;
                  gap: 8px;

                  &-item {
                    display: flex;
                    align-items: center;
                    gap: 4px;

                    &-label {
                      flex: 0 0 auto;
                      font-size: 14px;
                      font-weight: 500;
                      color: #111925a6;
                      margin-right: 5px;
                    }

                    &-value {
                      display: flex;
                      align-items: center;
                      gap: 4px;
                      overflow: hidden;
                      color: #000;

                      &-text {
                        font-size: 14px;
                        // 超出省略
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                      }

                      .text-show-modal-style {
                        color: #166ff7;
                        font-weight: 700;
                        display: none;
                      }

                      &:hover {
                        .text-show-modal-style {
                          display: flex;
                          align-items: center;
                        }
                      }
                    }
                  }
                }
              }
            }
          }

          .info-tabs-card {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;

            .mtd-card-body {
              height: 100%;
              flex: 1;
              display: flex;
              flex-direction: column;
              min-height: 0;
              overflow: hidden;
              padding: 0;
            }

            .mtd-tabs {
              height: 100%;
              flex: 1;
              display: flex;
              flex-direction: column;
              min-height: 0;

              .mtd-tabs-nav {
                flex-shrink: 0;
                padding: 0 16px;
                background: #fff;
              }

              .mtd-tabs-content {
                flex: 1;
                overflow-y: auto;
                padding: 16px;
              }
            }

          }
        }

        /**
         * 可拖拽元素
         */
        .vue-resizable-handle {
          z-index: 9999 !important;
          /**
          * 去掉vue-grid-item的背景icon
          */
          background: #a3a6ab !important;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);
          height: 24px !important;
          width: 4px !important;
          border-radius: 2px !important;
          cursor: col-resize !important;
          /**
           * pading 结合 -margin 增大点击区域
           */
          padding: 2px 4px !important;
          margin: -4px !important;
        }
      }

      // 服务过程面板
      .vue-grid-item.service-process-panel-grid-item {
        height: auto !important;
        width: 100% !important;
        margin-bottom: 0 !important; /* 减小到0，完全消除下方间距 */
        transition: height 0.3s !important; /* 增加过渡时间，使动画更平滑 */
        z-index: 10 !important; /* 确保服务过程面板在上层 */

        .vue-resizable-handle {
          display: none;
        }
      }

      .vue-grid-item.model-info-panel-grid-item {
        height: 100% !important;
        transition: transform 0.3s !important; /* 增加过渡时间，与服务过程面板一致 */
        .model-info-panel {
          height: 100% !important;
        }

        .vue-resizable-handle {
          display: none;
        }
      }

      /**
       * 拖拽拉伸时，占位符样式：保持无色
       */
      .vue-grid-item.vue-grid-placeholder {
        background: #f0f2f5 !important;
        height: 100% !important;
      }
    }
  }
}
</style>
