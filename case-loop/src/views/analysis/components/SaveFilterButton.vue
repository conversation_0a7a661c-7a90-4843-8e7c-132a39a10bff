<template>
  <div class="save-filter-button">
    <a-button class="custom-button" @click="handleSaveClick">
      <template #icon><SaveOutlined /></template>
      保存筛选条件
    </a-button>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { SaveOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  name: 'SaveFilterButton',
  components: {
    SaveOutlined
  },
  props: {
    currentFilter: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ['save'],
  setup(props, { emit }) {
    // 处理保存按钮点击
    const handleSaveClick = () => {
      emit('save');
    };
    
    return {
      handleSaveClick
    };
  }
});
</script>

<style scoped>
.save-filter-button {
  display: inline-block;
}

.custom-button {
  border-radius: 8px;
  transition: all 0.3s;
}

.custom-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style> 