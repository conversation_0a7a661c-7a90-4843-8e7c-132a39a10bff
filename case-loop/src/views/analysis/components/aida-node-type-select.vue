<template>
  <mtd-select
    :model-value="selectedNodeTypes"
    :clearable="true"
    :multiple="true"
    :max-count="1"
    placeholder="节点类型筛选"
    @change="onNodeTypesChange"
  >
    <template #tag="{ option }">
      <mtd-tag closable @close="onTagClose(option.value)">
        {{ getNodeName(option.value) }}
      </mtd-tag>
    </template>
    <mtd-option v-for="node in RuleTaskNodeList" :key="node.value" :value="node.value">
      <div class="aida-node-type-select-option-container">
        <img :src="node.icon" style="height: 17px; width: 17px" />
        <div>
          {{ node.label }}
        </div>
      </div>
    </mtd-option>
  </mtd-select>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { RuleTaskNodeMap, TaskRuleBusinessType, RuleTaskNodeList } from '@/constants/aida-rule-task';

const props = defineProps<{
  nodeTypes?: Array<TaskRuleBusinessType> | null;
}>();

const selectedNodeTypes = ref<Array<TaskRuleBusinessType>>([]);

watch(
  () => props.nodeTypes,
  () => {
    if (!props.nodeTypes) return;
    selectedNodeTypes.value = props.nodeTypes;
  },
  { immediate: true }
);

const emit = defineEmits<{
  (e: 'change', nodeTypes: Array<TaskRuleBusinessType>): void;
}>();

const onNodeTypesChange = (value: Array<TaskRuleBusinessType>) => {
  emit('change', value);
};

const getNodeName = (nodeType: TaskRuleBusinessType) => {
  return RuleTaskNodeMap[nodeType].name;
};

const onTagClose = (nodeType: TaskRuleBusinessType) => {
  selectedNodeTypes.value = selectedNodeTypes.value.filter((item) => item !== nodeType);
  onNodeTypesChange(selectedNodeTypes.value);
};
</script>

<style lang="scss" scoped>
:deep(.aida-node-type-select-option-container),
:deep(.aida-node-type-select-label-container) {
  display: flex;
  gap: 3px;
  align-items: center;
}
</style>
