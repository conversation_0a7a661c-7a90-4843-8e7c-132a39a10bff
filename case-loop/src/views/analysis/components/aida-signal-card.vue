<template>
  <div class="aida-signal-card">
    <!-- 卡片标题 -->
    <div class="card-header">
      <div class="card-title-section">
        <div style="display: flex; align-items: center; gap: 4px">
          信号信息
        </div>
        <!-- 展开收起按钮 -->
        <a-button
            type="text"
            size="small"
            @click="toggleExpanded"
            class="expand-toggle-btn"
        >
          {{ isExpanded ? '收起' : '展开' }}
          <span class="arrow-icon" :class="{ 'expanded': isExpanded }">▼</span>
        </a-button>
      </div>
    </div>

    <!-- 信号信息卡片内容 -->
    <div class="record-info-card-container" v-if="hasSignalData">
      <!-- 执行后信号 -->
      <div v-if="displaySignalAfterData.length" class="signal-section">
        <div class="signal-list">
          <div class="signal-item" v-for="(item, index) in displaySignalAfterData" :key="`after-${index}`">
            <span class="signal-label">{{ item.signalName }}:</span>
            <span class="signal-value" :class="{ 'signal-changed': item.isChange }">{{
                item.signalValue || '--'
              }}</span>
            <div class="signal-actions">
              <!-- 变化标识 -->
              <ArrowUpOutlined
                  v-if="item.isChange"
                  style="color: #52c41a; font-size: 16px"
                  title="信号值已变化"
              />
              <text-show-modal
                  v-if="item.signalValue"
                  class="text-show-modal-style"
                  :value="item.signalValue"
                  :title="item.signalName"
                  :read-only="true"
                  placeholder=""
              />
              <!-- 收藏按钮 -->
              <mtd-icon
                  :name="item.isFavorite ? 'mtdicon-star-fill' : 'mtdicon-star-o'"
                  :style="{
                  color: item.isFavorite ? '#faad14' : '#8c8c8c',
                  cursor: 'pointer',
                  fontSize: '18px',
                  transition: 'all 0.2s ease'
                }"
                  class="mtdicon mtdicon-star-o"
                  :title="item.isFavorite ? '取消收藏' : '收藏'"
                  @click="toggleFavorite('after', index, item)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <a-empty description="暂无信号信息"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, ref} from 'vue';
import TextShowModal from '@/components/TextShowModal.vue';
import {changeSignalFavoriteApi} from "@/api/workbench-session";
import {ArrowUpOutlined} from '@ant-design/icons-vue';

// 定义 props
const props = defineProps<{
  queryMessageLlmInfo?: any;
}>();


// 展开状态
const isExpanded = ref(false);

// 计算执行前信号数据
const signalBeforeData = computed(() => {
  if (!props.queryMessageLlmInfo?.allSignal?.signalBeforeList) return [];
  return props.queryMessageLlmInfo.allSignal.signalBeforeList;
});

// 计算执行后信号数据
const signalAfterData = computed(() => {
  if (!props.queryMessageLlmInfo?.allSignal?.signalAfterList) return [];
  return props.queryMessageLlmInfo.allSignal.signalAfterList;
});


// 计算要显示的执行后信号数据（根据展开状态）
const displaySignalAfterData = computed(() => {
  if (isExpanded.value) {
    return signalAfterData.value;
  }
  return signalAfterData.value.filter(item => item.isFavorite);
});

// 判断是否有信号数据
const hasSignalData = computed(() => {
  return signalBeforeData.value.length > 0 || signalAfterData.value.length > 0;
});

// 切换展开状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};

// 切换收藏状态
const toggleFavorite = async (type: 'before' | 'after', index: number, signal: any) => {
  const {workspaceId, applicationId, applicationVersion} = props.queryMessageLlmInfo.messageLevelAidaAppInfo || {};

  // 切换收藏状态
  signal.isFavorite = !signal.isFavorite;

  try {
    await changeSignalFavoriteApi({
      platformWorkspace: workspaceId,
      appId: applicationId,
      appVersionId: applicationVersion,
      isFavorite: signal.isFavorite,
      signalKey: signal.signalKey,
    });
  } catch (error) {
    console.error("收藏信号失败", error)
  }

  // 这里可以添加API调用来保存收藏状态
  console.log(`${signal.isFavorite ? '收藏' : '取消收藏'}信号:`, signal.signalName);
};


</script>

<style scoped lang="scss">
.aida-signal-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #ebedf0;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  }


  .card-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 8px 8px 0 0;
    font-weight: 500;
    font-size: 16px;
    color: #262626;

    .card-title-section {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;

      .title-content {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-icon {
          font-size: 16px;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }
      }

      .expand-toggle-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 12px;
        border: none;
        background: transparent;
        color: #1890ff;
        font-size: 12px;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba(24, 144, 255, 0.1);
        }

        .arrow-icon {
          font-size: 10px;
          transition: transform 0.2s ease;

          &.expanded {
            transform: rotate(180deg);
          }
        }
      }
    }
  }
}

.record-info-card-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 20px;

  .signal-section {
    .signal-section-title {
      font-size: 14px;
      font-weight: 600;
      color: #111925;
      margin-bottom: 8px;
      padding-left: 8px;
      border-left: 3px solid #fa8c16;
    }

    .signal-list {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .signal-item {
        display: flex;
        align-items: center;
        gap: 12px;
        min-height: 32px;
        padding: 4px 0;

        .signal-label {
          flex: 0 0 auto;
          font-size: 14px;
          font-weight: 500;
          color: #111925a6;
          min-width: 100px;
        }

        .signal-value {
          flex: 1;
          font-size: 14px;
          color: #000;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          &.signal-changed {
            color: #52c41a;
            font-weight: 500;
          }
        }

        .signal-actions {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-shrink: 0;

          .text-show-modal-style {
            color: #166ff7;
            font-weight: 700;
            display: none;
          }

          // 确保收藏按钮始终可见
          .favorite-star-icon,
          .unfavorite-star-icon {
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
          }
        }

        &:hover {
          .text-show-modal-style {
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
}

.expand-toggle-btn {
  color: #1890ff !important;
  padding: 2px 8px !important;
  font-size: 12px !important;
  height: 24px !important;
  line-height: 20px !important;
  border-radius: 4px !important;

  &:hover {
    background-color: rgba(24, 144, 255, 0.1) !important;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}


</style>
