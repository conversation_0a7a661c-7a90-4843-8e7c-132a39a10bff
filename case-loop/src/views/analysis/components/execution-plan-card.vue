<template>
  <div class="execution-plan-card">
    <!-- 卡片标题 -->
    <div class="card-header">
      <div class="card-title-section">
        <div style="display: flex; align-items: center; gap: 4px">
          方案执行
        </div>
        <!-- 展开收起按钮 -->
        <a-button
            type="text"
            size="small"
            @click="toggleExpanded"
            class="expand-toggle-btn"
        >
          {{ isExpanded ? '收起' : '展开' }}
          <span class="arrow-icon" :class="{ 'expanded': isExpanded }">▼</span>
        </a-button>
      </div>
    </div>

    <!-- 方案执行卡片内容 -->
    <div class="record-info-card-container" v-if="executionData.length && isExpanded">
      <!-- 方案执行内容 -->
      <div class="record-info-card-content">
        <div class="record-info-card-content-item" v-for="(item, index) in executionData" :key="index">
          <div class="record-info-card-content-item-label">{{ item.displayName }}:</div>
          <div class="record-info-card-content-item-value text-show-modal-contaniner">
            <div class="record-info-card-content-item-value-text">{{ item.displayContent }}</div>
            <text-show-modal
                v-if="item.displayContent"
                class="text-show-modal-style"
                :value="item.displayContent"
                :title="item.displayName"
                :read-only="true"
                placeholder=""
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!executionData.length" class="empty-state">
      <a-empty description="暂无方案执行信息"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, ref} from 'vue';
import TextShowModal from '@/components/TextShowModal.vue';
import { AppstoreOutlined, UpOutlined, DownOutlined } from '@ant-design/icons-vue';

// 定义 props
const props = defineProps<{
  curMessageLlmInfo?: any;
}>();

// 展开状态
const isExpanded = ref(false);

// 切换展开状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};

// 计算方案执行信息（不包含输入、输出字段）
const executionData = computed(() => {
  if (!props.curMessageLlmInfo || !props.curMessageLlmInfo.detail || !props.curMessageLlmInfo.detail.basicInfo) return [];
  return props.curMessageLlmInfo.detail.basicInfo.filter(
      (item: any) => item.displayName !== '输入' && item.displayName !== '输出'
  );
});
</script>

<style scoped lang="scss">
.execution-plan-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #ebedf0;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  }

  .card-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 8px 8px 0 0;
    font-weight: 500;
    font-size: 16px;
    color: #262626;
    .card-title-section {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;

      .title-content {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-icon {
          font-size: 16px;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }
      }

      .expand-toggle-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 12px;
        border: none;
        background: transparent;
        color: #1890ff;
        font-size: 12px;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba(24, 144, 255, 0.1);
        }

        .arrow-icon {
          font-size: 10px;
          transition: transform 0.2s ease;

          &.expanded {
            transform: rotate(180deg);
          }
        }
      }
    }
  }
}

.record-info-card-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px 20px;

  .record-info-card-content {
    display: flex;
    flex-direction: column;
    gap: 8px;

    &-item {
      display: flex;
      align-items: center;
      gap: 4px;

      &-label {
        flex: 0 0 auto;
        font-size: 14px;
        font-weight: 500;
        color: #111925a6;
        margin-right: 5px;
      }

      &-value {
        display: flex;
        align-items: center;
        gap: 4px;
        overflow: hidden;
        color: #000;

        &-text {
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .text-show-modal-style {
          color: #166ff7;
          font-weight: 700;
          display: none;
        }

        &:hover {
          .text-show-modal-style {
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
}

.expand-toggle-btn {
  color: #1890ff !important;
  padding: 2px 8px !important;
  font-size: 12px !important;
  height: 24px !important;
  line-height: 20px !important;
  border-radius: 4px !important;

  &:hover {
    background-color: rgba(24, 144, 255, 0.1) !important;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}

.text-show-modal-contaniner {
  display: flex;
  align-items: center;
  width: 100%;

  .text-show-modal-style {
    margin-left: 8px;
    margin-right: 8px;
    color: #166ff7;
    font-weight: 700;
    display: none;
  }

  &:hover {
    .text-show-modal-style {
      display: flex;
      align-items: center;
    }
  }
}
</style>
