<template>
  <div class="chat-queue-end">
    <div class="message" v-html="formattedMessage"></div>
  </div>
</template>

<script setup lang="ts">
import type { ComputedRef } from 'vue';
import { computed } from 'vue';

interface QueueEndData {
  content: string;
}

interface OriginMessage {
  data: string;
  senderType: string;
  type: string;
}

const props = defineProps({
  data: {
    type: String,
    required: true,
  },
});

const parsedMessage: ComputedRef<string> = computed(() => {
  try {
    const originMessage = JSON.parse(props.data) as OriginMessage;
    const contentData = JSON.parse(originMessage.data) as QueueEndData;
    return contentData.content;
  } catch (error) {
    console.error('解析排队结束消息失败:', error);
    return '';
  }
});

const formattedMessage: ComputedRef<string> = computed(() => {
  if (!parsedMessage.value) return '';
  return parsedMessage.value
    .split('\n')
    .map((line) => `${line}`)
    .join('');
});
</script>

<style lang="scss" scoped>
.chat-queue-end {
  padding: 12px 16px;
  background-color: #ffcb00;
  border-radius: 10px 10px 0px 10px;

  .message {
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    line-height: 1.5;

    :deep(p) {
      margin: 0;
      padding: 4px 0;
    }
  }
}
</style>
