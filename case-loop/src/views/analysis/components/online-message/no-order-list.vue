<template>
  <div class="no-order-list-container">
    <mtd-card shadow="hover" :border="false" class="card" :title="listData.guideText">
      <!-- 券包列表 -->
      <div v-if="listData.noOrderList && listData.noOrderList.length > 0">
        <div
          v-for="(section, index) in listData.noOrderList"
          :key="`section-${section.id || index}`"
          class="list-section"
        >
          <div class="list-content">
            <div class="section-title">
              <span>{{ section.name }}</span>
              <span v-if="section.recordType" class="record-type">{{ section.recordType }}</span>
            </div>
            <div
              v-for="(item, itemIndex) in section.list"
              :key="`item-${itemIndex}`"
              class="list-item"
            >
              <div class="item-header">
                <span class="item-title">{{ item.orderTitle }}</span>
                <span class="item-price">{{ item.firstOrderContent }}</span>
              </div>
              <div class="item-info">
                <div class="info-row">
                  <span class="label">订单号：</span>
                  <span class="value">{{ item['113634'] || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">购买时间：</span>
                  <span class="value">{{ item['113635'] || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">有效期至：</span>
                  <span class="value">{{ item['113633'] || '-' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态提示 -->
      <div v-else-if="dataLoaded" class="empty-state">
        <div class="empty-icon">📋</div>
        <div class="empty-text">暂无订单数据</div>
      </div>

      <!-- 气泡提示 -->
      <div v-if="listData.bubbleText" class="bubble-text">
        {{ listData.bubbleText }}
      </div>

      <!-- 无选项按钮 -->
      <div v-if="listData.noSelectButtonText" class="no-select-button">
        <mtd-button size="small" type="primary">{{ listData.noSelectButtonText }}</mtd-button>
      </div>
    </mtd-card>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';

// 定义接口类型
interface NoOrderListItem {
  orderTitle: string;
  firstOrderContent: string;
  secondOrderContent: string;
  forthOrderContent: string;
  orderImageUrl: string;
  [key: string]: string;
}

interface NoOrderSection {
  id: number;
  name: string;
  recordType?: string;
  list: NoOrderListItem[];
}

interface NoOrderListData {
  guideText: string;
  bubbleText: string;
  noSelectButtonText: string;
  noOrderList: NoOrderSection[];
  noSelect: string;
  isPopUp: boolean;
  typicalQuestionId: number;
  typicalQuestionName: string;
  displayType: string;
  extendInfo: {
    selectType: string;
    buttonTrigger: string;
    knowledgeId: string;
    knowledgeName: string;
  };
}

const props = defineProps<{
  data: string;
}>();

const listData = ref<NoOrderListData>({} as NoOrderListData);
const dataLoaded = ref(false);
const parseError = ref<Error | null>(null);

// 解析数据
const parseListData = () => {
  try {
    if (!props.data) {
      listData.value = {} as NoOrderListData;
      dataLoaded.value = true;
      return;
    }

    const originMessage = JSON.parse(props.data);
    const parsedData = JSON.parse(originMessage.data);
    listData.value = parsedData;
    dataLoaded.value = true;
    parseError.value = null;
  } catch (e) {
    console.error('解析列表数据失败', e);
    parseError.value = e as Error;
    dataLoaded.value = true;
  }
};

// 监听数据变化
watch(() => props.data, parseListData, { immediate: true });

// 组件挂载时解析数据
onMounted(() => {
  if (!dataLoaded.value) {
    parseListData();
  }
});
</script>

<style lang="scss" scoped>
.no-order-list-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;

  .card {
    width: 100%;
  }

  .list-section {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      font-size: 15px;
      font-weight: 500;
      color: #333;
      display: flex;
      align-items: center;

      .record-type {
        margin-left: 8px;
        font-size: 12px;
        color: #666;
        background-color: #f5f5f5;
        padding: 2px 6px;
        border-radius: 4px;
      }
    }

    .list-content {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .list-item {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 12px;
        transition: all 0.3s ease;

        &:hover {
          background: #f0f2f5;
          transform: translateY(-2px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          padding-bottom: 8px;
          border-bottom: 1px solid #e8e8e8;

          .item-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            flex: 1;
            margin-right: 16px;
            word-break: break-word;
          }

          .item-price {
            font-size: 15px;
            color: #f60;
            font-weight: 500;
            white-space: nowrap;
          }
        }

        .item-info {
          .info-row {
            display: flex;
            margin-bottom: 4px;
            font-size: 13px;
            line-height: 1.5;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              color: #666;
              margin-right: 8px;
              white-space: nowrap;
            }

            .value {
              color: #333;
              word-break: break-all;
            }
          }
        }
      }
    }
  }

  .bubble-text {
    margin-top: 16px;
    padding: 8px 12px;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 4px;
    color: #1890ff;
    font-size: 13px;
  }

  .no-select-button {
    margin-top: 10px;
    margin-bottom: 10px;
    text-align: center;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 0;
    color: #999;

    .empty-icon {
      font-size: 32px;
      margin-bottom: 8px;
    }

    .empty-text {
      font-size: 14px;
    }
  }
}

@media (max-width: 576px) {
  .no-order-list-container {
    .list-section {
      .list-content {
        .list-item {
          .item-header {
            flex-direction: column;
            align-items: flex-start;

            .item-title {
              margin-right: 0;
              margin-bottom: 4px;
            }
          }
        }
      }
    }
  }
}
</style>
