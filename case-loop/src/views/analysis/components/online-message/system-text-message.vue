<template>
  <div class="system-text-message-wrapper" :class="{ 'active': isActive, 'active-llm': isActiveLlm }">
    <div v-html="message" />
    <slot name="additional-content"></slot>
  </div>
</template>

<script setup lang="ts">
defineProps({
  message: {
    type: String,
    required: true,
  },
  isActive: {
    type: Boolean,
    default: false,
  },
  isActiveLlm: {
    type: Boolean,
    default: false,
  },
});
</script>

<style lang="scss" scoped>
.system-text-message-wrapper {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  word-break: break-all;
  white-space: pre-line;
  background-color: #ffffff;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
  padding: 12px 16px;
  position: relative;
  box-sizing: border-box;
  transition: all 0.3s ease;

  // 可操作消息的样式
  &.active {
    cursor: pointer;
    border-color: #dee2e6;

    &:hover {
      color: #212529;
      box-shadow: 0 0 12px 2px rgba(0, 116, 231, 0.55);
      transform: translateY(-2px);
    }
  }

  // 当前选中的LLM消息样式
  &.active-llm {
    font-weight: 500;
    color: #212529;
    box-shadow: 0 0 12px 2px rgba(0, 116, 231, 0.55);
    transform: translateY(-2px);

    &:hover {
      cursor: pointer;
      box-shadow: 0 0 12px 2px rgba(0, 116, 231, 0.55);
    }
  }
}
</style>
