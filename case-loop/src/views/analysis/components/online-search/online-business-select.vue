<template>
  <a-select v-model:value="localValue" placeholder="请选择业务" :allowClear="true">
    <a-select-option v-for="option in businessOptions" :key="option.value" :value="option.value">
      {{ option.label }}
    </a-select-option>
  </a-select>
</template>

<script lang="ts" setup>
import { computed, ref, watch, getCurrentInstance } from 'vue';

const props = defineProps<{
  modelValue?: string; // v-model 传递的值
  formData?: Record<string, any>; // 搜索表单数据
  sceneRelationConfig?: {
    sceneInfos: Array<{
      sceneId: number;
      sceneName: string;
      buInfos: Array<{
        buId: number;
        buName: string;
        buCode: string;
        subBuInfos: Array<{
          subBuId: number;
          subBuName: string;
          subBuCode: string;
          taskKeyInfos: Array<{
            taskKey: string;
          }>;
        }>;
      }>;
      workspaceAppList: Array<{
        workspaceId: string;
        workspaceName: string;
        applicationList?: Array<{
          applicationId: string;
          applicationName: string;
          applicationType: string;
          robotList?: Array<{
            robotId: string;
            robotName: string;
          }>;
        }>;
      }>;
    }>;
  } | null;
}>();

const emit = defineEmits<{
  (e: 'change', value: string, displayName?: string): void;
}>();

const localValue = ref(props.modelValue || '');

// 监听 props.modelValue 的变化，更新 localValue
watch(
  () => props.modelValue,
  (newValue) => {
    localValue.value = newValue || ''; // 更新本地值
  }
);

// 监听 props.formData.bu 的变化，更新 localValue 并触发 change 事件
watch(
  () => props.formData?.bu,
  (newValue, oldValue) => {

    // 只有当新值与旧值不同时才更新本地值和触发 change 事件
    if (newValue !== oldValue && newValue !== localValue.value) {
      localValue.value = newValue || ''; // 更新本地值

      // 如果新值不为空，手动触发 change 事件
      if (newValue) {
        // 获取业务的显示名称
        let displayName = newValue;
        if (props.sceneRelationConfig && props.sceneRelationConfig.sceneInfos) {
          for (const scene of props.sceneRelationConfig.sceneInfos) {
            if (scene.buInfos) {
              const buInfo = scene.buInfos.find((bu) => String(bu.buId) === String(newValue));
              if (buInfo) {
                displayName = buInfo.buName;
                break;
              }
            }
          }
        }

        emit('change', newValue, displayName); // 触发 change 事件
      }
    }
  },
  { immediate: true } // 立即触发一次，确保初始化时也能正确处理
);

watch(
  () => localValue.value,
  (newValue) => {

    // 获取业务的显示名称
    let displayName = newValue;
    if (newValue && props.sceneRelationConfig && props.sceneRelationConfig.sceneInfos) {
      for (const scene of props.sceneRelationConfig.sceneInfos) {
        if (scene.buInfos) {
          const buInfo = scene.buInfos.find((bu) => String(bu.buId) === String(newValue));
          if (buInfo) {
            displayName = buInfo.buName;
            break;
          }
        }
      }
    }

    emit('change', newValue, displayName); // 更新父组件的值，并传递显示名称
  }
);

// 从 sceneRelationConfig 获取业务选项
const businessOptions = computed(() => {
  console.log('[BusinessSelect] 开始计算业务选项');
  console.log('[BusinessSelect] props.sceneRelationConfig:', props.sceneRelationConfig);
  console.log('[BusinessSelect] props.formData:', props.formData);
  console.log('[BusinessSelect] 组件是否挂载:', getCurrentInstance());

  if (!props.sceneRelationConfig || !props.sceneRelationConfig.sceneInfos) {
    console.log('[BusinessSelect] sceneRelationConfig 或 sceneInfos 为空');
    return [];
  }

  console.log('[BusinessSelect] sceneInfos 数量:', props.sceneRelationConfig.sceneInfos.length);

  const businesses = new Map<string, string>();

  // 如果有选择的场景，只从该场景中获取业务信息
  if (props.formData?.scene) {
    console.log('[BusinessSelect] 已选择场景:', props.formData.scene);
    const selectedScene = props.sceneRelationConfig.sceneInfos.find(
      (scene) => String(scene.sceneId) === String(props.formData.scene)
    );
    console.log('[BusinessSelect] 找到的场景:', selectedScene);
    if (selectedScene && selectedScene.buInfos && Array.isArray(selectedScene.buInfos)) {
      console.log('[BusinessSelect] 场景中的业务数量:', selectedScene.buInfos.length);
      selectedScene.buInfos.forEach((business) => {
        // 使用 buId 作为值，buName 作为标签
        businesses.set(String(business.buId), business.buName);
      });
    }
  } else {
    // 如果没有选择场景，从所有场景中收集业务信息作为初始化可选值
    console.log('[BusinessSelect] 未选择场景，收集所有业务');
    props.sceneRelationConfig.sceneInfos.forEach((scene, index) => {
      console.log(`[BusinessSelect] 处理场景${index + 1}:`, scene.sceneName, '业务数量:', scene.buInfos ? scene.buInfos.length : 0);
      if (scene.buInfos && Array.isArray(scene.buInfos)) {
        scene.buInfos.forEach((business) => {
          // 使用 buId 作为值，buName 作为标签
          businesses.set(String(business.buId), business.buName);
          console.log(`[BusinessSelect] 添加业务: ${business.buName} (${business.buId})`);
        });
      }
    });
  }

  const options = Array.from(businesses.entries()).map(([value, label]) => ({
    value,
    label: label || value,
  }));

  console.log('[BusinessSelect] 最终业务选项:', options, '总数:', options.length);
  return options;
});

// 存储值到名称的映射，供外部访问
// 定义为响应式对象，便于外部访问
const buValueMap = ref<Record<string, string>>({});

// 对外暴露获取名称的方法
const getBusinessName = (value: string): string => {
  // 先从映射中查找
  if (buValueMap.value[value]) {
    return buValueMap.value[value];
  }

  // 如果映射中没有，尝试从场景配置中查找
  if (props.sceneRelationConfig && props.sceneRelationConfig.sceneInfos) {
    for (const scene of props.sceneRelationConfig.sceneInfos) {
      if (scene.buInfos) {
        const buInfo = scene.buInfos.find((bu) => String(bu.buId) === String(value));
        if (buInfo) {
          // 将找到的名称添加到映射中
          buValueMap.value[value] = buInfo.buName;
          return buInfo.buName;
        }
      }
    }
  }

  // 如果都没有找到，返回原始值
  return value;
};

// 将方法挂载到组件实例上
defineExpose({
  getBusinessName,
});

// 监听场景变化，当场景变化时清空业务选择
watch(
  () => props.formData?.scene,
  (newValue, oldValue) => {
    if (newValue !== oldValue && !props.isApplyingSavedFilter) {
      localValue.value = '';
    }
  }
);

watch(
  () => props.sceneRelationConfig,
  (newConfig) => {

    // 更新值到名称的映射
    if (newConfig && newConfig.sceneInfos) {
      newConfig.sceneInfos.forEach((scene) => {
        if (scene.buInfos) {
          scene.buInfos.forEach((bu) => {
            buValueMap.value[String(bu.buId)] = bu.buName;
          });
        }
      });
    }
  },
  { deep: true }
);
</script>

<style lang="scss" scoped></style>
