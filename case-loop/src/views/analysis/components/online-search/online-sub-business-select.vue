<template>
  <a-select v-model:value="localValue" placeholder="请选择子业务" :allowClear="true" :disabled="!hasSelectedBusiness">
    <a-select-option v-for="option in subBusinessOptions" :key="option.value" :value="option.value">
      {{ option.label }}
    </a-select-option>
  </a-select>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';

const props = defineProps<{
  modelValue?: string; // v-model 传递的值
  formData?: Record<string, any>; // 搜索表单数据
  sceneRelationConfig?: {
    sceneInfos: Array<{
      sceneId: number;
      sceneName: string;
      buInfos: Array<{
        buId: number;
        buName: string;
        buCode: string;
        subBuInfos: Array<{
          subBuId: number;
          subBuName: string;
          subBuCode: string;
          taskKeyInfos: Array<{
            taskKey: string;
          }>;
        }>;
      }>;
      workspaceAppList: Array<{
        workspaceId: string;
        workspaceName: string;
        applicationList?: Array<{
          applicationId: string;
          applicationName: string;
          applicationType: string;
          robotList?: Array<{
            robotId: string;
            robotName: string;
          }>;
        }>;
      }>;
    }>;
  } | null;
  // 是否正在应用常用筛选，用于控制业务变化时是否清空子业务字段
  isApplyingSavedFilter?: boolean;
}>();

const emit = defineEmits<{
  (e: 'change', value: string, displayName?: string): void;
}>();

const localValue = ref(props.modelValue || '');

// 监听 props.modelValue 的变化，更新 localValue
watch(
  () => props.modelValue,
  (newValue) => {
    console.log('[子业务选择] props.modelValue 变化:', newValue);
    localValue.value = newValue || ''; // 更新本地值
  }
);

// 监听 props.formData.subBu 的变化，更新 localValue 并触发 change 事件
watch(
  () => props.formData?.subBu,
  (newValue, oldValue) => {
    // 只有当新值与旧值不同时才更新本地值和触发 change 事件
    if (newValue !== oldValue && newValue !== localValue.value) {
      localValue.value = newValue || ''; // 更新本地值

      // 如果新值不为空，手动触发 change 事件
      if (newValue) {
        // 获取子业务的显示名称
        let displayName = newValue;
        if (props.sceneRelationConfig && props.sceneRelationConfig.sceneInfos && props.formData?.bu) {
          for (const scene of props.sceneRelationConfig.sceneInfos) {
            if (scene.buInfos) {
              const buInfo = scene.buInfos.find((bu) => String(bu.buId) === String(props.formData?.bu));
              if (buInfo && buInfo.subBuInfos) {
                const subBuInfo = buInfo.subBuInfos.find((subBu) => String(subBu.subBuId) === String(newValue));
                if (subBuInfo) {
                  displayName = subBuInfo.subBuName;
                  break;
                }
              }
            }
          }
        }

        emit('change', newValue, displayName); // 触发 change 事件
      }
    }
  },
  { immediate: true } // 立即触发一次，确保初始化时也能正确处理
);

watch(
  () => localValue.value,
  (newValue) => {
    // 获取子业务的显示名称
    let displayName = newValue;
    if (newValue && props.sceneRelationConfig && props.sceneRelationConfig.sceneInfos && props.formData?.bu) {
      for (const scene of props.sceneRelationConfig.sceneInfos) {
        if (scene.buInfos) {
          const buInfo = scene.buInfos.find((bu) => String(bu.buId) === String(props.formData?.bu));
          if (buInfo && buInfo.subBuInfos) {
            const subBuInfo = buInfo.subBuInfos.find((subBu) => String(subBu.subBuId) === String(newValue));
            if (subBuInfo) {
              displayName = subBuInfo.subBuName;
              break;
            }
          }
        }
      }
    }

    emit('change', newValue, displayName); // 更新父组件的值，并传递显示名称
  }
);

// 计算是否已选择业务
const hasSelectedBusiness = computed(() => {
  return !!props.formData?.bu;
});

// 从 sceneRelationConfig 获取子业务选项
const subBusinessOptions = computed(() => {
  if (!props.sceneRelationConfig || !props.sceneRelationConfig.sceneInfos) {
    return [];
  }

  const subBusinesses = new Map<string, string>();

  // 如果已选择业务，只显示该业务下的子业务
  if (hasSelectedBusiness.value) {
    const selectedBuId = props.formData?.bu;

    // 从所有场景中查找匹配的业务，并收集其子业务信息
    props.sceneRelationConfig.sceneInfos.forEach((scene) => {
      if (scene.buInfos && Array.isArray(scene.buInfos)) {
        const matchedBusiness = scene.buInfos.find((business) => String(business.buId) === selectedBuId);

        if (matchedBusiness && matchedBusiness.subBuInfos && Array.isArray(matchedBusiness.subBuInfos)) {
          matchedBusiness.subBuInfos.forEach((subBusiness) => {
            // 使用 subBuId 作为值，subBuName 作为标签
            subBusinesses.set(String(subBusiness.subBuId), subBusiness.subBuName);
          });
        }
      }
    });
  } else {
    // 如果没有选择业务，从所有场景中收集所有子业务信息作为初始化可选值
    props.sceneRelationConfig.sceneInfos.forEach((scene) => {
      if (scene.buInfos && Array.isArray(scene.buInfos)) {
        scene.buInfos.forEach((business) => {
          if (business.subBuInfos && Array.isArray(business.subBuInfos)) {
            business.subBuInfos.forEach((subBusiness) => {
              // 使用 subBuId 作为值，subBuName 作为标签
              subBusinesses.set(String(subBusiness.subBuId), subBusiness.subBuName);
            });
          }
        });
      }
    });
  }

  const options = Array.from(subBusinesses.entries()).map(([value, label]) => ({
    value,
    label: label || value,
  }));

  console.log('[SubBusinessSelect] subBusinessOptions:', options, 'selectedBu:', props.formData?.bu, 'totalOptions:', options.length);
  return options;
});

// 存储值到名称的映射，供外部访问
// 定义为响应式对象，便于外部访问
const subBuValueMap = ref<Record<string, string>>({});

// 对外暴露获取名称的方法
const getSubBusinessName = (value: string): string => {
  // 先从映射中查找
  if (subBuValueMap.value[value]) {
    return subBuValueMap.value[value];
  }

  // 如果映射中没有，尝试从场景配置中查找
  if (props.sceneRelationConfig && props.sceneRelationConfig.sceneInfos && props.formData?.bu) {
    for (const scene of props.sceneRelationConfig.sceneInfos) {
      if (scene.buInfos) {
        const buInfo = scene.buInfos.find((bu) => String(bu.buId) === String(props.formData?.bu));
        if (buInfo && buInfo.subBuInfos) {
          const subBuInfo = buInfo.subBuInfos.find((subBu) => String(subBu.subBuId) === String(value));
          if (subBuInfo) {
            // 将找到的名称添加到映射中
            subBuValueMap.value[value] = subBuInfo.subBuName;
            return subBuInfo.subBuName;
          }
        }
      }
    }
  }

  // 如果都没有找到，返回原始值
  return value;
};

// 将方法挂载到组件实例上
defineExpose({
  getSubBusinessName,
});

watch(
  () => props.formData?.bu,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      // 只有在不是应用保存的筛选条件时才清空子业务选择
      if (!props.isApplyingSavedFilter) {
        localValue.value = '';
      }

      // 更新值到名称的映射
      if (newValue && props.sceneRelationConfig && props.sceneRelationConfig.sceneInfos) {
        for (const scene of props.sceneRelationConfig.sceneInfos) {
          if (scene.buInfos) {
            const buInfo = scene.buInfos.find((bu) => String(bu.buId) === String(newValue));
            if (buInfo && buInfo.subBuInfos) {
              buInfo.subBuInfos.forEach((subBu) => {
                subBuValueMap.value[String(subBu.subBuId)] = subBu.subBuName;
              });
            }
          }
        }
      }
    }
  }
);
</script>

<style lang="scss" scoped></style>
