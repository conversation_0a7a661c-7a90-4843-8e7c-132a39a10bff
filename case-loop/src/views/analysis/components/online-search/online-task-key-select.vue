<template>
  <a-select v-model:value="localValue" placeholder="请选择触发task" :allowClear="true" :disabled="!hasSelectedSubBusiness">
    <a-select-option v-for="option in taskKeyOptions" :key="option.value" :value="option.value">
      {{ option.label }}
    </a-select-option>
  </a-select>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';

const props = defineProps<{
  modelValue?: string; // v-model 传递的值
  formData?: Record<string, any>; // 搜索表单数据
  sceneRelationConfig?: {
    sceneInfos: Array<{
      sceneId: number;
      sceneName: string;
      buInfos: Array<{
        buId: number;
        buName: string;
        buCode: string;
        subBuInfos: Array<{
          subBuId: number;
          subBuName: string;
          subBuCode: string;
          taskKeyInfos: Array<{
            taskKey: string;
          }>;
        }>;
      }>;
      workspaceAppList: Array<{
        workspaceId: string;
        workspaceName: string;
        applicationList?: Array<{
          applicationId: string;
          applicationName: string;
          applicationType: string;
          robotList?: Array<{
            robotId: string;
            robotName: string;
          }>;
        }>;
      }>;
    }>;
  } | null;
  // 是否正在应用常用筛选，用于控制子业务变化时是否清空触发task字段
  isApplyingSavedFilter?: boolean;
}>();

const emit = defineEmits<{
  (e: 'change', value: string, displayName?: string): void;
}>();

const localValue = ref(props.modelValue || '');

// 监听 props.modelValue 的变化，更新 localValue
watch(
  () => props.modelValue,
  (newValue) => {
    localValue.value = newValue || ''; // 更新本地值
  }
);

// 监听 props.formData.taskKey 的变化，更新 localValue 并触发 change 事件
watch(
  () => props.formData?.taskKey,
  (newValue, oldValue) => {

    // 只有当新值与旧值不同时才更新本地值和触发 change 事件
    if (newValue !== oldValue && newValue !== localValue.value) {
      localValue.value = newValue || ''; // 更新本地值

      // 如果新值不为空，手动触发 change 事件
      if (newValue) {
        // 获取触发task的显示名称
        const displayName = taskKeyValueMap.value[newValue] || newValue;

        emit('change', newValue, displayName); // 触发 change 事件
      }
    }
  },
  { immediate: true } // 立即触发一次，确保初始化时也能正确处理
);

watch(
  () => localValue.value,
  (newValue) => {
    // 获取触发task的显示名称
    const displayName = taskKeyValueMap.value[newValue] || newValue;

    emit('change', newValue, displayName); // 更新父组件的值，并传递显示名称
  }
);

// 计算是否已选择子业务
const hasSelectedSubBusiness = computed(() => {
  return !!props.formData?.bu && !!props.formData?.subBu;
});

// 从 sceneRelationConfig 获取触发task选项
// 存储值到名称的映射，供外部访问
// 定义为响应式对象，便于外部访问
// 将其定义为全局变量，便于在组件外部访问
const taskKeyValueMap = ref<Record<string, string>>({});

// 对外暴露获取名称的方法
const getTaskKeyName = (value: string): string => {
  return taskKeyValueMap.value[value] || value;
};

// 强制触发 change 事件
const triggerChange = (value: string) => {
  if (value) {
    const displayName = taskKeyValueMap.value[value] || value;
    emit('change', value, displayName);
  }
};

// 将方法挂载到组件实例上
// 使用defineExpose将方法暴露给父组件
defineExpose({
  getTaskKeyName,
  taskKeyValueMap,
  triggerChange,
});

// 更新触发task选项和值到名称的映射
const updateTaskKeyOptions = () => {
  if (!props.sceneRelationConfig || !props.sceneRelationConfig.sceneInfos) {
    return [];
  }

  const taskKeys = new Map<string, string>();
  // 清空值到名称的映射
  taskKeyValueMap.value = {};

  // 如果已选择子业务，只显示该子业务下的触发task
  if (hasSelectedSubBusiness.value) {
    const selectedBuId = props.formData?.bu;
    const selectedSubBuId = props.formData?.subBu;

    // 从所有场景中查找匹配的业务和子业务，并收集其触发task信息
    props.sceneRelationConfig.sceneInfos.forEach((scene) => {
      if (scene.buInfos && Array.isArray(scene.buInfos)) {
        const matchedBusiness = scene.buInfos.find((business) => String(business.buId) === selectedBuId);

        if (matchedBusiness && matchedBusiness.subBuInfos && Array.isArray(matchedBusiness.subBuInfos)) {
          const matchedSubBusiness = matchedBusiness.subBuInfos.find(
            (subBusiness) => String(subBusiness.subBuId) === selectedSubBuId
          );

          if (matchedSubBusiness && matchedSubBusiness.taskKeyInfos && Array.isArray(matchedSubBusiness.taskKeyInfos)) {
            matchedSubBusiness.taskKeyInfos.forEach((taskKeyInfo) => {
              // 使用 taskKey 作为值和标签
              if (taskKeyInfo.taskKey) {
                const value = taskKeyInfo.taskKey;
                const label = taskKeyInfo.taskKey; // 任务名称就是taskKey
                taskKeys.set(value, label);
                // 存储值到名称的映射
                taskKeyValueMap.value[value] = label;
              }
            });
          }
        }
      }
    });
  } else {
    // 如果没有选择子业务，从所有场景中收集所有触发task信息作为初始化可选值
    props.sceneRelationConfig.sceneInfos.forEach((scene) => {
      if (scene.buInfos && Array.isArray(scene.buInfos)) {
        scene.buInfos.forEach((business) => {
          if (business.subBuInfos && Array.isArray(business.subBuInfos)) {
            business.subBuInfos.forEach((subBusiness) => {
              if (subBusiness.taskKeyInfos && Array.isArray(subBusiness.taskKeyInfos)) {
                subBusiness.taskKeyInfos.forEach((taskKeyInfo) => {
                  // 使用 taskKey 作为值和标签
                  if (taskKeyInfo.taskKey) {
                    const value = taskKeyInfo.taskKey;
                    const label = taskKeyInfo.taskKey; // 任务名称就是taskKey
                    taskKeys.set(value, label);
                    // 存储值到名称的映射
                    taskKeyValueMap.value[value] = label;
                  }
                });
              }
            });
          }
        });
      }
    });
  }

  const options = Array.from(taskKeys.entries()).map(([value, label]) => ({
    value,
    label,
  }));

  console.log('[TaskKeySelect] taskKeyOptions:', options, 'selectedSubBu:', props.formData?.subBu, 'totalOptions:', options.length);
  return options;
};

// 存储触发task选项
const taskKeyOptionsData = ref<Array<{ value: string; label: string }>>([]);

// 监听子业务变化和场景配置变化，更新触发task选项
watch(
  [() => props.formData?.bu, () => props.formData?.subBu, () => props.sceneRelationConfig],
  () => {
    taskKeyOptionsData.value = updateTaskKeyOptions();
  },
  { immediate: true, deep: true }
);

const taskKeyOptions = computed(() => {
  return taskKeyOptionsData.value;
});

// 监听子业务变化，重置触发task选择
watch(
  () => props.formData?.subBu,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      // 如果正在应用常用筛选，不清空触发task选择
      if (!props.isApplyingSavedFilter) {
        localValue.value = '';
      }
    }
  }
);
</script>

<style lang="scss" scoped></style>
