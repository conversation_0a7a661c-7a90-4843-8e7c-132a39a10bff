<template>
  <a-cascader
    :options="workspaceListAdapter"
    v-model:value="localValue"
    :allowClear="true"
    @change="handleAppChange"
    placeholder="请选择空间/应用"
  />
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';

const props = defineProps<{
  modelValue?: [string, string]; // v-model 传递的值
  formData?: Record<string, any>; // 搜索表单数据
  sceneRelationConfig?: {
    sceneInfos: Array<{
      sceneId: number;
      sceneName: string;
      workspaceAppList: Array<{
        workspaceId: string;
        workspaceName: string;
        applicationList?: Array<{
          applicationId: string;
          applicationName: string;
          applicationType: string;
          robotList?: Array<{
            robotId: string;
            robotName: string;
          }>;
        }>;
      }>;
    }>;
  } | null;
}>();

const emit = defineEmits<{
  (e: 'change', workspaceAndApp: [string, string], displayText?: string): void;
}>();

// 从sceneRelationConfig中提取所有工作空间和应用
const workspaceListAdapter = computed(() => {
  if (!props.sceneRelationConfig || !props.sceneRelationConfig.sceneInfos) {
    return [];
  }

  // 从所有场景中收集工作空间和应用
  const allWorkspaces: Array<{
    value: string;
    label: string;
    children: Array<{
      value: string;
      label: string;
    }>;
  }> = [];

  // 用于跟踪已添加的应用，避免重复
  const appMap: Record<string, { value: string; label: string }> = {};

  // 遍历所有场景，收集工作空间和应用
  props.sceneRelationConfig.sceneInfos.forEach((scene) => {
    if (scene.workspaceAppList && Array.isArray(scene.workspaceAppList)) {
      scene.workspaceAppList.forEach((workspace) => {
        // 检查是否已存在相同ID的工作空间
        let existingWorkspace = allWorkspaces.find((ws) => ws.value === workspace.workspaceId);

        if (!existingWorkspace) {
          // 如果不存在，添加新的工作空间
          existingWorkspace = {
            value: workspace.workspaceId,
            label: workspace.workspaceName,
            children: [],
          };
          allWorkspaces.push(existingWorkspace);
        }

        // 处理应用列表，跨场景合并应用
        if (workspace.applicationList && Array.isArray(workspace.applicationList)) {
          workspace.applicationList.forEach((app) => {
            // 如果这个应用还没有被添加到应用映射中，则添加它
            if (!appMap[app.applicationId]) {
              appMap[app.applicationId] = {
                value: app.applicationId,
                label: app.applicationName,
              };

              // 同时添加到工作空间的子列表中
              existingWorkspace.children.push({
                value: app.applicationId,
                label: app.applicationName,
              });
            }
          });
        }
      });
    }
  });

  return allWorkspaces;
});

// 本地值，用于管理选中的工作空间和应用
const localValue = ref<[string, string] | undefined>(props.modelValue || props.formData?.workspaceAndApp);

// 监听 props.modelValue 的变化，更新 localValue
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== localValue.value) {
      localValue.value = newValue;
    }
  }
);

// 监听 props.formData.workspaceAndApp 的变化，更新 localValue 并触发 change 事件
watch(
  () => props.formData?.workspaceAndApp,
  (newValue, oldValue) => {
    // 只有当新值与旧值不同时才更新本地值和触发 change 事件
    if (newValue !== oldValue && JSON.stringify(newValue) !== JSON.stringify(localValue.value)) {
      localValue.value = newValue;

      // 如果新值不为空，手动触发 change 事件
      if (newValue && newValue[0]) {
        // 获取工作空间和应用的显示名称
        const displayText = getWorkspaceAppName(newValue);
        emit('change', newValue, displayText); // 触发 change 事件
      }
    }
  },
  { immediate: true } // 立即触发一次，确保初始化时也能正确处理
);

// 监听 localValue 变化，触发 change 事件
watch(
  () => localValue.value,
  (newValue) => {
    if (newValue) {
      // 获取工作空间和应用的显示名称
      const displayText = getWorkspaceAppName(newValue);
      emit('change', newValue, displayText); // 更新父组件的值，并传递显示名称
    } else {
      emit('change', ['', ''], ''); // 清空选择时，传递空值
    }
  }
);

// 存储值到名称的映射，供外部访问
const workspaceAppValueMap = ref<Record<string, Record<string, string>>>({});

// 获取工作空间和应用的显示名称
const getWorkspaceAppName = (value: [string, string]): string => {
  if (!value || !value[0]) {
    return '';
  }

  // 从适配后的数据中查找工作空间
  const workspace = workspaceListAdapter.value.find((ws) => ws.value === value[0]);
  if (workspace) {
    if (!value[1]) {
      // 只选择了工作空间
      return workspace.label;
    } else {
      // 选择了工作空间和应用
      const app = (workspace.children || []).find((app) => app.value === value[1]);
      if (app) {
        return `${workspace.label} / ${app.label}`;
      }
    }
  }

  // 如果在适配数据中找不到，尝试从映射中查找
  const workspaceId = value[0];
  const appId = value[1];

  if (workspaceAppValueMap.value[workspaceId]) {
    const workspaceName = workspaceAppValueMap.value[workspaceId].name || workspaceId;

    if (!appId) {
      return workspaceName;
    }

    const appName = workspaceAppValueMap.value[workspaceId][appId];
    if (appName) {
      return `${workspaceName} / ${appName}`;
    }
  }

  // 如果都没有找到，返回原始值
  return value.filter(Boolean).join(' / ');
};

// 更新工作空间和应用的值到名称映射
const updateWorkspaceAppValueMap = () => {
  if (!props.sceneRelationConfig || !props.sceneRelationConfig.sceneInfos) {
    return;
  }

  const newMap: Record<string, Record<string, string>> = {};

  // 用于跟踪已添加的应用，避免重复
  const appMap: Record<string, string> = {};

  props.sceneRelationConfig.sceneInfos.forEach((scene) => {
    if (scene.workspaceAppList && Array.isArray(scene.workspaceAppList)) {
      scene.workspaceAppList.forEach((workspace) => {
        // 初始化工作空间映射
        if (!newMap[workspace.workspaceId]) {
          newMap[workspace.workspaceId] = {
            name: workspace.workspaceName,
          };
        }

        // 添加应用映射，跨场景合并应用
        if (workspace.applicationList) {
          workspace.applicationList.forEach((app) => {
            // 如果这个应用还没有被添加到应用映射中，则添加它
            if (!appMap[app.applicationId]) {
              appMap[app.applicationId] = app.applicationName;
            }

            // 将应用添加到工作空间的映射中
            if (!newMap[workspace.workspaceId][app.applicationId]) {
              newMap[workspace.workspaceId][app.applicationId] = app.applicationName;
            }
          });
        }
      });
    }
  });

  workspaceAppValueMap.value = newMap;
};

// 监听场景配置变化，更新值到名称的映射
watch(
  () => props.sceneRelationConfig,
  (newConfig) => {
    updateWorkspaceAppValueMap();

    // 当场景配置变化时，检查当前值是否需要更新
    if (props.formData?.workspaceAndApp && JSON.stringify(props.formData.workspaceAndApp) !== JSON.stringify(localValue.value)) {
      localValue.value = props.formData.workspaceAndApp;

      // 获取工作空间和应用的显示名称
      const displayText = getWorkspaceAppName(props.formData.workspaceAndApp);
      emit('change', props.formData.workspaceAndApp, displayText);
    }
  },
  { deep: true, immediate: true }
);

// 将方法挂载到组件实例上
defineExpose({
  getWorkspaceAppName,
});

const handleAppChange = (value: [string, string]) => {
  localValue.value = value;
};
</script>

<style lang="scss" scoped></style>
