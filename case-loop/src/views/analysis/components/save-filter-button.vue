<template>
  <div class="save-filter-button-wrapper">
    <a-button
      class="save-filter-btn"
      type="text"
      size="small"
      :disabled="!hasFilterConditions"
      @click="handleSaveClick"
    >
      <StarOutlined class="icon-star" />
      保存筛选
    </a-button>

    <save-filter-modal
      :visible="modalVisible"
      @update:visible="modalVisible = $event"
      :filter-conditions="filterConditions"
      @save="handleSaveFilter"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, PropType } from 'vue';
import SaveFilterModal from './save-filter-modal.vue';
import { useNotification } from '@/hooks/use-notification';

export default defineComponent({
  name: 'SaveFilterButton',

  components: {
    SaveFilterModal
  },

  props: {
    filterConditions: {
      type: Object as PropType<Record<string, any>>,
      required: true
    }
  },

  emits: ['save-filter'],

  setup(props, { emit }) {
    const { notification } = useNotification();
    const modalVisible = ref(false);

    // 检查是否有筛选条件
    const hasFilterConditions = computed(() => {
      const conditions = props.filterConditions;
      if (!conditions) return false;

      // 检查对象中是否有非空值
      return Object.values(conditions).some(value => {
        if (Array.isArray(value)) {
          return value.length > 0;
        }
        return value !== null && value !== undefined && value !== '';
      });
    });

    // 打开保存筛选弹窗
    const handleSaveClick = () => {
      if (!hasFilterConditions.value) {
        notification.warning({
          title: '保存失败',
          message: '请先设置筛选条件'
        });
        return;
      }

      modalVisible.value = true;
    };

    // 保存筛选条件
    const handleSaveFilter = (name: string) => {
      const savedFilter = {
        name,
        conditions: { ...props.filterConditions },
        createdAt: Date.now(),
        id: Date.now().toString() // 简单使用时间戳作为ID
      };

      emit('save-filter', savedFilter);
      modalVisible.value = false;

      notification.success({
        title: '保存成功',
        message: `筛选条件"${name}"已保存`
      });
    };

    return {
      modalVisible,
      hasFilterConditions,
      handleSaveClick,
      handleSaveFilter
    };
  }
});
</script>

<style scoped>
.save-filter-button-wrapper {
  display: inline-block;
}

.save-filter-btn {
  display: flex;
  align-items: center;
  padding: 0 8px;

  .icon-star {
    margin-right: 4px;
    font-size: 14px;
  }
}
</style>