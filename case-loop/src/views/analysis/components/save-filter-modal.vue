<template>
  <mtd-modal
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    title="保存筛选条件"
    width="420px"
    append-to-body
    :closable="true"
    @close="handleClose"
  >
    <div class="save-filter-modal">
      <div class="save-form">
        <mtd-form :model="formData" :rules="rules" ref="formRef">
          <mtd-form-item prop="name">
            <mtd-input
              v-model="formData.name"
              placeholder="请输入筛选条件名称"
              maxlength="30"
              show-word-limit
              autofocus
              ref="nameInput"
            />
          </mtd-form-item>
        </mtd-form>
      </div>
    </div>

    <template #footer>
      <mtd-button @click="handleClose">取消</mtd-button>
      <mtd-button type="primary" :loading="loading" @click="handleSave">保存</mtd-button>
    </template>
  </mtd-modal>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed, PropType, nextTick, watch } from 'vue';

export default defineComponent({
  name: 'SaveFilterModal',
  
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    filterConditions: {
      type: Object as PropType<Record<string, any>>,
      required: true
    }
  },
  
  emits: ['update:visible', 'save'],
  
  setup(props, { emit }) {
    const formRef = ref(null);
    const nameInput = ref(null);
    const loading = ref(false);
    
    const formData = reactive({
      name: ''
    });
    
    const rules = {
      name: [
        { required: true, message: '请输入筛选条件名称', trigger: 'blur' }
      ]
    };
    
    const handleClose = () => {
      formData.name = '';
      emit('update:visible', false);
    };
    
    const handleSave = async () => {
      if (!formRef.value) return;
      
      try {
        loading.value = true;
        await (formRef.value as any).validate();
        
        emit('save', formData.name);
        formData.name = '';
      } catch (error) {
        // 表单验证失败，不做处理
      } finally {
        loading.value = false;
      }
    };
    
    // 当弹窗显示时，自动聚焦到输入框
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        nextTick(() => {
          nameInput.value?.focus();
        });
      }
    });
    
    return {
      formRef,
      nameInput,
      formData,
      rules,
      loading,
      handleClose,
      handleSave
    };
  }
});
</script>

<style scoped>
.save-filter-modal {
  padding: 0 20px;
  
  .save-form {
    width: 100%;
  }
}
</style> 