<template>
  <div class="service-process-container">
    <mtd-collapse v-model="activeCollapse" class="service-process-collapse" @change="onCollapseChange">
      <mtd-collapse-item name="service-process">
        <template #title>
          <div class="collapse-header">
            <mtd-icon name="mtdicon-theme-o" style="color: #1890ff" />
            服务过程
            <!-- 在标题后面显示自定义标签 -->
            <div
              class="custom-tags-container"
              v-if="
                parsedServiceProcessInfo &&
                parsedServiceProcessInfo.sessionCustomTagList &&
                parsedServiceProcessInfo.sessionCustomTagList.length
              "
            >
              <mtd-tag
                v-for="(tag, index) in parsedServiceProcessInfo.sessionCustomTagList"
                :key="index"
                class="title-tag"
                type="error"
                size="small"
              >
                {{ tag.tagName }}
              </mtd-tag>
            </div>
          </div>
        </template>

        <div class="service-process-content">
          <!-- 服务过程为空时展示 -->
          <div v-if="!parsedServiceProcessInfo" class="empty-data">暂无数据</div>

          <!-- 服务过程不为空但没有groups时的提示 -->
          <div
            v-else-if="!parsedServiceProcessInfo.groups || !parsedServiceProcessInfo.groups.length"
            class="empty-data"
          >
            暂无数据
          </div>

          <!-- 服务过程不为空时展示 -->
          <template v-else>
            <!-- 横向时间轴容器 -->
            <div class="horizontal-process-timeline">
              <!-- 滚动控制按钮 -->
              <div class="scroll-button scroll-left" @click="scrollLeft" v-if="canScrollLeft">
                <mtd-icon name="mtdicon-angle-left" />
              </div>

              <!-- 时间轴内容区域（可横向滚动） -->
              <div class="timeline-scroll-container" ref="scrollContainer">
                <div class="process-timeline">
                  <div
                    class="timeline-item"
                    v-for="(group, groupIndex) in parsedServiceProcessInfo.groups"
                    :key="groupIndex"
                  >
                    <div class="timeline-card">
                      <div class="timeline-header">
                        <div class="timeline-icon">
                          <div class="icon-circle">
                            <mtd-icon :name="getIconForProcessType(group.serviceProcessType)" />
                          </div>
                        </div>
                        <div class="timeline-title-block">
                          <div class="timeline-title">
                            {{ group.serviceProcessName }}
                            <span class="item-count">({{ group.items.length }})</span>
                          </div>
                          <div class="timeline-time">{{ formatTime(group.triggerTime) }}</div>
                        </div>
                      </div>

                      <div class="timeline-content">
                        <div
                          v-for="(item, itemIndex) in group.items"
                          :key="itemIndex"
                          class="detail-card"
                          :class="{ 'detail-card-selected': isDetailCardSelected(item.uuid) }"
                          @click="toggleDetailCardSelection(item, group, groupIndex, itemIndex)"
                        >
                          <div class="detail-card-header">
                            <div class="detail-card-title-wrapper">
                              <!-- 只有在item的名称与group不同时才显示名称 -->
                              <template v-if="item.serviceProcessName !== group.serviceProcessName">
                                <mtd-tag size="small" type="primary">{{ item.serviceProcessName }}</mtd-tag>
                              </template>
                              <!-- 否则显示序号 -->
                              <template v-else>
                                <mtd-tag
                                  size="small"
                                  :type="
                                    item.serviceProcessType === 'transfer'
                                      ? 'error'
                                      : item.serviceProcessType === 'notice'
                                      ? 'warning'
                                      : 'info'
                                  "
                                >
                                  事件 #{{ itemIndex + 1 }}
                                </mtd-tag>
                              </template>
                              <span class="detail-time">{{ formatTime(item.triggerTime) }}</span>
                            </div>
                          </div>
                          <div class="detail-card-content" v-if="item.detail">
                            <!-- 针对不同类型的服务过程显示不同的内容 -->
                            <!-- 公告类型 -->
                            <template v-if="item.serviceProcessType === 'notice' && item.detail.content">
                              <div class="notice-content-container">
                                <!-- 1. 应用场景 -->
                                <div class="detail-item" v-if="item.detail.applySceneName">
                                  <span class="label">应用场景:</span>
                                  <span class="value">{{ item.detail.applySceneName }}</span>
                                </div>

                                <!-- 2. 公告名称 -->
                                <div class="detail-item" v-if="item.detail.name">
                                  <span class="label">公告名称:</span>
                                  <span class="value notice-title">{{ item.detail.name }}</span>
                                </div>

                                <!-- 3. 公告内容 -->
                                <div class="detail-item">
                                  <span class="label">公告内容:</span>
                                  <span class="value">
                                    <div class="notice-content-html" v-html="item.detail.content"></div>
                                  </span>
                                </div>

                                <!-- 4. 引用规则 -->
                                <div v-if="item.detail.rules && item.detail.rules.length" class="detail-item">
                                  <span class="label">引用规则:</span>
                                  <span class="value">
                                    <mtd-tag
                                      v-for="(rule, ruleIndex) in item.detail.rules"
                                      :key="ruleIndex"
                                      size="small"
                                      type="warning"
                                      class="rule-tag"
                                    >
                                      {{ rule.name || '未命名规则' }}
                                    </mtd-tag>
                                  </span>
                                </div>
                              </div>
                            </template>

                            <!-- 豆腐块类型 -->
                            <template
                              v-else-if="
                                item.serviceProcessType === 'doufukuai' && item.detail.clickTypicalQuestionName
                              "
                            >
                              <div class="doufukuai-content">
                                <div class="detail-item">
                                  <span class="label">常见问题:</span>
                                  <span class="value">{{ item.detail.clickTypicalQuestionName }}</span>
                                </div>
                              </div>
                            </template>

                            <!-- 轻交互类型 -->
                            <template v-else-if="item.serviceProcessType === 'light'">
                              <div class="light-content">
                                <div class="detail-item" v-if="item.detail.lightModuleName">
                                  <span class="label">模块:</span>
                                  <span class="value">{{ item.detail.lightModuleName }}</span>
                                </div>
                                <div class="detail-item" v-if="item.detail.lightOperationName">
                                  <span class="label">操作:</span>
                                  <span class="value">{{ item.detail.lightOperationName }}</span>
                                </div>
                                <div class="detail-item" v-if="item.detail.eventName">
                                  <span class="label">事件名称:</span>
                                  <span class="value">{{ item.detail.eventName }}</span>
                                </div>
                                <div class="detail-item" v-if="item.detail.positionName">
                                  <span class="label">位置:</span>
                                  <span class="value">{{ item.detail.positionName }}</span>
                                </div>
                              </div>
                            </template>

                            <!-- 机器人调用 -->
                            <template v-else-if="item.serviceProcessType === 'robotInvoke'">
                              <div class="robot-content">
                                <div class="detail-item" v-if="item.detail.userInput">
                                  <span class="label">用户输入:</span>
                                  <span class="value">{{ item.detail.userInput }}</span>
                                </div>
                                <div class="detail-item" v-if="item.detail.userInputType">
                                  <span class="label">输入类型:</span>
                                  <span class="value">{{ item.detail.userInputType }}</span>
                                </div>
                                <div class="detail-item" v-if="item.detail.typicalQuestionName">
                                  <span class="label">标准问:</span>
                                  <span class="value">{{ item.detail.typicalQuestionName }}</span>
                                </div>
                                <div class="detail-item" v-if="item.detail.solution && item.detail.solution.taskName">
                                  <span class="label">任务名称:</span>
                                  <span class="value">{{ item.detail.solution.taskName }}</span>
                                </div>
                                <div
                                  class="detail-item"
                                  v-if="item.detail.solution && item.detail.solution.solutionName"
                                >
                                  <span class="label">方案类型:</span>
                                  <span class="value">{{ item.detail.solution.solutionName }}</span>
                                </div>

                                <!-- 显示关联任务 -->
                                <div
                                  v-if="
                                    item.detail.solution &&
                                    item.detail.solution.relationTasks &&
                                    item.detail.solution.relationTasks.length
                                  "
                                  class="relation-tasks-container"
                                >
                                  <div class="related-tasks-header" @click="toggleTaskExpand(groupIndex, itemIndex)">
                                    <div class="detail-title">
                                      关联任务 ({{ item.detail.solution.relationTasks.length }})
                                    </div>
                                    <div class="expand-control">
                                      <span class="expand-text">{{
                                        isTaskExpanded(groupIndex, itemIndex) ? '收起' : '展开'
                                      }}</span>
                                      <mtd-icon
                                        name="mtdicon-chevron-down"
                                        class="toggle-icon"
                                        :class="{ expanded: isTaskExpanded(groupIndex, itemIndex) }"
                                      />
                                    </div>
                                  </div>

                                  <!-- 明确使用v-if而非v-show，以解决可能的响应性问题 -->
                                  <div class="related-tasks" v-if="isTaskExpanded(groupIndex, itemIndex)">
                                    <div class="tasks-list-title">关联任务列表</div>
                                    <div
                                      class="detail-item"
                                      v-for="(task, taskIndex) in item.detail.solution.relationTasks"
                                      :key="taskIndex"
                                    >
                                      <span class="label">{{ taskIndex + 1 }}</span>
                                      <div class="value">
                                        <span class="task-name">{{ task.taskName }}</span>
                                        <span class="task-time">{{ task.triggerTime }}</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </template>

                            <!-- 转人工 -->
                            <template v-else-if="item.serviceProcessType === 'transfer'">
                              <div class="transfer-content">
                                <!-- 基本信息区域 -->
                                <div class="transfer-basic-info">
                                  <div class="info-row">
                                    <div class="detail-item status-item" v-if="item.detail.status">
                                      <span class="label">状态:</span>
                                      <span class="value status-value">
                                        <mtd-tag :type="getTransferStatusTagType(item.detail.status)" size="small">
                                          {{ item.detail.status }}
                                        </mtd-tag>
                                      </span>
                                    </div>

                                    <div class="detail-item" v-if="item.detail.transferType">
                                      <span class="label">转人工类型:</span>
                                      <span class="value">{{ item.detail.transferType }}</span>
                                    </div>
                                  </div>

                                  <div class="info-row">
                                    <div class="detail-item" v-if="item.detail.skillName">
                                      <span class="label">技能组:</span>
                                      <span class="value">{{ item.detail.skillName }}</span>
                                    </div>
                                  </div>

                                  <div class="info-row">
                                    <div class="detail-item" v-if="item.detail.strategyName">
                                      <span class="label">策略名称:</span>
                                      <span class="value">{{ item.detail.strategyName }}</span>
                                    </div>

                                    <div class="detail-item" v-if="item.detail.sceneType">
                                      <span class="label">场景类型:</span>
                                      <span class="value">{{ item.detail.sceneType }}</span>
                                    </div>
                                  </div>
                                </div>

                                <!-- 技能变更列表 -->
                                <div
                                  v-if="item.detail.skillChangeList && item.detail.skillChangeList.length"
                                  class="skill-changes-block"
                                >
                                  <div class="skill-changes-header">
                                    <mtd-icon name="mtdicon-transfer-o" />
                                    <span>技能组变更历史</span>
                                  </div>

                                  <div class="skill-changes-list">
                                    <div
                                      v-for="(change, changeIndex) in item.detail.skillChangeList"
                                      :key="changeIndex"
                                      class="skill-change-item"
                                    >
                                      <!-- 时间信息 -->
                                      <div class="skill-change-time">
                                        <span class="time-value">{{ change.hitTime || formatTime(change.time) }}</span>
                                      </div>

                                      <!-- 技能组信息 -->
                                      <div class="skill-change-content">
                                        <div class="skill-name">
                                          <span class="label">技能组:</span>
                                          <span class="value"
                                            >{{ change.skillName }}
                                            <span v-if="change.skillId">(ID: {{ change.skillId }})</span></span
                                          >
                                        </div>
                                        <div class="skill-reason" v-if="change.reason">
                                          <span class="label">原因:</span>
                                          <span class="value">{{ change.reason }}</span>
                                        </div>
                                        <div class="skill-strategy" v-if="change.strategyName">
                                          <span class="label">策略:</span>
                                          <span class="value">{{ change.strategyName }}</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </template>

                            <!-- 自动化任务类型 -->
                            <template v-else-if="item.serviceProcessType === 'automaticTask'">
                              <div class="automatic-task-content">
                                <!-- 基本信息区域 -->
                                <div class="automatic-task-basic-info">
                                  <div class="info-row">
                                    <div class="detail-item" v-if="item.detail.taskSceneName">
                                      <span class="label">场景名称:</span>
                                      <span class="value">{{ item.detail.taskSceneName }}</span>
                                    </div>
                                  </div>
                                  <div class="info-row">
                                    <div class="detail-item" v-if="item.detail.referKeyName">
                                      <span class="label">关联任务:</span>
                                      <span class="value">{{ item.detail.referKeyName }}</span>
                                    </div>
                                  </div>

                                  <div class="info-row">
                                    <div class="detail-item" v-if="item.detail.taskState">
                                      <span class="label">任务状态:</span>
                                      <span class="value">
                                        <mtd-tag :type="getTaskStateTagType(item.detail.taskState)" size="small">
                                          {{ item.detail.taskState }}
                                        </mtd-tag>
                                      </span>
                                    </div>
                                  </div>
                                </div>

                                <!-- 解决方案信息 -->
                                <div v-if="item.detail.solution" class="solution-block">
                                  <div class="solution-header">
                                    <mtd-icon name="mtdicon-solution-o" />
                                    <span>解决方案信息</span>
                                  </div>
                                  <div class="solution-grid">
                                    <div class="detail-item">
                                      <span class="label">方案名称:</span>
                                      <span class="value">{{ item.detail.solution.solutionName }}</span>
                                    </div>
                                    <div class="detail-item">
                                      <span class="label">任务名称:</span>
                                      <span class="value">{{ item.detail.solution.taskName }}</span>
                                    </div>
                                    <div class="detail-item">
                                      <span class="label">任务通道:</span>
                                      <span class="value">{{ item.detail.solution.taskChannel }}</span>
                                    </div>
                                  </div>
                                </div>

                                <!-- 业务信息 -->
                                <div v-if="item.businessInfo" class="business-block">
                                  <div class="business-header">
                                    <mtd-icon name="mtdicon-briefcase-o" />
                                    <span>业务信息</span>
                                  </div>
                                  <div class="business-grid">
                                    <div class="detail-item">
                                      <span class="label">业务类型:</span>
                                      <span class="value">{{ item.businessInfo.businessTypeName }}</span>
                                    </div>
                                    <div class="detail-item">
                                      <span class="label">子业务:</span>
                                      <span class="value">{{ item.businessInfo.subBusinessTypeName }}</span>
                                    </div>
                                    <div class="detail-item">
                                      <span class="label">机器人:</span>
                                      <span class="value">{{ item.businessInfo.robotName }}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </template>

                            <!-- 转人工召回类型 -->
                            <template v-else-if="item.serviceProcessType === 'transferRecall'">
                              <div class="transfer-recall-content">
                                <!-- 基本信息区域 -->
                                <div class="recall-basic-info">
                                  <div class="info-row">
                                    <div class="detail-item" v-if="item.detail.strategyName">
                                      <span class="label">策略名称:</span>
                                      <span class="value strategy-name">{{ item.detail.strategyName }}</span>
                                    </div>
                                  </div>

                                  <div class="info-row">
                                    <div class="detail-item" v-if="item.detail.applySceneName">
                                      <span class="label">应用场景:</span>
                                      <span class="value">{{ item.detail.applySceneName }}</span>
                                    </div>

                                    <div class="detail-item" v-if="item.detail.recallReason">
                                      <span class="label">召回原因:</span>
                                      <span class="value">{{ item.detail.recallReason }}</span>
                                    </div>
                                  </div>
                                </div>

                                <!-- 规则信息 -->
                                <div v-if="item.detail.rules && item.detail.rules.length" class="rules-block">
                                  <div class="rules-header">
                                    <mtd-icon name="mtdicon-rule-o" />
                                    <span>规则信息</span>
                                  </div>

                                  <div class="rules-list">
                                    <div
                                      v-for="(rule, ruleIndex) in item.detail.rules"
                                      :key="ruleIndex"
                                      class="rule-item"
                                    >
                                      <div class="rule-title">
                                        <mtd-tag size="small" type="warning">规则 #{{ ruleIndex + 1 }}</mtd-tag>
                                        <span class="rule-name">{{ rule.name }}</span>
                                        <span class="rule-id" v-if="rule.id">(ID: {{ rule.id }})</span>
                                      </div>

                                      <div class="rule-content">
                                        <div class="rule-info-grid">
                                          <div class="detail-item" v-if="rule.businessTypeName">
                                            <span class="label">业务类型:</span>
                                            <span class="value">{{ rule.businessTypeName }}</span>
                                          </div>
                                          <div class="detail-item" v-if="rule.subBusinessTypeName">
                                            <span class="label">子业务:</span>
                                            <span class="value">{{ rule.subBusinessTypeName }}</span>
                                          </div>
                                          <div class="detail-item" v-if="rule.robotName">
                                            <span class="label">机器人:</span>
                                            <span class="value">{{ rule.robotName }}</span>
                                          </div>
                                          <div class="detail-item" v-if="rule.hitTime">
                                            <span class="label">命中时间:</span>
                                            <span class="value">{{ rule.hitTime }}</span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- 消息列表 -->
                              </div>
                            </template>

                            <!-- 虚拟客服类型 -->
                            <template v-else-if="item.serviceProcessType === 'virtual'">
                              <div class="virtual-content">
                                <!-- 基本信息 -->
                                <div class="virtual-basic-info">
                                  <!-- 服务时间段 -->
                                  <div class="time-period">
                                    <div class="time-item start-time">
                                      <span class="time-label">开始时间:</span>
                                      <span class="time-value">{{ formatTime(item.triggerTime) }}</span>
                                    </div>
                                  </div>
                                  <div class="time-period">
                                    <div class="time-item end-time">
                                      <span class="time-label">结束时间:</span>
                                      <span class="time-value">{{ formatTime(item.endTime) }}</span>
                                    </div>
                                  </div>

                                  <!-- 结束类型 -->
                                  <div class="end-type-container">
                                    <span class="detail-label">结束类型:</span>
                                    <mtd-tag v-if="item.detail.endType" type="warning" size="small">
                                      {{ item.detail.endType }}
                                    </mtd-tag>
                                    <span v-else class="empty-value">--</span>
                                  </div>
                                </div>

                                <!-- 虚拟客服信息区域 -->
                                <div class="virtual-service-info">
                                  <div class="info-row">
                                    <div class="detail-item" v-if="item.detail.status">
                                      <span class="label">状态:</span>
                                      <span class="value">
                                        <mtd-tag type="primary" size="small">{{ item.detail.status }}</mtd-tag>
                                      </span>
                                    </div>

                                    <div class="detail-item" v-if="item.detail.virtualType">
                                      <span class="label">虚拟客服类型:</span>
                                      <span class="value">{{ item.detail.virtualType }}</span>
                                    </div>
                                  </div>

                                  <div class="info-row">
                                    <div class="detail-item" v-if="item.detail.skillGroup">
                                      <span class="label">技能组:</span>
                                      <span class="value">{{ item.detail.skillGroup }}</span>
                                    </div>
                                  </div>

                                  <div class="info-row">
                                    <div class="detail-item" v-if="item.detail.strategyName">
                                      <span class="label">策略名称:</span>
                                      <span class="value">{{ item.detail.strategyName }}</span>
                                    </div>

                                    <div class="detail-item" v-if="item.detail.sceneType">
                                      <span class="label">场景类型:</span>
                                      <span class="value">{{ item.detail.sceneType }}</span>
                                    </div>
                                  </div>
                                </div>

                                <!-- 详细信息区域 -->
                                <div
                                  class="virtual-details-info"
                                  v-if="
                                    item.detail.clarificationResult ||
                                    item.detail.taskAnswer ||
                                    item.detail.orderCreationDetails
                                  "
                                >
                                  <div class="virtual-info-block">
                                    <div class="virtual-info-header">
                                      <mtd-icon name="mtdicon-file-text-o" />
                                      <span>详细信息</span>
                                    </div>

                                    <div class="virtual-info-grid">
                                      <!-- 厘清结果 -->
                                      <div class="detail-item" v-if="item.detail.clarificationResult">
                                        <span class="label">厘清结果:</span>
                                        <span class="value">{{ item.detail.clarificationResult }}</span>
                                      </div>

                                      <!-- Task答案 -->
                                      <div class="detail-item" v-if="item.detail.taskAnswer">
                                        <span class="label">Task答案:</span>
                                        <span class="value">{{ item.detail.taskAnswer }}</span>
                                      </div>

                                      <!-- 建单详情 -->
                                      <div class="detail-item" v-if="item.detail.orderCreationDetails">
                                        <span class="label">建单详情:</span>
                                        <span class="value">{{ item.detail.orderCreationDetails }}</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- 解决方案列表 -->
                                <div
                                  v-if="item.detail.solutionList && item.detail.solutionList.length"
                                  class="solution-list-block"
                                >
                                  <div class="solution-list-header">
                                    <mtd-icon name="mtdicon-solution-o" />
                                    <span>解决方案列表 ({{ item.detail.solutionList.length }})</span>
                                  </div>

                                  <div class="solution-items">
                                    <div
                                      v-for="(solution, solutionIndex) in item.detail.solutionList"
                                      :key="solutionIndex"
                                      class="solution-item"
                                    >
                                      <div class="solution-title">
                                        <mtd-tag size="small" type="primary">方案 #{{ solutionIndex + 1 }}</mtd-tag>
                                      </div>

                                      <div class="solution-details">
                                        <div class="solution-detail-item" v-if="solution.taskName">
                                          <span class="detail-label">任务:</span>
                                          <span class="detail-value">{{ solution.taskName }}</span>
                                        </div>
                                        <div class="solution-detail-item" v-if="solution.solutionName">
                                          <span class="detail-label">方案:</span>
                                          <span class="detail-value">{{ solution.solutionName }}</span>
                                        </div>
                                        <div class="solution-detail-item" v-if="solution.taskChannel">
                                          <span class="detail-label">渠道:</span>
                                          <span class="detail-value">{{ solution.taskChannel }}</span>
                                        </div>
                                        <div class="solution-detail-item" v-if="solution.taskTag">
                                          <span class="detail-label">标签:</span>
                                          <span class="detail-value">{{ solution.taskTag }}</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </template>

                            <!-- 智能服务进度类型 -->
                            <template v-else-if="item.serviceProcessType === 'aiServiceProgress'">
                              <div class="ai-service-progress-content">
                                <div v-if="item.detail && item.detail.nodeInfo" class="node-info-container">
                                  <!-- 场景信息 -->
                                  <div class="scene-info-block">
                                    <div class="scene-header">
                                      <mtd-icon name="mtdicon-scene-o" />
                                      <span>场景信息</span>
                                    </div>
                                    <div class="detail-item">
                                      <span class="label">场景名称:</span>
                                      <span class="value">{{ item.detail.nodeInfo.sceneName }}</span>
                                    </div>

                                    <div class="detail-item">
                                      <span class="label">场景版本:</span>
                                      <span class="value">{{ item.detail.nodeInfo.sceneVersion }}</span>
                                    </div>
                                  </div>

                                  <!-- 节点信息 -->
                                  <div class="node-detail-block">
                                    <div class="node-header">
                                      <mtd-icon name="mtdicon-node-o" />
                                      <span>节点信息</span>
                                      <mtd-tag
                                        v-if="item.detail.nodeInfo.isInitNode !== undefined"
                                        size="small"
                                        :type="item.detail.nodeInfo.isInitNode ? 'primary' : 'info'"
                                      >
                                        {{ item.detail.nodeInfo.isInitNode ? '初始节点' : '普通节点' }}
                                      </mtd-tag>
                                    </div>
                                    <div class="detail-item">
                                      <span class="label">节点名称:</span>
                                      <span class="value">{{ item.detail.nodeInfo.nodeName }}</span>
                                    </div>
                                    <div class="detail-item">
                                      <span class="label">节点标题:</span>
                                      <span class="value">{{ item.detail.nodeInfo.nodeTitle }}</span>
                                    </div>

                                    <div class="detail-item">
                                      <span class="label">创建时间:</span>
                                      <span class="value">{{ item.detail.nodeInfo.createTime }}</span>
                                    </div>
                                  </div>

                                  <!-- 服务信息 -->
                                  <div class="service-info-block">
                                    <div class="service-header">
                                      <mtd-icon name="mtdicon-service-o" />
                                      <span>服务信息</span>
                                    </div>

                                    <div class="detail-item">
                                      <span class="label">服务类型:</span>
                                      <span class="value"
                                        >{{ item.detail.nodeInfo.serviceTypeName }} ({{
                                          item.detail.nodeInfo.serviceType
                                        }})</span
                                      >
                                    </div>
                                    <div class="detail-item">
                                      <span class="label">服务创建时间:</span>
                                      <span class="value">{{
                                        formatFullDateTime(item.detail.nodeInfo.serviceCreateTime)
                                      }}</span>
                                    </div>
                                  </div>

                                  <!-- 规则参数 -->
                                  <div
                                    v-if="
                                      item.detail.nodeInfo.nodeRuleParams && item.detail.nodeInfo.nodeRuleParams.length
                                    "
                                    class="rule-params-block"
                                  >
                                    <div class="params-header">
                                      <mtd-icon name="mtdicon-control-o" />
                                      <span>规则参数</span>
                                    </div>
                                    <div
                                      v-for="(param, paramIndex) in item.detail.nodeInfo.nodeRuleParams"
                                      :key="paramIndex"
                                      class="param-item"
                                    >
                                      <span class="param-key">{{ param.paramKey }}:</span>
                                      <span class="param-value">{{ param.paramValue }}</span>
                                    </div>
                                  </div>

                                  <!-- 自动异步任务 -->
                                  <div
                                    v-if="
                                      item.detail.nodeInfo.autoAsyncTaskInfos &&
                                      item.detail.nodeInfo.autoAsyncTaskInfos.length
                                    "
                                    class="auto-task-block"
                                  >
                                    <div class="auto-task-header">
                                      <mtd-icon name="mtdicon-task-o" />
                                      <span>自动异步任务 ({{ item.detail.nodeInfo.autoAsyncTaskInfos.length }})</span>
                                    </div>
                                    <div
                                      v-for="(task, taskIndex) in item.detail.nodeInfo.autoAsyncTaskInfos"
                                      :key="taskIndex"
                                      class="task-item"
                                    >
                                      <mtd-tag size="small" type="success">任务 #{{ taskIndex + 1 }}</mtd-tag>
                                      <div class="task-detail">{{ JSON.stringify(task) }}</div>
                                    </div>
                                  </div>
                                </div>
                                <div v-else class="empty-node-info">
                                  <mtd-icon name="mtdicon-warning-o" />
                                  <span>节点信息不完整</span>
                                </div>
                              </div>
                            </template>

                            <!-- 默认显示 -->
                            <template v-else>
                              <!-- 使用方法判断如何显示内容 -->
                              <div v-if="isSimpleContent(item.detail)" class="detail-item content-item">
                                <span class="content-icon"><mtd-icon name="mtdicon-message-o" /></span>
                                <mtd-tooltip placement="top" :content="item.detail.content">
                                  <span class="content-text text-ellipsis">{{ item.detail.content }}</span>
                                </mtd-tooltip>
                              </div>
                              <!-- 其他复杂结构仍然使用JSON显示 -->
                              <div v-else class="detail-item">
                                <pre class="json-content">{{ JSON.stringify(item.detail, null, 2) }}</pre>
                              </div>
                            </template>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 右滚动按钮 -->
              <div class="scroll-button scroll-right" @click="scrollRight" v-if="canScrollRight">
                <mtd-icon name="mtdicon-angle-right" />
              </div>
            </div>
          </template>
        </div>
      </mtd-collapse-item>
    </mtd-collapse>
  </div>
</template>

<script setup lang="ts">
import { computed, defineEmits, defineProps, nextTick, onMounted, ref, watch } from 'vue';
import dayjs from 'dayjs';

const props = defineProps({
  serviceProcessInfo: {
    type: [Object, String],
    default: null,
  },
});

const activeCollapse = ref<string[]>([]);
const scrollContainer = ref<HTMLElement | null>(null);

// 跟踪展开的关联任务的数组索引路径
const expandedTaskPaths = ref<string[]>([]);

// 选中的卡片UUID
const selectedDetailCardUuids = ref<string[]>([]);

// 检查卡片是否被选中
const isDetailCardSelected = (uuid: string) => {
  return uuid && selectedDetailCardUuids.value.includes(uuid);
};

// 发送选中/取消选中的事件
const emit = defineEmits(['collapse-change', 'select-messages', 'deselect-messages', 'request-highlight-messages']);

// 清空选中状态
const clearDetailCardSelection = () => {
  if (selectedDetailCardUuids.value.length > 0) {
    selectedDetailCardUuids.value = [];
    emit('deselect-messages', { clearAll: true });
  }
};

// 提供一个新方法，当对话详情组件已加载时，请求高亮和滚动到选中的消息
const requestHighlightSelectedMessages = () => {
  if (selectedDetailCardUuids.value.length > 0) {
    const selectedCardIndex = selectedDetailCardUuids.value[0];

    // 遍历所有组和项，找到匹配的卡片
    if (parsedServiceProcessInfo.value && parsedServiceProcessInfo.value.groups) {
      for (const group of parsedServiceProcessInfo.value.groups) {
        if (group.items && Array.isArray(group.items)) {
          for (const item of group.items) {
            if (item.uuid === selectedCardIndex && item.messageIdList) {
              // 找到匹配的卡片，请求高亮和滚动
              emit('request-highlight-messages', {
                cardUuid: item.uuid,
                messageIds: item.messageIdList,
                shouldScrollToFirstMessage: true,
                firstMessageId: item.messageIdList[0] || null,
              });
              return;
            }
          }
        }
      }
    }
  }
};

// 切换关联任务的展开状态
const toggleTaskExpand = (groupIndex: number, itemIndex: number) => {
  const path = `${groupIndex}_${itemIndex}`;

  const index = expandedTaskPaths.value.indexOf(path);
  if (index === -1) {
    // 当前未展开，添加到展开列表
    expandedTaskPaths.value.push(path);
  } else {
    // 当前已展开，从列表中移除
    expandedTaskPaths.value.splice(index, 1);
  }
};

// 检查特定任务是否展开
const isTaskExpanded = (groupIndex: number, itemIndex: number) => {
  const path = `${groupIndex}_${itemIndex}`;
  return expandedTaskPaths.value.includes(path);
};

// 横向滚动状态
const canScrollLeft = ref(false);
const canScrollRight = ref(false);

// 解析 serviceProcessInfo 并确保每个项目都有唯一ID
const parsedServiceProcessInfo = computed(() => {
  if (!props.serviceProcessInfo) return null;

  try {
    let data;

    // 如果是字符串，尝试解析JSON
    if (typeof props.serviceProcessInfo === 'string') {
      const parsedData = JSON.parse(props.serviceProcessInfo);

      // 如果解析后的数据包含data字段，使用data
      if (parsedData.data) {
        data = parsedData.data;
      } else {
        data = parsedData;
      }
    }
    // 如果是对象，检查是否有data字段
    else if (props.serviceProcessInfo && typeof props.serviceProcessInfo === 'object') {
      // 如果有data字段并且data字段是一个包含groups的对象
      if (props.serviceProcessInfo.data && props.serviceProcessInfo.data.groups) {
        data = props.serviceProcessInfo.data;
      }
      // 如果本身就有groups字段
      else if (props.serviceProcessInfo.groups) {
        data = props.serviceProcessInfo;
      }
      // 处理嵌套在响应结构中的情况
      else if (props.serviceProcessInfo.data && typeof props.serviceProcessInfo.data === 'string') {
        try {
          const nestedData = JSON.parse(props.serviceProcessInfo.data);

          if (nestedData.data) {
            data = nestedData.data;
          } else {
            data = nestedData;
          }
        } catch (nestedError) {
          console.error('Failed to parse nested JSON:', nestedError);
          data = props.serviceProcessInfo;
        }
      } else {
        // 最后的兜底，直接使用原始对象
        data = props.serviceProcessInfo;
      }
    } else {
      return null;
    }

    return data;
  } catch (e) {
    console.error('解析服务过程信息失败:', e, props.serviceProcessInfo);
    return null;
  }
});

// 初始化数据 - 用于solutionType为taskSolution的项目设置为默认收起
watch(
  parsedServiceProcessInfo,
  (newVal) => {
    if (newVal && newVal.groups) {
      // 默认重置展开状态
      expandedTaskPaths.value = [];

      // 查找所有非taskSolution类型的项目，将它们设置为展开状态
      newVal.groups.forEach((group: any, groupIndex: number) => {
        if (group.items && Array.isArray(group.items)) {
          group.items.forEach((item: any, itemIndex: number) => {
            if (
              item.serviceProcessType === 'robotInvoke' &&
              item.detail &&
              item.detail.solution &&
              item.detail.solution.solutionType !== 'taskSolution'
            ) {
              // 非taskSolution类型，默认展开
              expandedTaskPaths.value.push(`${groupIndex}_${itemIndex}`);
            }
          });
        }
      });
    }
  },
  { immediate: true }
);

// 添加监听，在serviceProcessInfo变化时输出日志
watch(
  () => props.serviceProcessInfo,
  (newVal) => {
    console.log('serviceProcessInfo changed:', newVal);
    console.log('parsedServiceProcessInfo:', parsedServiceProcessInfo.value);
  },
  { immediate: true }
);

// 初始展开折叠面板
onMounted(() => {
  // 默认展开折叠面板
  activeCollapse.value = ['service-process'];

  // 初始化滚动检测
  nextTick(() => {
    checkScrollability();
  });
});

// 横向滚动方法
const scrollLeft = () => {
  if (!scrollContainer.value) return;
  scrollContainer.value.scrollLeft -= 250; // 每次滚动250px
  checkScrollability();
};

const scrollRight = () => {
  if (!scrollContainer.value) return;
  scrollContainer.value.scrollLeft += 250; // 每次滚动250px
  checkScrollability();
};

// 检查是否可以左右滚动
const checkScrollability = () => {
  if (!scrollContainer.value) return;

  const { scrollLeft, scrollWidth, clientWidth } = scrollContainer.value;
  canScrollLeft.value = scrollLeft > 0;
  canScrollRight.value = scrollLeft + clientWidth < scrollWidth;
};

// 监听滚动事件
const onScrollContainerScroll = () => {
  checkScrollability();
};

// 切换卡片选中状态
const toggleDetailCardSelection = (item: any, group: any, groupIndex: number, itemIndex: number) => {
  if (!item || !item.uuid) return;

  const uuid = item.uuid;
  const index = selectedDetailCardUuids.value.indexOf(uuid);

  // 检查是否有消息ID列表
  if (!item.messageIdList || !item.messageIdList.length) {
    console.log('卡片没有关联的消息ID列表:', item);
    return;
  }

  if (index === -1) {
    // 单选模式：先取消之前选中的卡片
    if (selectedDetailCardUuids.value.length > 0) {
      // 获取之前选中的卡片ID
      const previousSelectedUuid = selectedDetailCardUuids.value[0];

      // 先清空选中数组
      selectedDetailCardUuids.value = [];

      // 通知父组件取消之前的选中状态
      emit('deselect-messages', {
        cardUuid: previousSelectedUuid,
      });
    }

    // 选中当前卡片
    selectedDetailCardUuids.value.push(uuid);
    console.log('选中卡片:', uuid, '关联消息:', item.messageIdList);

    // 通知父组件选中消息，同时请求跳转到第一个匹配的消息
    emit('select-messages', {
      cardUuid: uuid,
      messageIds: item.messageIdList,
      cardInfo: {
        serviceProcessType: item.serviceProcessType,
        serviceProcessName: item.serviceProcessName,
        groupIndex,
        itemIndex,
      },
      // 添加跳转请求标志
      shouldScrollToFirstMessage: true,
      // 如果有第一个消息ID，直接提供
      firstMessageId: item.messageIdList[0] || null,
    });
  } else {
    // 取消选中
    selectedDetailCardUuids.value.splice(index, 1);
    console.log('取消选中卡片:', uuid);

    // 通知父组件取消选中消息
    emit('deselect-messages', {
      cardUuid: uuid,
      messageIds: item.messageIdList,
    });
  }
};

// 原来的onCollapseChange需要修改以保留emit的多个事件
const onCollapseChange = (value: string[]) => {
  const isExpanded = value.includes('service-process');
  console.log('Service process collapse state changed:', isExpanded);
  console.log('Current parsed data:', parsedServiceProcessInfo.value);

  if (isExpanded && !parsedServiceProcessInfo.value) {
    console.warn('服务过程面板已展开，但解析后的数据为空');
    console.log('原始数据:', props.serviceProcessInfo);
  }

  // 当面板展开时，检查是否需要显示滚动按钮
  if (isExpanded) {
    nextTick(() => {
      checkScrollability();
    });
  } else {
    // 当折叠面板时，清空选中状态
    clearDetailCardSelection();
  }

  emit('collapse-change', isExpanded);
};

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';

  try {
    return dayjs(timeStr).format('HH:mm:ss');
  } catch (e) {
    return timeStr;
  }
};

/**
 * 判断是否是简单的内容结构
 * @param detail 详情对象
 * @returns 是否是简单内容
 */
const isSimpleContent = (detail: any): boolean => {
  // 判断是否是简单的内容结构，即只有content属性
  if (detail && detail.content && typeof detail.content === 'string') {
    // 检查是否只有content属性或者其他属性很少
    const keys = Object.keys(detail);
    return keys.length === 1 || (keys.length <= 3 && keys.includes('content'));
  }
  return false;
};

// 获取服务过程类型对应的图标
const getIconForProcessType = (type: string) => {
  const iconMap: Record<string, string> = {
    doufukuai: 'mtdicon-cube-o',
    notice: 'mtdicon-notification-o',
    light: 'mtdicon-interaction-o',
    robotInvoke: 'mtdicon-robot-o',
    transfer: 'mtdicon-customer-service-o',
    automaticTask: 'mtdicon-flow-o',
    transferRecall: 'mtdicon-return-o',
    aiServiceProgress: 'mtdicon-process-o',
    default: 'mtdicon-task-o',
  };

  return iconMap[type] || iconMap.default;
};

// 添加滚动监听
onMounted(() => {
  if (scrollContainer.value) {
    scrollContainer.value.addEventListener('scroll', onScrollContainerScroll);
  }
});

// 组件卸载时清理监听器
defineExpose({
  // 提供给父组件的方法
  checkScrollability,
  clearDetailCardSelection,
  requestHighlightSelectedMessages,
});

// 获取任务状态标签类型
const getTaskStateTagType = (taskState: string) => {
  const stateMap: Record<string, string> = {
    未开始: 'warning',
    进行中: 'info',
    已完成: 'success',
    已失败: 'error',
  };

  return stateMap[taskState] || 'default';
};

// 格式化完整日期时间（包含毫秒时间戳）
const formatFullDateTime = (timestamp: number | string) => {
  if (!timestamp) return '';

  try {
    return dayjs(Number(timestamp)).format('YYYY-MM-DD HH:mm:ss');
  } catch (e) {
    return String(timestamp);
  }
};

// 获取转人工状态标签类型
const getTransferStatusTagType = (status: string) => {
  if (status.includes('已接入')) return 'success';
  if (status.includes('等待')) return 'warning';
  if (status.includes('超时')) return 'error';
  if (status.includes('失败')) return 'error';
  if (status.includes('拒绝')) return 'error';
  return 'info';
};

// 单一的watch监听activeCollapse，避免重复定义
watch(
  () => activeCollapse.value,
  (newVal) => {
    // 折叠时清空选中状态
    if (!newVal.includes('service-process')) {
      clearDetailCardSelection();
    }

    // 展开时检查滚动状态
    nextTick(() => {
      checkScrollability();
    });
  }
);
</script>

<style lang="scss" scoped>
// 添加淡入动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.service-process-container {
  width: 100%;
  margin-top: 0;
  height: 100%;
  display: flex;
  flex-direction: column;

  .service-process-collapse {
    border: none;
    flex: 1;

    :deep(.mtd-collapse-item) {
      background-color: #fff;
      border-radius: 4px;
      margin-bottom: 0;

      .mtd-collapse-item-header {
        background-color: #fff;
        border-radius: 4px;
        min-height: 40px;

        .collapse-header {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 14px;
          font-weight: 500;

          .custom-tags-container {
            display: flex;
            margin-left: 10px;
            gap: 4px;

            .title-tag {
              border: 1px solid #f5222d;
              background-color: #fff;
              color: #f5222d;
              padding: 0 6px;
              height: 22px;
              line-height: 20px;
              border-radius: 3px;
              font-size: 12px;
              font-weight: normal;
              box-shadow: 0 0 0 1px rgba(245, 34, 45, 0.1);
              transition: all 0.2s;

              &:hover {
                background-color: rgba(245, 34, 45, 0.05);
              }
            }
          }
        }
      }

      .mtd-collapse-item-content {
        padding: 0 10px 16px;
      }
    }
  }

  .service-process-content {
    padding: 6px 0 20px;
    position: relative;

    .debug-info {
      padding: 12px;
      background-color: #fffbe6;
      border: 1px solid #ffe58f;
      border-radius: 4px;
      margin-bottom: 16px;
      font-size: 12px;

      h4 {
        margin-top: 0;
        margin-bottom: 8px;
        color: #d48806;
      }

      .debug-button {
        margin-top: 8px;
        padding: 4px 8px;
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          color: #1890ff;
          border-color: #1890ff;
        }
      }
    }

    .debug-data {
      margin-top: 8px;
      max-height: 200px;
      overflow-y: auto;
      background-color: #f5f5f5;
      padding: 8px;
      border-radius: 4px;

      pre {
        margin: 0;
        font-size: 12px;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }

    .empty-data {
      height: 120px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #999;
      font-size: 14px;
      background-color: #f9fafc;
      border-radius: 8px;
      border: 1px dashed #e8e8e8;
      box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.02);

      &::before {
        content: '\e688'; /* mtdicon-empty-o */
        font-family: 'MTDIcon';
        font-size: 24px;
        color: #bfbfbf;
        margin-bottom: 8px;
      }
    }

    .service-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 16px;
    }

    // 横向时间轴容器
    .horizontal-process-timeline {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      margin: 4px 0 12px;

      // 滚动按钮样式
      .scroll-button {
        position: absolute;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background-color: rgba(24, 144, 255, 0.1);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        z-index: 10;
        color: #1890ff;
        transition: all 0.3s;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

        &:hover {
          background-color: rgba(24, 144, 255, 0.2);
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.25);
        }

        &.scroll-left {
          left: -14px;
        }

        &.scroll-right {
          right: -14px;
        }
      }

      // 时间轴滚动容器
      .timeline-scroll-container {
        width: 100%;
        overflow-x: auto;
        overflow-y: hidden;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        padding: 4px 0 8px;

        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Opera */
        }
      }

      // 横向时间轴样式
      .process-timeline {
        display: flex;
        padding: 0 8px;
        min-width: 100%;

        // 时间轴项目样式
        .timeline-item {
          flex: 0 0 auto;
          padding: 0 8px;
          margin-right: 4px;
          min-width: 250px;
          max-width: 320px;

          // 时间轴卡片样式
          .timeline-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 100%;
            border: 1px solid #ebedf0;
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
              transform: translateY(-2px);
            }

            // 时间轴头部样式
            .timeline-header {
              padding: 8px 12px;
              display: flex;
              align-items: center;
              border-bottom: 1px solid #f0f0f0;
              background-color: #f8fafc;

              .timeline-icon {
                margin-right: 12px;

                .icon-circle {
                  width: 32px;
                  height: 32px;
                  border-radius: 50%;
                  background-color: rgba(24, 144, 255, 0.1);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 16px;
                  color: #1890ff;
                  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.05);
                  transition: all 0.2s ease;
                }
              }

              .timeline-title-block {
                flex: 1;
                display: flex;
                align-items: center;

                .timeline-title {
                  font-size: 14px;
                  font-weight: 600;
                  color: #222;
                  line-height: 1.4;
                  margin-right: 8px;

                  .item-count {
                    font-size: 12px;
                    color: #888;
                    font-weight: normal;
                    margin-left: 4px;
                  }
                }

                .timeline-time {
                  font-size: 12px;
                  color: #888;
                  background-color: rgba(0, 0, 0, 0.03);
                  padding: 2px 6px;
                  border-radius: 10px;
                }
              }
            }

            // 时间轴内容区域
            .timeline-content {
              padding: 8px;
              flex: 1;
              overflow-y: auto;
              max-height: 300px;

              // 详情卡片样式
              .detail-card {
                background-color: #f9fafc;
                border-radius: 6px;
                padding: 10px 12px;
                margin-bottom: 10px;
                position: relative;
                margin-left: 12px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
                transition: all 0.2s ease;
                cursor: pointer; /* 添加指针样式，表明可点击 */

                &:hover {
                  background-color: #f0f7ff;
                }

                // 选中状态样式
                &.detail-card-selected {
                  background-color: #e6f7ff;
                  border: 1px solid #91d5ff;
                  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);

                  &::before {
                    background-color: #1890ff;
                    box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.3);
                  }

                  &:hover {
                    background-color: #d6f0ff;
                  }
                }

                &::before {
                  content: '';
                  position: absolute;
                  top: 50%;
                  left: -12px;
                  transform: translateY(-50%);
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;
                  background-color: #1890ff;
                  z-index: 1;
                  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
                  transition: all 0.2s ease;
                }

                &:not(:last-child)::after {
                  content: '';
                  position: absolute;
                  top: 50%;
                  left: -8px;
                  height: calc(100% + 10px);
                  border-left: 1.5px dashed rgba(24, 144, 255, 0.6);
                  transform: translateY(5px);
                }

                .detail-card-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 8px;
                  padding-bottom: 6px;
                  border-bottom: 1px dashed rgba(0, 0, 0, 0.06);

                  .detail-card-title-wrapper {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    :deep(.mtd-tag) {
                      border-radius: 4px;
                      font-weight: 500;
                      height: 24px;
                      line-height: 22px;
                      padding: 0 8px;

                      &.mtd-tag-primary {
                        background-color: rgba(24, 144, 255, 0.08);
                        color: #1890ff;
                        border-color: rgba(24, 144, 255, 0.2);
                      }

                      &.mtd-tag-error {
                        background-color: rgba(245, 34, 45, 0.08);
                        color: #f5222d;
                        border-color: rgba(245, 34, 45, 0.2);
                      }

                      &.mtd-tag-warning {
                        background-color: rgba(250, 173, 20, 0.08);
                        color: #faad14;
                        border-color: rgba(250, 173, 20, 0.2);
                      }

                      &.mtd-tag-info {
                        background-color: rgba(144, 147, 153, 0.08);
                        color: #909399;
                        border-color: rgba(144, 147, 153, 0.2);
                      }
                    }
                  }

                  .detail-time {
                    font-size: 12px;
                    color: #888;
                    background-color: rgba(0, 0, 0, 0.03);
                    padding: 1px 6px;
                    border-radius: 10px;
                  }
                }

                .detail-card-content {
                  font-size: 12px;

                  .detail-title {
                    font-weight: 600;
                    margin: 10px 0 6px;
                    color: #222;
                    font-size: 13px;
                  }

                  .related-tasks {
                    background-color: #fafcff;
                    border-radius: 8px;
                    padding: 12px 16px;
                    margin-top: 8px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
                    animation: fadeIn 0.2s ease-in-out;
                    border-left: 3px solid #1890ff;
                    margin-left: 4px;
                    position: relative;

                    &::before {
                      content: '';
                      position: absolute;
                      top: -8px;
                      left: 20px;
                      width: 0;
                      height: 0;
                      border-left: 8px solid transparent;
                      border-right: 8px solid transparent;
                      border-bottom: 8px solid #fafcff;
                    }

                    // 任务列表顶部标题
                    .tasks-list-title {
                      font-size: 12px;
                      color: #8c8c8c;
                      margin-bottom: 12px;
                      display: flex;
                      align-items: center;

                      &::before {
                        content: '';
                        width: 4px;
                        height: 4px;
                        background-color: #1890ff;
                        border-radius: 50%;
                        margin-right: 6px;
                      }
                    }

                    // 优化关联任务内的详情项目
                    .detail-item {
                      display: flex;
                      margin-bottom: 10px;
                      padding-bottom: 10px;
                      border-bottom: 1px dashed rgba(0, 0, 0, 0.06);
                      position: relative;

                      &:last-child {
                        margin-bottom: 0;
                        padding-bottom: 0;
                        border-bottom: none;
                      }

                      // 添加序号样式
                      .label {
                        min-width: 28px;
                        height: 28px;
                        border-radius: 50%;
                        background-color: rgba(24, 144, 255, 0.08);
                        color: #1890ff;
                        font-weight: 500;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 12px;
                        font-size: 12px;
                        flex-shrink: 0;
                      }

                      // 优化任务内容样式
                      .value {
                        flex: 1;
                        color: #333;
                        word-break: break-all;
                        line-height: 1.6;
                        padding: 4px 0;
                        display: flex;
                        flex-direction: column;

                        // 任务名称和时间分开显示
                        .task-name {
                          font-weight: 500;
                          color: #262626;
                          margin-bottom: 4px;
                        }

                        .task-time {
                          color: #888;
                          font-size: 12px;
                          display: flex;
                          align-items: center;

                          &::before {
                            content: '\e6b4'; /* mtdicon-time-o */
                            font-family: 'MTDIcon';
                            font-size: 14px;
                            margin-right: 4px;
                            color: #bfbfbf;
                          }
                        }
                      }
                    }
                  }

                  .notice-content {
                    background-color: #fff;
                    border-radius: 6px;
                    padding: 10px;
                    color: #333;
                    font-size: 12px;
                    line-height: 1.6;
                    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);

                    :deep(p) {
                      margin: 0;
                    }
                  }

                  .robot-content,
                  .light-content,
                  .doufukuai-content,
                  .transfer-content {
                    padding: 2px;
                  }

                  .json-content {
                    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
                    font-size: 12px;
                    white-space: pre-wrap;
                    word-break: break-all;
                    background-color: #fff;
                    padding: 10px;
                    border-radius: 6px;
                    margin: 0;
                    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
                    border-left: 2px solid #1890ff;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.related-tasks-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin-top: 4px;
  margin-bottom: 6px;
  background-color: rgba(24, 144, 255, 0.05);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background-color: rgba(24, 144, 255, 0.1);
  }

  &:active {
    background-color: rgba(24, 144, 255, 0.15);
    transform: translateY(1px);
  }

  .detail-title {
    font-weight: 600;
    color: #1890ff;
    margin: 0;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 14px;
      background-color: #1890ff;
      border-radius: 2px;
    }
  }

  .expand-control {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #1890ff;

    .expand-text {
      font-size: 12px;
    }

    .toggle-icon {
      font-size: 14px;
      transition: transform 0.2s ease;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }
}

.related-tasks {
  background-color: #fafcff;
  border-radius: 8px;
  padding: 12px 16px;
  margin-top: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  animation: fadeIn 0.2s ease-in-out;
  border-left: 3px solid #1890ff;
  margin-left: 4px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #fafcff;
  }

  // 任务列表顶部标题
  .tasks-list-title {
    font-size: 12px;
    color: #8c8c8c;
    margin-bottom: 12px;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      width: 4px;
      height: 4px;
      background-color: #1890ff;
      border-radius: 50%;
      margin-right: 6px;
    }
  }

  // 优化关联任务内的详情项目
  .detail-item {
    display: flex;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px dashed rgba(0, 0, 0, 0.06);
    position: relative;

    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }

    // 添加序号样式
    .label {
      min-width: 28px;
      height: 28px;
      border-radius: 50%;
      background-color: rgba(24, 144, 255, 0.08);
      color: #1890ff;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      font-size: 12px;
      flex-shrink: 0;
    }

    // 优化任务内容样式
    .value {
      flex: 1;
      color: #333;
      word-break: break-all;
      line-height: 1.6;
      padding: 4px 0;
      display: flex;
      flex-direction: column;

      // 任务名称和时间分开显示
      .task-name {
        font-weight: 500;
        color: #262626;
        margin-bottom: 4px;
      }

      .task-time {
        color: #888;
        font-size: 12px;
        display: flex;
        align-items: center;

        &::before {
          content: '\e6b4'; /* mtdicon-time-o */
          font-family: 'MTDIcon';
          font-size: 14px;
          margin-right: 4px;
          color: #bfbfbf;
        }
      }
    }
  }
}

// 调试信息样式
.debug-relation-info {
  padding: 4px 8px;
  background-color: #fffbe6;
  color: #d48806;
  font-size: 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  border: 1px solid #ffe58f;
}

.relation-tasks-container {
  border-top: 1px dashed rgba(0, 0, 0, 0.06);
  margin-top: 8px;
  padding-top: 8px;
}

// 为展开的图标添加旋转状态
.toggle-icon.expanded {
  transform: rotate(180deg);
}

.transfer-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 2px 2px 4px 2px;

  .transfer-basic-info {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .info-row {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 12px;
      margin-bottom: 2px;

      .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 0;

        .label {
          color: #666;
          width: 65px;
          flex-shrink: 0;
          font-weight: 500;
        }

        .value {
          flex: 1;
          margin-left: 6px;
        }

        .status-value {
          margin-left: 4px;
        }
      }
    }
  }

  .skill-changes-block {
    margin-top: 6px;
    margin-left: 0;
    border-left: 2px solid #1890ff;
    background-color: rgba(24, 144, 255, 0.04);
    border-radius: 4px;
    padding: 10px 12px;

    .skill-changes-header {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
      font-weight: 500;
      color: #1890ff;
      margin-bottom: 12px;
      padding-bottom: 6px;
      border-bottom: 1px dashed rgba(24, 144, 255, 0.2);
    }

    .skill-changes-list {
      display: flex;
      flex-direction: column;
      position: relative;

      /* 左侧时间轴 */
      &:before {
        content: '';
        position: absolute;
        left: 8px;
        top: 8px;
        bottom: 8px;
        width: 1px;
        background-color: rgba(24, 144, 255, 0.3);
        z-index: 1;
      }

      .skill-change-item {
        display: flex;
        flex-direction: column;
        position: relative;
        margin-bottom: 20px;
        padding-left: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        /* 时间点圆点 */
        &:before {
          content: '';
          position: absolute;
          left: 4px;
          top: 8px;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #1890ff;
          z-index: 2;
          border: 1px solid #fff;
        }

        .skill-change-time {
          font-size: 12px;
          color: #666;
          margin-bottom: 8px;
          font-weight: 500;

          .time-value {
            display: inline-block;
            background-color: rgba(24, 144, 255, 0.05);
            padding: 2px 6px;
            border-radius: 2px;
          }
        }

        .skill-change-content {
          background-color: white;
          padding: 10px 12px;
          border-radius: 4px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

          .skill-name,
          .skill-reason,
          .skill-strategy {
            display: flex;
            align-items: flex-start;
            margin-bottom: 6px;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              width: 45px;
              min-width: 45px;
              color: #666;
              flex-shrink: 0;
            }

            .value {
              flex: 1;
              color: #333;
              word-break: break-word;
            }
          }

          .skill-name {
            .value {
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}

.transfer-message-list {
  background-color: rgba(245, 245, 245, 0.6);
  border-radius: 8px;
  padding: 12px;

  .message-list-header {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-weight: 500;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
  }

  .message-items {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .message-id-item {
      display: flex;
      align-items: center;
      gap: 6px;
      background-color: white;
      border-radius: 4px;
      padding: 4px 8px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

      .message-id {
        font-family: monospace;
        font-size: 12px;
        color: #666;
      }
    }
  }
}

// 公告样式优化
.notice-content-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 2px 2px 4px 2px;

  .detail-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 6px;

    .label {
      color: #666;
      width: 65px;
      flex-shrink: 0;
      font-weight: 500;
      padding-right: 2px;
    }

    .value {
      flex: 1;
      margin-left: 6px;

      &.notice-title {
        font-weight: 500;
        color: #1890ff;
        font-size: 15px;
      }

      .notice-content-html {
        color: #333;
        line-height: 1.4;
        font-size: 13px;
        background-color: #fff;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        padding: 8px 10px;
        margin-top: 2px;
        margin-bottom: 2px;

        p {
          margin-bottom: 6px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        img {
          max-width: 100%;
          height: auto;
        }
      }

      .mtd-tag {
        background-color: #fff7e6;
        border-color: #ffd591;
        color: #fa8c16;
        margin-right: 6px;
        margin-bottom: 6px;
        height: 22px;
        line-height: 20px;
        padding: 0 8px;
        display: inline-block;
        margin-top: 4px;
      }
    }
  }
}

.ai-service-progress-content {
  .node-info-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .scene-info-block,
  .node-detail-block,
  .service-info-block,
  .rule-params-block,
  .auto-task-block {
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    background-color: #fafafa;
    position: relative;
  }

  .scene-info-block {
    border-left: 3px solid #1890ff;

    .scene-header {
      color: #1890ff;
    }
  }

  .node-detail-block {
    border-left: 3px solid #722ed1;

    .node-header {
      color: #722ed1;
    }
  }

  .service-info-block {
    border-left: 3px solid #52c41a;

    .service-header {
      color: #52c41a;
    }
  }

  .rule-params-block {
    border-left: 3px solid #fa8c16;

    .params-header {
      color: #fa8c16;
    }

    .param-item {
      background-color: rgba(250, 140, 22, 0.05);
      padding: 6px 8px;
      border-radius: 4px;
      margin-bottom: 8px;

      .param-key {
        font-weight: 500;
        margin-right: 4px;
      }

      .param-value {
        color: #333;
      }
    }
  }

  .auto-task-block {
    border-left: 3px solid #13c2c2;

    .auto-task-header {
      color: #13c2c2;
    }

    .task-item {
      margin-bottom: 8px;

      .task-detail {
        margin-top: 4px;
        padding: 6px 8px;
        background-color: rgba(19, 194, 194, 0.05);
        border-radius: 4px;
        font-family: monospace;
        font-size: 12px;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }

  .scene-header,
  .node-header,
  .service-header,
  .params-header,
  .auto-task-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-weight: 500;
    font-size: 14px;
    border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
    padding-bottom: 8px;
  }

  .detail-item {
    margin-bottom: 8px;
    display: flex;

    .label {
      color: #666;
      width: 100px;
      flex-shrink: 0;
    }

    .value {
      color: #333;
      flex: 1;

      &.mono-text {
        font-family: monospace;
        font-size: 12px;
        background-color: rgba(0, 0, 0, 0.03);
        padding: 2px 4px;
        border-radius: 3px;
      }
    }

    &.content-item {
      background-color: #f0f7ff;
      padding: 10px 12px;
      border-radius: 6px;
      border-left: 3px solid #1890ff;
      align-items: center;
      width: 100%;
      box-sizing: border-box;
      overflow: hidden;

      .content-icon {
        margin-right: 8px;
        color: #1890ff;
        display: flex;
        align-items: center;

        :deep(.mtdicon) {
          font-size: 16px;
        }
      }

      .content-text {
        flex: 1;
        font-size: 13px;
        color: #333;
        line-height: 1.5;

        &.text-ellipsis {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          display: inline-block;
        }
      }
    }
  }

  .empty-node-info {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;
    background-color: #f9fafc;
    border-radius: 8px;
    border: 1px dashed #e8e8e8;
    padding: 16px;
    margin-top: 8px;
    height: 100px;

    .mtdicon {
      margin-right: 8px;
      font-size: 20px;
    }
  }
}

.automatic-task-content {
  .automatic-task-basic-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 4px;

    .info-row {
      display: flex;
      align-items: center;
      gap: 12px;

      .detail-item {
        display: flex;
        align-items: center;
        gap: 4px;

        .label {
          color: #666;
          width: 80px;
          flex-shrink: 0;
        }

        .value {
          color: #333;
          flex: 1;
        }
      }
    }
  }

  .solution-block,
  .business-block {
    margin-top: 12px;
    background-color: rgba(24, 144, 255, 0.04);
    border-radius: 4px;
    padding: 8px 10px;
    border-left: 3px solid #1890ff;
    position: relative;

    .solution-header,
    .business-header {
      font-size: 13px;
      font-weight: 500;
      color: #1890ff;
      margin-bottom: 8px;
      padding-bottom: 4px;
      border-bottom: 1px dashed rgba(24, 144, 255, 0.2);
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .solution-grid,
    .business-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 8px 16px;

      .detail-item {
        margin-bottom: 0;

        .mono-text {
          font-family: monospace;
          font-size: 12px;
          background-color: rgba(0, 0, 0, 0.03);
          padding: 2px 4px;
          border-radius: 3px;
        }
      }
    }
  }

  .business-block {
    border-left-color: #52c41a;
    background-color: rgba(82, 196, 26, 0.04);

    .business-header {
      color: #52c41a;
      border-bottom-color: rgba(82, 196, 26, 0.2);
    }
  }
}

.transfer-recall-content {
  .recall-basic-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 4px;

    .info-row {
      display: flex;
      align-items: center;
      gap: 12px;

      .detail-item {
        display: flex;
        align-items: center;
        gap: 4px;

        .label {
          color: #666;
          width: 80px;
          flex-shrink: 0;
        }

        .value {
          color: #333;
          flex: 1;

          &.strategy-name {
            font-weight: 500;
            color: #1890ff;
          }

          &.mono-text {
            font-family: monospace;
            font-size: 12px;
            background-color: rgba(0, 0, 0, 0.03);
            padding: 2px 4px;
            border-radius: 3px;
          }
        }
      }
    }
  }
}

.rules-block {
  margin-top: 12px;
  background-color: rgba(250, 140, 22, 0.04);
  border-radius: 4px;
  padding: 8px 10px;
  border-left: 3px solid #fa8c16;
  position: relative;

  .rules-header {
    font-size: 13px;
    font-weight: 500;
    color: #fa8c16;
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px dashed rgba(250, 140, 22, 0.2);
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .rules-list {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .rule-item {
      background-color: rgba(250, 140, 22, 0.04);
      border-radius: 4px;
      padding: 8px;
      border: 1px solid rgba(250, 140, 22, 0.1);

      .rule-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        :deep(.mtd-tag) {
          border-radius: 4px;
          font-weight: 500;
          height: 24px;
          line-height: 22px;
          padding: 0 8px;

          &.mtd-tag-warning {
            background-color: rgba(250, 173, 20, 0.08);
            color: #faad14;
            border-color: rgba(250, 173, 20, 0.2);
          }
        }

        .rule-name {
          font-weight: 500;
          font-size: 13px;
          color: #333;
        }

        .rule-id {
          font-size: 12px;
          color: #999;
        }
      }

      .rule-content {
        background-color: #fff;
        border-radius: 3px;
        padding: 6px 8px;

        .rule-info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 8px;

          .detail-item {
            margin-bottom: 0;

            .label {
              color: #666;
              font-size: 12px;
            }

            .value {
              color: #333;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

.message-list-block {
  margin-top: 12px;
  background-color: rgba(24, 144, 255, 0.04);
  border-radius: 4px;
  padding: 8px 10px;
  border-left: 3px solid #1890ff;

  .message-list-header {
    font-size: 13px;
    font-weight: 500;
    color: #1890ff;
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px dashed rgba(24, 144, 255, 0.2);
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .message-items {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .message-id-item {
      display: flex;
      align-items: center;
      gap: 6px;
      background-color: #fff;
      border-radius: 4px;
      padding: 4px 8px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

      :deep(.mtd-tag) {
        margin: 0;
      }

      .message-id {
        font-family: monospace;
        font-size: 12px;
        color: #666;
        word-break: break-all;
      }
    }
  }
}

.virtual-content {
  .virtual-basic-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 4px;

    .time-period {
      display: flex;
      align-items: center;
      gap: 12px;

      .time-item {
        display: flex;
        align-items: center;
        gap: 4px;

        .time-icon {
          font-size: 14px;
          color: #1890ff;
        }

        .time-label {
          font-size: 12px;
          color: #666;
        }

        .time-value {
          font-size: 12px;
          color: #333;
        }
      }

      .time-line {
        flex: 1;
        height: 1px;
        background-color: #e8e8e8;
      }
    }

    .end-type-container {
      display: flex;
      align-items: center;
      gap: 8px;

      .detail-label {
        font-size: 12px;
        color: #666;
        width: 60px;
      }

      .empty-value {
        font-size: 12px;
        color: #999;
        font-style: italic;
      }

      .mtd-tag {
        border: 1px solid #fa8c16;
        background-color: #fff7e6;
        color: #fa8c16;
        padding: 0 6px;
        height: 22px;
        line-height: 20px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: normal;
        box-shadow: 0 0 0 1px rgba(250, 173, 20, 0.1);
        transition: all 0.2s;

        &:hover {
          background-color: rgba(250, 173, 20, 0.05);
        }
      }
    }
  }

  .virtual-details-info {
    margin-top: 12px;

    .virtual-info-block {
      background-color: rgba(24, 144, 255, 0.04);
      border-radius: 4px;
      padding: 12px;
      border-left: 3px solid #1890ff;

      .virtual-info-header {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 13px;
        font-weight: 500;
        color: #1890ff;
        margin-bottom: 12px;
        padding-bottom: 4px;
        border-bottom: 1px dashed rgba(24, 144, 255, 0.2);
      }

      .virtual-info-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 10px;

        .detail-item {
          display: flex;
          flex-wrap: wrap;
          align-items: flex-start;

          .label {
            width: 80px;
            flex-shrink: 0;
            font-size: 12px;
            color: #666;
            margin-right: 4px;
          }

          .value {
            flex: 1;
            font-size: 12px;
            color: #333;
            word-break: break-word;
            line-height: 1.5;

            &:empty::before {
              content: '--';
              color: #999;
              font-style: italic;
            }
          }
        }
      }
    }
  }

  .solution-list-block {
    margin-top: 12px;
    background-color: rgba(24, 144, 255, 0.04);
    border-radius: 4px;
    padding: 8px 10px;
    border-left: 3px solid #1890ff;
    position: relative;

    .solution-list-header {
      font-size: 13px;
      font-weight: 500;
      color: #1890ff;
      margin-bottom: 8px;
      padding-bottom: 4px;
      border-bottom: 1px dashed rgba(24, 144, 255, 0.2);
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .solution-items {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .solution-item {
        background-color: #fff;
        border-radius: 4px;
        padding: 10px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

        .solution-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          :deep(.mtd-tag) {
            border-radius: 4px;
            font-weight: 500;
            height: 24px;
            line-height: 22px;
            padding: 0 8px;

            &.mtd-tag-primary {
              background-color: rgba(24, 144, 255, 0.08);
              color: #1890ff;
              border-color: rgba(24, 144, 255, 0.2);
            }
          }

          .solution-name {
            font-size: 12px;
            color: #333;
            font-weight: 500;
          }
        }

        .solution-details {
          margin-top: 8px;
          padding: 8px 12px;
          background-color: #f9f9f9;
          border-radius: 4px;
          font-size: 12px;

          .solution-detail-item {
            display: flex;
            margin-bottom: 6px;

            &:last-child {
              margin-bottom: 0;
            }

            .detail-label {
              width: 70px;
              color: #666;
              flex-shrink: 0;
            }

            .detail-value {
              flex: 1;
              color: #333;
              word-break: break-all;
            }
          }

          .solution-tag {
            margin-left: 8px;
          }

          .solution-json {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 12px;
            white-space: pre-wrap;
            word-break: break-all;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            margin: 0;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
            border-left: 2px solid #1890ff;
            max-height: 200px;
            overflow-y: auto;
          }
        }
      }
    }
  }

  .messages-block {
    margin-top: 12px;
    background-color: rgba(24, 144, 255, 0.04);
    border-radius: 4px;
    padding: 8px 10px;
    border-left: 3px solid #1890ff;
    position: relative;

    .messages-header {
      display: flex;
      align-items: center;
      font-size: 13px;
      font-weight: 500;
      color: #1890ff;
      margin-bottom: 8px;
      padding-bottom: 4px;
      border-bottom: 1px dashed rgba(24, 144, 255, 0.2);

      .mtd-icon {
        margin-right: 6px;
      }

      .toggle-messages-btn {
        margin-left: auto;
        padding: 0;
        height: 24px;

        .toggle-icon {
          transition: transform 0.2s;
          margin-left: 4px;

          &.expanded {
            transform: rotate(180deg);
          }
        }
      }
    }

    .message-items {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding: 4px;
      animation: fadeIn 0.2s ease-in-out;

      .message-id-item {
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #fff;
        border-radius: 4px;
        padding: 4px 8px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

        :deep(.mtd-tag) {
          margin: 0;
        }

        .message-id {
          font-family: monospace;
          font-size: 12px;
          color: #666;
          word-break: break-all;
        }
      }
    }
  }
}

// 基础样式 - 通用的详情项样式
.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 6px;

  .label {
    color: #666;
    width: 65px;
    flex-shrink: 0;
    font-weight: 500;
    padding-right: 2px;
  }

  .value {
    flex: 1;
    margin-left: 6px;
    word-break: break-word;
  }
}

// 豆腐块样式
.doufukuai-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 2px 2px 4px 2px;
}

// 轻交互样式
.light-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 2px 2px 4px 2px;
}

// 机器人调用样式
.robot-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 2px 2px 4px 2px;

  .relation-tasks-container {
    margin-top: 10px;
    border-top: 1px dashed rgba(0, 0, 0, 0.06);
    padding-top: 8px;

    .related-tasks-header {
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      padding: 6px 8px !important;
      background-color: rgba(24, 144, 255, 0.04) !important;
      border-radius: 4px !important;
      margin-left: 0 !important; /* 左对齐 */
      cursor: pointer !important;
      margin-top: 0 !important;
      margin-bottom: 0 !important;

      .detail-title {
        font-size: 13px !important;
        font-weight: 500 !important;
        color: #333 !important;
        position: relative !important;
        padding-left: 12px !important;

        &::before {
          content: '' !important;
          position: absolute !important;
          left: 0 !important;
          top: 50% !important;
          transform: translateY(-50%) !important;
          width: 3px !important;
          height: 14px !important;
          background-color: #1890ff !important;
          border-radius: 2px !important;
        }
      }

      .expand-control {
        display: flex !important;
        align-items: center !important;

        .expand-text {
          font-size: 12px !important;
          color: #1890ff !important;
          margin-right: 4px !important;
        }

        .toggle-icon {
          transition: transform 0.2s !important;
          color: #1890ff !important;

          &.expanded {
            transform: rotate(180deg) !important;
          }
        }
      }
    }

    .related-tasks {
      background-color: #fafcff !important;
      border-radius: 6px !important;
      padding: 10px !important;
      margin-top: 6px !important;
      margin-left: 0 !important; /* 左对齐 */
      border-left: 3px solid #1890ff !important;
      box-shadow: none !important;

      &::before {
        display: none !important;
      }

      .tasks-list-title {
        font-size: 12px !important;
        color: #8c8c8c !important;
        margin-bottom: 10px !important;
        display: flex !important;
        align-items: center !important;

        &::before {
          content: '' !important;
          width: 4px !important;
          height: 4px !important;
          background-color: #1890ff !important;
          border-radius: 50% !important;
          margin-right: 6px !important;
        }
      }

      .detail-item {
        display: flex !important;
        margin-bottom: 8px !important;
        padding-bottom: 8px !important;
        border-bottom: 1px dashed rgba(0, 0, 0, 0.06) !important;

        &:last-child {
          margin-bottom: 0 !important;
          padding-bottom: 0 !important;
          border-bottom: none !important;
        }

        .label {
          min-width: 28px !important;
          height: 28px !important;
          border-radius: 50% !important;
          background-color: rgba(24, 144, 255, 0.08) !important;
          color: #1890ff !important;
          font-weight: 500 !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          margin-right: 12px !important;
          font-size: 12px !important;
          flex-shrink: 0 !important;
          width: auto !important;
        }

        .value {
          flex: 1 !important;
          color: #333 !important;
          word-break: break-all !important;
          line-height: 1.6 !important;
          padding: 4px 0 !important;
          display: flex !important;
          flex-direction: column !important;

          .task-name {
            font-weight: 500 !important;
            color: #262626 !important;
            margin-bottom: 4px !important;
          }

          .task-time {
            color: #888 !important;
            font-size: 12px !important;
            display: flex !important;
            align-items: center !important;

            &::before {
              content: '\e6b4' !important; /* mtdicon-time-o */
              font-family: 'MTDIcon' !important;
              font-size: 14px !important;
              margin-right: 4px !important;
              color: #bfbfbf !important;
            }
          }
        }
      }
    }
  }
}

// 转人工样式
.transfer-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 2px 2px 4px 2px;

  .transfer-basic-info {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .info-row {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 12px;
      margin-bottom: 2px;

      .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 0;

        .label {
          color: #666;
          width: 65px;
          flex-shrink: 0;
          font-weight: 500;
        }

        .value {
          flex: 1;
          margin-left: 6px;
        }

        .status-value {
          margin-left: 4px;
        }
      }
    }
  }

  .skill-changes-block {
    margin-top: 6px;
    margin-left: 0;
    border-left: 2px solid #1890ff;
    background-color: rgba(24, 144, 255, 0.04);
    border-radius: 4px;
    padding: 10px 12px;

    .skill-changes-header {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
      font-weight: 500;
      color: #1890ff;
      margin-bottom: 12px;
      padding-bottom: 6px;
      border-bottom: 1px dashed rgba(24, 144, 255, 0.2);
    }

    .skill-changes-list {
      display: flex;
      flex-direction: column;
      position: relative;

      /* 左侧时间轴 */
      &:before {
        content: '';
        position: absolute;
        left: 8px;
        top: 8px;
        bottom: 8px;
        width: 1px;
        background-color: rgba(24, 144, 255, 0.3);
        z-index: 1;
      }

      .skill-change-item {
        display: flex;
        flex-direction: column;
        position: relative;
        margin-bottom: 20px;
        padding-left: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        /* 时间点圆点 */
        &:before {
          content: '';
          position: absolute;
          left: 4px;
          top: 8px;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #1890ff;
          z-index: 2;
          border: 1px solid #fff;
        }

        .skill-change-time {
          font-size: 12px;
          color: #666;
          margin-bottom: 8px;
          font-weight: 500;

          .time-value {
            display: inline-block;
            background-color: rgba(24, 144, 255, 0.05);
            padding: 2px 6px;
            border-radius: 2px;
          }
        }

        .skill-change-content {
          background-color: white;
          padding: 10px 12px;
          border-radius: 4px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

          .skill-name,
          .skill-reason,
          .skill-strategy {
            display: flex;
            align-items: flex-start;
            margin-bottom: 6px;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              width: 45px;
              min-width: 45px;
              color: #666;
              flex-shrink: 0;
            }

            .value {
              flex: 1;
              color: #333;
              word-break: break-word;
            }
          }

          .skill-name {
            .value {
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}

// 转人工召回样式
.transfer-recall-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 2px 2px 4px 2px;

  .recall-basic-info {
    display: flex;
    flex-direction: column;
    gap: 6px;

    .info-row {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }

  .rules-block {
    margin-top: 10px;

    .rules-header {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
      font-weight: 500;
      color: #fa8c16;
      margin-bottom: 10px;
      padding-bottom: 4px;
      border-bottom: 1px dashed rgba(250, 140, 22, 0.2);
    }

    .rules-list {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .rule-item {
        background-color: rgba(250, 140, 22, 0.04);
        border-radius: 4px;
        padding: 8px;
        border: 1px solid rgba(250, 140, 22, 0.1);

        .rule-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          .rule-name {
            font-weight: 500;
            font-size: 13px;
            color: #333;
          }

          .rule-id {
            font-size: 12px;
            color: #999;
          }
        }

        .rule-content {
          background-color: #fff;
          border-radius: 3px;
          padding: 6px 8px;

          .rule-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 8px;

            .detail-item {
              margin-bottom: 0;

              .label {
                width: 55px;
              }
            }
          }
        }
      }
    }
  }
}

// 自动化任务样式
.automatic-task-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 2px 2px 4px 2px;

  .automatic-task-basic-info {
    display: flex;
    flex-direction: column;
    gap: 6px;

    .info-row {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }

  .solution-block,
  .business-block {
    margin-top: 10px;
    background-color: rgba(24, 144, 255, 0.04);
    border-radius: 4px;
    padding: 8px 10px;
    border-left: 3px solid #1890ff;
    position: relative;

    .solution-header,
    .business-header {
      font-size: 13px;
      font-weight: 500;
      color: #1890ff;
      margin-bottom: 8px;
      padding-bottom: 4px;
      border-bottom: 1px dashed rgba(24, 144, 255, 0.2);
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .solution-grid,
    .business-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
      gap: 8px 16px;

      .detail-item {
        margin-bottom: 0;

        .mono-text {
          font-family: monospace;
          font-size: 12px;
          background-color: rgba(0, 0, 0, 0.03);
          padding: 2px 4px;
          border-radius: 3px;
        }
      }
    }
  }

  .business-block {
    border-left-color: #52c41a;
    background-color: rgba(82, 196, 26, 0.04);

    .business-header {
      color: #52c41a;
      border-bottom-color: rgba(82, 196, 26, 0.2);
    }
  }
}

// 虚拟客服样式
.virtual-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 2px 2px 4px 2px;

  .virtual-basic-info {
    display: flex;
    flex-direction: column;
    gap: 6px;

    .time-period {
      display: flex;
      align-items: center;
      padding-left: 2px;
      margin-bottom: 4px;

      .time-item {
        display: flex;
        align-items: center;
        gap: 4px;

        .time-icon {
          font-size: 14px;
          color: #1890ff;
        }

        .time-label {
          font-size: 12px;
          color: #666;
          width: 60px;
        }

        .time-value {
          font-size: 12px;
          color: #333;
        }
      }

      .time-line {
        flex: 1;
        height: 1px;
        background-color: #e8e8e8;
        margin: 0 10px;
      }
    }

    .end-type-container {
      display: flex;
      align-items: center;
      padding-left: 2px;

      .detail-label {
        font-size: 12px;
        color: #666;
        width: 60px;
      }

      .empty-value {
        font-size: 12px;
        color: #999;
        font-style: italic;
      }
    }
  }

  .virtual-details-info,
  .solution-list-block,
  .messages-block {
    margin-top: 6px;
  }
}
</style>
