<template>
  <div class="timeline-item">
    <div class="timeline-card">
      <div class="timeline-header">
        <div class="timeline-icon">
          <div class="icon-circle">
            <mtd-icon :name="getIconForProcessType(group.serviceProcessType)" />
          </div>
        </div>
        <div class="timeline-title-block">
          <div class="timeline-title">
            {{ group.serviceProcessName }}
            <span class="item-count">({{ group.items.length }})</span>
          </div>
          <div class="timeline-time">{{ dayjs(group.triggerTime).format('HH:mm:ss') }}</div>
        </div>
      </div>

      <div class="timeline-content">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import dayjs from 'dayjs';

defineProps({
  group: {
    type: Object,
    required: true,
  },
});

const getIconForProcessType = (type: string): string => {
  const iconMap: Record<string, string> = {
    doufukuai: 'mtdicon-cube-o',
    notice: 'mtdicon-notification-o',
    light: 'mtdicon-interaction-o',
    robotInvoke: 'mtdicon-robot-o',
    transfer: 'mtdicon-customer-service-o',
    automaticTask: 'mtdicon-flow-o',
    transferRecall: 'mtdicon-return-o',
    aiServiceProgress: 'mtdicon-process-o',
    virtual: 'mtdicon-virtual-o',
    default: 'mtdicon-task-o',
  };

  return iconMap[type] || iconMap.default;
};
</script>

<style lang="scss" scoped>
// 横向时间轴容器
.horizontal-process-timeline {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  margin: 4px 0 12px;

  // 滚动按钮样式
  .scroll-button {
    position: absolute;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: rgba(24, 144, 255, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 10;
    color: #1890ff;
    transition: all 0.3s;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

    &:hover {
      background-color: rgba(24, 144, 255, 0.2);
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.25);
    }

    &.scroll-left {
      left: -14px;
    }

    &.scroll-right {
      right: -14px;
    }
  }

  // 时间轴滚动容器
  .timeline-scroll-container {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    padding: 4px 0 8px;

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
  }

  // 横向时间轴样式
  .process-timeline {
    display: flex;
    padding: 0 8px;
    min-width: 100%;

    // 时间轴项目样式
    .timeline-item {
      flex: 0 0 auto;
      padding: 0 8px;
      margin-right: 4px;
      min-width: 250px;
      max-width: 320px;

      // 时间轴卡片样式
      .timeline-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        display: flex;
        flex-direction: column;
        height: 100%;
        border: 1px solid #ebedf0;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
          transform: translateY(-2px);
        }

        // 时间轴头部样式
        .timeline-header {
          padding: 8px 12px;
          display: flex;
          align-items: center;
          border-bottom: 1px solid #f0f0f0;
          background-color: #f8fafc;

          .timeline-icon {
            margin-right: 12px;

            .icon-circle {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background-color: rgba(24, 144, 255, 0.1);
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 16px;
              color: #1890ff;
              box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.05);
              transition: all 0.2s ease;
            }
          }

          .timeline-title-block {
            flex: 1;
            display: flex;
            align-items: center;

            .timeline-title {
              font-size: 14px;
              font-weight: 600;
              color: #222;
              line-height: 1.4;
              margin-right: 8px;

              .item-count {
                font-size: 12px;
                color: #888;
                font-weight: normal;
                margin-left: 4px;
              }
            }

            .timeline-time {
              font-size: 12px;
              color: #888;
              background-color: rgba(0, 0, 0, 0.03);
              padding: 2px 6px;
              border-radius: 10px;
            }
          }
        }

        // 时间轴内容区域
        .timeline-content {
          padding: 8px;
          flex: 1;
          overflow-y: auto;
          max-height: 300px;
        }
      }
    }
  }
}

// 任务展开相关样式
.related-tasks-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin-top: 4px;
  margin-bottom: 6px;
  background-color: rgba(24, 144, 255, 0.05);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background-color: rgba(24, 144, 255, 0.1);
  }

  &:active {
    background-color: rgba(24, 144, 255, 0.15);
    transform: translateY(1px);
  }

  .detail-title {
    font-weight: 600;
    color: #1890ff;
    margin: 0;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 14px;
      background-color: #1890ff;
      border-radius: 2px;
    }
  }

  .expand-control {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #1890ff;

    .expand-text {
      font-size: 12px;
    }

    .toggle-icon {
      font-size: 14px;
      transition: transform 0.2s ease;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }
}
</style>
