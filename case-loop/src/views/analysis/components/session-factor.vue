<template>
  <mtd-card shadow="hover" :border="false" class="record-info-card">
    <template #title>
      <div style="display: flex; align-items: center; justify-content: space-between; width: 100%">
        <div style="display: flex; align-items: center; gap: 4px">
          会话因子
        </div>
        <!-- 展开收起按钮 -->
        <mtd-button
            type="text"
            size="mini"
            @click="toggleCollapse"
            class="expand-toggle-btn"
        >
          {{ isCollapsed ? '展开' : '收起' }}
          <mtd-icon
              :name="isCollapsed ? 'mtdicon-arrow-down' : 'mtdicon-arrow-up'"
              style="margin-left: 2px; font-size: 12px"
          />
        </mtd-button>
      </div>
    </template>
    
    <div v-show="!isCollapsed" class="record-info-card-container">
      <!-- 会话因子内容 -->
      <div class="record-info-card-content">
        <div class="record-info-card-content-item">
          <div class="record-info-card-content-item-value">
            <div class="empty-content">
              暂无会话因子信息
            </div>
          </div>
        </div>
      </div>
    </div>
  </mtd-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps<{
  sessionId: string;
  currentMessage?: {
    messageId: string;
    originMessage: string;
    [key: string]: any;
  } | null;
}>();

// 控制面板展开/收起
const isCollapsed = ref(false);
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 这里可以添加获取会话因子数据的逻辑
// const fetchSessionFactor = async () => {
//   // TODO: 实现获取会话因子的API调用
// };
</script>

<style lang="scss" scoped>
.record-info-card {
  flex-shrink: 0;

  :deep(.mtd-card-header) {
    padding: 12px 16px;
    background: #fff;
    min-height: auto;
    height: 40px;
  }

  :deep(.mtd-card-body) {
    padding: 16px;
  }

  .expand-toggle-btn {
    color: #1890ff !important;
    padding: 2px 8px !important;
    font-size: 12px !important;
    height: 24px !important;
    line-height: 20px !important;
    border-radius: 4px !important;

    &:hover {
      background-color: rgba(24, 144, 255, 0.1) !important;
    }
  }

  .record-info-card-container {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .record-info-card-content {
      display: flex;
      flex-direction: column;
      gap: 8px;

      &-item {
        display: flex;
        align-items: flex-start;
        gap: 4px;

        &-value {
          display: flex;
          flex-direction: column;
          gap: 4px;
          overflow: hidden;
          color: #000;
          flex: 1;
          width: 100%;

          .empty-content {
            color: #999;
            font-size: 14px;
            text-align: center;
            padding: 20px 0;
          }
        }
      }
    }
  }
}
</style> 