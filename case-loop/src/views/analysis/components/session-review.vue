<template>
  <mtd-card shadow="hover" :border="false" class="record-info-card">
    <template #title>
      <div style="display: flex; align-items: center; justify-content: space-between; width: 100%">
        <div style="display: flex; align-items: center; gap: 4px">
          评论
        </div>
        <!-- 展开收起按钮 -->
        <mtd-button
            type="text"
            size="mini"
            @click="toggleCollapse"
            class="expand-toggle-btn"
        >
          {{ isCollapsed ? '展开' : '收起' }}
          <mtd-icon
              :name="isCollapsed ? 'mtdicon-arrow-down' : 'mtdicon-arrow-up'"
              style="margin-left: 2px; font-size: 12px"
          />
        </mtd-button>
      </div>
    </template>
    
    <div v-show="!isCollapsed" class="record-info-card-container">
      <!-- 评论输入区域 -->
      <div class="record-info-card-content">
        <div class="record-info-card-content-item">
          <div class="record-info-card-content-item-value">
            <div v-if="!props.currentMessage?.messageId" class="message-hint">
              请先在对话详情中选择一条消息后再进行评论
            </div>
            <mtd-textarea
              v-model="reviewContent"
              :rows="4"
              placeholder="请输入您的评论..."
              class="review-textarea"
              :disabled="!props.currentMessage?.messageId"
            />
          </div>
        </div>
        <div class="review-submit-section">
          <mtd-button 
            type="primary" 
            :loading="isSubmitting"
            :disabled="!reviewContent.trim() || !props.currentMessage?.messageId"
            @click="submitReview"
            size="small"
          >
            提交评论
          </mtd-button>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="record-info-card-content">
        <div class="record-info-card-content-item">
          <div class="record-info-card-content-item-value">
            <div class="filter-controls">
              <mtd-button 
                type="text" 
                size="small" 
                @click="toggleOnlyShowMine"
                class="filter-btn"
                :class="{ active: onlyShowMine }"
              >
                仅看自己评论
              </mtd-button>
              <mtd-button 
                type="text" 
                size="small" 
                @click="toggleShowAllMessages"
                class="filter-btn"
                :class="{ active: !showAllMessages }"
              >
                仅看选中消息评论
              </mtd-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 评论列表 -->
      <div class="record-info-card-content">
        <div class="record-info-card-content-item">
          <div class="record-info-card-content-item-value">
            <div v-if="filteredReviews.length === 0" class="empty-reviews">
              暂无评论
            </div>
            <div v-else class="review-items">
              <div v-for="review in filteredReviews" :key="review.id" class="review-item">
                <div class="review-item-header">
                  <span class="reviewer-name">{{ review.reviewName }}</span>
                  <span class="review-time">{{ formatDate(review.reviewDate) }}</span>
                </div>
                <div class="review-item-content">{{ review.reviewContent }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </mtd-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import { addReview, getReviewList } from '../../../api/workbench-session';

interface ReviewItem {
  id: number;
  sessionId: string;
  messageId: string;
  reviewContent: string;
  reviewMis: string;
  reviewName: string;
  reviewDate: string;
  likeNumber: number;
}

const props = defineProps<{
  sessionId: string;
  currentUserMis?: string;
  currentMessage?: {
    messageId: string;
    originMessage: string;
    [key: string]: any;
  } | null;
}>();

// 控制面板展开/收起
const isCollapsed = ref(false);
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 切换显示全部消息评论
const toggleShowAllMessages = () => {
  showAllMessages.value = !showAllMessages.value;
};

// 切换仅看自己评论
const toggleOnlyShowMine = () => {
  onlyShowMine.value = !onlyShowMine.value;
};

// 评论相关状态
const reviewContent = ref('');
const isSubmitting = ref(false);
const reviewList = ref<ReviewItem[]>([]);
const onlyShowMine = ref(false);
const showAllMessages = ref(false);

// 过滤后的评论列表
const filteredReviews = computed(() => {
  // 确保 reviewList.value 是数组
  let reviews = Array.isArray(reviewList.value) ? reviewList.value : [];
  
  // 如果不显示全部消息评论，则只显示当前消息的评论
  if (!showAllMessages.value && props.currentMessage?.messageId) {
    reviews = reviews.filter(review => review.messageId === props.currentMessage!.messageId);
  }
  
  // 如果只看自己的评论
  if (onlyShowMine.value) {
    reviews = reviews.filter(review => review.reviewMis === props.currentUserMis);
  }
  
  return reviews;
});

// 提交评论
const submitReview = async () => {
  if (!reviewContent.value.trim() || !props.currentMessage?.messageId) {
    message.warning('请先选择消息后再进行评论');
    return;
  }
  
  try {
    isSubmitting.value = true;
    await addReview({
      sessionId: props.sessionId,
      messageId: props.currentMessage.messageId,
      reviewContent: reviewContent.value.trim()
    });
    
    message.success('评论提交成功');
    reviewContent.value = '';
    await fetchReviewList();
  } catch (error) {
    console.error('提交评论失败:', error);
    message.error('提交评论失败，请重试');
  } finally {
    isSubmitting.value = false;
  }
};

// 获取评论列表（获取整个会话的所有评论）
const fetchReviewList = async () => {
  try {
    const result = await getReviewList(props.sessionId);
    // 确保结果是数组
    reviewList.value = Array.isArray(result) ? result : [];
  } catch (error) {
    console.error('获取评论列表失败:', error);
    message.error('获取评论列表失败');
    // 出错时设置为空数组
    reviewList.value = [];
  }
};

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 初始化
fetchReviewList();
</script>

<style lang="scss" scoped>
.record-info-card {
  flex-shrink: 0;

  :deep(.mtd-card-header) {
    padding: 12px 16px;
    background: #fff;
    min-height: auto;
    height: 40px;
  }

  :deep(.mtd-card-body) {
    padding: 16px;
  }

  .expand-toggle-btn {
    color: #1890ff !important;
    padding: 2px 8px !important;
    font-size: 12px !important;
    height: 24px !important;
    line-height: 20px !important;
    border-radius: 4px !important;

    &:hover {
      background-color: rgba(24, 144, 255, 0.1) !important;
    }
  }

  .record-info-card-container {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .record-info-card-content {
      display: flex;
      flex-direction: column;
      gap: 8px;

      &-item {
        display: flex;
        align-items: flex-start;
        gap: 4px;

        &-label {
          flex: 0 0 auto;
          font-size: 14px;
          font-weight: 500;
          color: #111925a6;
          margin-right: 5px;
          min-width: 80px;
        }

        &-value {
          display: flex;
          flex-direction: column;
          gap: 4px;
          overflow: hidden;
          color: #000;
          flex: 1;
          width: 100%;

          .review-textarea {
            width: 100%;
            resize: vertical;
          }

          .message-hint {
            color: #999;
            font-size: 12px;
            margin-bottom: 8px;
            padding: 8px 12px;
            background: #f5f5f5;
            border-radius: 4px;
            border-left: 3px solid #faad14;
          }

          .empty-reviews {
            color: #999;
            font-size: 14px;
            text-align: center;
            padding: 20px 0;
          }

          .filter-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;

            .filter-btn {
              color: #666 !important;
              padding: 4px 12px !important;
              font-size: 12px !important;
              height: 28px !important;
              line-height: 20px !important;
              border-radius: 4px !important;
              border: 1px solid #d9d9d9 !important;
              background: #fff !important;
              transition: all 0.2s !important;

              &:hover {
                color: #1890ff !important;
                border-color: #1890ff !important;
                background-color: rgba(24, 144, 255, 0.05) !important;
              }

              &.active {
                color: #1890ff !important;
                border-color: #1890ff !important;
                background-color: rgba(24, 144, 255, 0.1) !important;
                font-weight: 500 !important;
              }
            }
          }

          .review-items {
            .review-item {
              padding: 12px;
              border: 1px solid #f0f0f0;
              border-radius: 4px;
              margin-bottom: 8px;
              background: #fafafa;

              &:last-child {
                margin-bottom: 0;
              }

              .review-item-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;

                .reviewer-name {
                  font-weight: 500;
                  color: #333;
                  font-size: 14px;
                }

                .review-time {
                  color: #999;
                  font-size: 12px;
                }
              }

              .review-item-content {
                color: #666;
                line-height: 1.5;
                word-break: break-all;
                font-size: 14px;
                white-space: pre-wrap;
              }
            }
          }
        }
      }
    }

    .review-submit-section {
      margin-top: 8px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style> 