<template>
  <a-cascader
    :options="workspaceListAdapter"
    v-model:value="props.selectedWorkspaceAndApp"
    :allowClear="true"
    @change="handleAppChange"
    placeholder="请选择空间/应用"
  />
</template>

<script lang="ts" setup>
import { getWorkspaceAndAppList } from '@/api/workbench-session';
import { message } from 'ant-design-vue';
import { computed, onBeforeMount, ref } from 'vue';

const props = defineProps<{
  selectedWorkspaceAndApp?: Array<string> | null;
}>();

const emit = defineEmits<{
  (e: 'change', workspaceAndApp: [string, string], displayText?: string): void;
}>();

const workspaceList = ref<TWorkspaceResponseData>([]);
const fetchWorkspaceAndAppList = async () => {
  try {
    const res = await getWorkspaceAndAppList();
    workspaceList.value = res.data || [];
  } catch (error) {
    message.error(error?.message || '获取空间失败');
  }
};

/**
 * 接口原始数据适配cascader组件
 */
const workspaceListAdapter = computed(() => {
  // 首先按 workspaceId 分组合并数据
  const mergedWorkspaceMap: Record<string, {
    workspaceId: string;
    workspaceName: string;
    applicationList: Array<{
      applicationId: string;
      applicationName: string;
      [key: string]: any;
    }>;
  }> = {};

  workspaceList.value.forEach(({ workspaceId, workspaceName, applicationList }) => {
    if (!mergedWorkspaceMap[workspaceId]) {
      // 如果这个 workspaceId 还没有被添加，创建一个新条目
      mergedWorkspaceMap[workspaceId] = {
        workspaceId,
        workspaceName,
        applicationList: []
      };
    }

    // 将当前项的 applicationList 合并到对应的 workspaceId 条目中
    if (applicationList && Array.isArray(applicationList)) {
      // 使用 Set 来跟踪已添加的应用，避免重复
      const existingAppIds = new Set(
        mergedWorkspaceMap[workspaceId].applicationList.map(app => app.applicationId)
      );

      applicationList.forEach(app => {
        // 只添加尚未存在的应用
        if (!existingAppIds.has(app.applicationId)) {
          mergedWorkspaceMap[workspaceId].applicationList.push(app);
          existingAppIds.add(app.applicationId);
        }
      });
    }
  });

  // 将合并后的数据转换为所需的格式
  return Object.values(mergedWorkspaceMap).map(({ workspaceId, workspaceName, applicationList }) => ({
    value: workspaceId,
    label: workspaceName,
    children: (applicationList || []).map(({ applicationId, applicationName }) => ({
      value: applicationId,
      label: applicationName,
    })),
  }));
});

onBeforeMount(() => {
  fetchWorkspaceAndAppList();
});

const handleAppChange = (value: [string, string]) => {
  // 获取显示文本
  let displayText = '';

  if (value && value[0]) {
    // 从适配后的数据中查找工作空间
    const workspace = workspaceListAdapter.value.find(ws => ws.value === value[0]);
    if (workspace) {
      if (!value[1]) {
        // 只选择了工作空间
        displayText = workspace.label;
      } else {
        // 选择了工作空间和应用
        const app = (workspace.children || []).find(app => app.value === value[1]);
        if (app) {
          displayText = `${workspace.label} / ${app.label}`;
        }
      }
    }
  }

  emit('change', value, displayText);
};
</script>

<style lang="scss" scoped></style>
