/**
 * CI Setup Script
 * 
 * This script handles the proper installation of dependencies for the CI environment.
 * It ensures that all necessary packages are installed in both the root directory
 * and the case-loop directory.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Log function
function log(message) {
  console.log(`[CI-SETUP] ${message}`);
}

// Helper function to execute a command and log its output
function executeCommand(command, cwd) {
  try {
    log(`Executing: ${command} in ${cwd || 'current directory'}`);
    const output = execSync(command, { 
      cwd, 
      stdio: 'pipe',
      encoding: 'utf-8'
    });
    log(output);
    return true;
  } catch (error) {
    log(`Error executing command: ${command}`);
    log(error.message);
    if (error.stdout) log(`stdout: ${error.stdout}`);
    if (error.stderr) log(`stderr: ${error.stderr}`);
    return false;
  }
}

// Main function
async function main() {
  log('Starting CI setup');
  
  // Install root dependencies
  log('Installing root dependencies');
  if (!executeCommand('npm install', process.cwd())) {
    process.exit(1);
  }
  
  // Check if case-loop directory exists
  const caseLoopDir = path.join(process.cwd(), 'case-loop');
  if (!fs.existsSync(caseLoopDir)) {
    log('Error: case-loop directory not found!');
    process.exit(1);
  }
  
  // Install case-loop dependencies
  log('Installing case-loop dependencies');
  if (!executeCommand('npm install', caseLoopDir)) {
    process.exit(1);
  }
  
  // Verify Vue CLI plugins are installed
  log('Verifying Vue CLI plugins');
  const nodeModulesDir = path.join(caseLoopDir, 'node_modules');
  const requiredModules = [
    '@vue/cli-plugin-babel',
    '@vue/cli-plugin-eslint',
    '@vue/cli-service'
  ];
  
  const missingModules = requiredModules.filter(
    module => !fs.existsSync(path.join(nodeModulesDir, module))
  );
  
  if (missingModules.length > 0) {
    log(`Missing Vue CLI modules: ${missingModules.join(', ')}`);
    log('Installing missing Vue CLI modules explicitly');
    const installCmd = `npm install ${missingModules.join(' ')} --save-dev`;
    if (!executeCommand(installCmd, caseLoopDir)) {
      process.exit(1);
    }
  }
  
  log('CI setup completed successfully');
}

main().catch(error => {
  log(`Unhandled error: ${error.message}`);
  process.exit(1);
}); 