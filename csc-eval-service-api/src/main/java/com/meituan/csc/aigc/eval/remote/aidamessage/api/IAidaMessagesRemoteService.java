package com.meituan.csc.aigc.eval.remote.aidamessage.api;

import com.meituan.csc.aigc.eval.remote.aidamessage.dto.AidaMessagesConditionDTO;
import com.meituan.csc.aigc.eval.remote.aidamessage.dto.AidaMessagesDTO;
import com.meituan.csc.aigc.eval.remote.aidamessage.dto.AidaMessagesExtraInfoDTO;
import com.meituan.csc.aigc.eval.remote.common.dto.ServiceResponse;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Description: aida messags查询服务接口
 * Date: 2025/4/14 16:15
 * Author: libin111
 */
public interface IAidaMessagesRemoteService {

    /**
     * 根据sessionId查询aida消息列表
     *
     * @param sessionId 会话id
     * @return aida消息列表
     */
    ServiceResponse<List<AidaMessagesDTO>> queryAidaMessagesBySessionId(String sessionId);

    /**
     * 根据sessionId集合查询aida消息列表
     *
     * @param sessionIdSet 会话id集合
     * @return aida消息列表
     */
    ServiceResponse<List<AidaMessagesDTO>> queryAidaMessagesBySessionIdSet(Set<String> sessionIdSet);

    /**
     * 根据查询条件查询aida消息列表
     *
     * @param conditionDTO 查询条件
     * @return aida消息列表
     */
    ServiceResponse<List<AidaMessagesDTO>> queryAidaMessagesByCondition(AidaMessagesConditionDTO conditionDTO);

    /**
     * 根据aida messages created_at时间范围查询aida messages的extraInfo信息
     *
     * @param startDate created_at开始时间
     * @param endDate created_at结束时间
     * @return aida extraInfo信息列表
     */
    ServiceResponse<List<AidaMessagesExtraInfoDTO>> queryAidaMessagesExtraInfoByCreatedAtBetween(Date startDate, Date endDate);
}
