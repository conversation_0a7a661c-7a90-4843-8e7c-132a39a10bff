package com.meituan.csc.aigc.eval.remote.training.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-21 11:40
 * @description 指标结果范围
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MetricItemDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private List<String> resultEnum;
    private ManualAnnotationDTO manualAnnotation;
}