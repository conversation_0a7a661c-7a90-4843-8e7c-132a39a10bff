package com.meituan.csc.aigc.eval.config;

import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */

@Slf4j
@Configuration
public class DxPushThriftClientConfiguration {

    @Bean(name = "pushMessageService", destroyMethod = "destroy")
    public ThriftClientProxy pushMessageService() {
        log.info("pushMessageService init");
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setServiceInterface(PushMessageServiceI.class);
        proxy.setRemoteAppkey("com.sankuai.xm.pubapi");
        proxy.setNettyIO(true);
        proxy.setFilterByServiceName(true);
        proxy.setTimeout(10000);
        return proxy;
    }



}
