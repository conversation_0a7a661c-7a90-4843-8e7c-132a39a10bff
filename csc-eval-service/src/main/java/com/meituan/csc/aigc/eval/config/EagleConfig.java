package com.meituan.csc.aigc.eval.config;

import com.sankuai.meituan.poros.client.PorosHighLevelClientBuilder;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ES配置类型
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Slf4j
@Configuration
public class EagleConfig {
    /**
     * ES集群名
     */
    @Value("${es.case.analysis.cluster.name}")
    private String clusterName;

    /**
     * AppKey
     */
    @Value("${es.case.analysis.appkey}")
    private String appKey;

    @Bean("caseAnalysisClient")
    public RestHighLevelClient sessionClient() {
        RestHighLevelClient porosClient = PorosHighLevelClientBuilder.builder()
                .clusterName(clusterName)
                .appKey(appKey)
                .callESDirectly(true)
                .build();
        return porosClient;
    }

}
