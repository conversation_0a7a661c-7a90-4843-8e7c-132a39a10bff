package com.meituan.csc.aigc.eval.config;

/**
 * 模块
 */
public enum Module {

    EVAL("EVAL", "评测系统"),
    EVAL_DATASET("EVAL_DATASET", "评测数据集"),
    EVAL_TASK("EVAL_TASK", "评测任务"),
    EVAL_ARENA("EVAL_ARENA", "评测竞技场"),
    EVAL_ARENA_TASK("EVAL_ARENA_TASK", "评测竞技场（任务）");

    private String code;
    private String info;

    Module(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

}
