package com.meituan.csc.aigc.eval.config.cors;


import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsProcessor;
import org.springframework.web.cors.CorsUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Slf4j
public class CorsConfig extends OncePerRequestFilter {

    private CorsProcessor processor = new DefaultCorsProcessor();

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        try {
            String origin = request.getHeader(HttpHeaders.ORIGIN);
            if (!CorsUtils.isCorsRequest(request)) {
                filterChain.doFilter(request, response);
                return;
            }
            CorsConfiguration corsConfiguration = new CorsConfiguration();
            corsConfiguration.applyPermitDefaultValues();
            corsConfiguration.setAllowedMethods(Arrays.asList(CorsConfiguration.ALL));
            corsConfiguration.addAllowedHeader("*");
            corsConfiguration.setAllowCredentials(true);

            corsConfiguration.setAllowedOrigins(Arrays.asList("*"));

            boolean isValid = this.processor.processRequest(corsConfiguration, request, response);
            log.info("uri:{} , method : {}, valid: {}", request.getRequestURI(), JSON.toJSONString(request.getMethod()), isValid);
            if (!isValid || CorsUtils.isPreFlightRequest(request)) {
                if (!isValid) {
                    String errMsg = String.format("Origin=<%s>. URI=<%s>", origin, request.getRequestURI());
                    log.error(errMsg);
                }
                return;
            }
            filterChain.doFilter(request, response);
        } catch (Exception e) {
            Cat.logError("cors exception:", e);
            throw e;
        }
    }
}
