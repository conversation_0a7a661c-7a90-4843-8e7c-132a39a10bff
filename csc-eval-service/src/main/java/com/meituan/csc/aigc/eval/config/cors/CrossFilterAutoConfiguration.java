package com.meituan.csc.aigc.eval.config.cors;


import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class CrossFilterAutoConfiguration {

    @Bean
    public FilterRegistrationBean corsFilterRegistrationBean() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(new CorsConfig());
        filterRegistrationBean.addUrlPatterns("/*");
        filterRegistrationBean.setName("corsFiler");
        filterRegistrationBean.setOrder(0);
        return filterRegistrationBean;
    }

}

