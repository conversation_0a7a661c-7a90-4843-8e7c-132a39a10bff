package com.meituan.csc.aigc.eval.config.mtrace;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MtraceFilterConfiguration {
    private static final int MTRACE_FILTER_ORDER = 1;

    @Bean
    public FilterRegistrationBean mTraceFilter() {
        com.meituan.mtrace.http.TraceFilter filter = new com.meituan.mtrace.http.TraceFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/*");
        registration.setName("mtrace-filter");
        registration.setOrder(MTRACE_FILTER_ORDER);
        return registration;
    }
}
