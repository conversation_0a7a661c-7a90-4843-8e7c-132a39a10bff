package com.meituan.csc.aigc.eval.config.pike;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dianping.lion.client.Lion;
import com.meituan.csc.aigc.eval.config.ResultCode;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.controller.vo.Result;
import com.meituan.csc.aigc.eval.dto.pike.PikeEventDTO;
import com.meituan.csc.aigc.eval.exception.EvalException;
import com.meituan.csc.aigc.eval.helper.PikeThreadLocalHelper;
import com.meituan.csc.aigc.eval.param.pike.PikeCacheParam;
import com.meituan.csc.aigc.eval.service.strategy.pike.PikeStrategyService;
import com.meituan.csc.aigc.eval.utils.CommonUtils;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.sankuai.pike.message.api.common.enums.DeviceStatusEnum;
import com.sankuai.pike.message.api.rpc.business.entity.*;
import com.sankuai.pike.message.sdk.PikeMessageServerClient;
import com.sankuai.pike.message.sdk.listener.ConnectListener;
import com.sankuai.pike.message.sdk.listener.ListenerHolder;
import com.sankuai.pike.message.sdk.listener.MessageListener;
import com.sankuai.pike.message.sdk.lite.callback.MessageCallback;
import com.sankuai.pike.message.sdk.lite.entity.MessageRequest;
import com.sankuai.pike.message.sdk.lite.entity.MessageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PikeService {


    @Autowired
    private List<PikeStrategyService> strategyList;

    private Map<Integer, PikeStrategyService> strategyMap;

    @PostConstruct
    public void init() {
//        if (CollectionUtils.isNotEmpty(strategyList) && MapUtils.isEmpty(strategyMap)) {
//            strategyMap = strategyList.stream().collect(Collectors.toMap(PikeStrategyService::getCode, Function.identity()));
//        }
//        try {
//            // 初始化Pike客户端
//            PikeMessageServerClient.preInitRpcClient();
//            // 设置执行监听器的线程池，可选操作，若不设置则使用RPC默认线程池
//            // 创建并设置执行监听器的线程池
//            Executor executor = new ThreadPoolExecutor(4, 4, 0, TimeUnit.SECONDS, new LinkedBlockingQueue<>());
//            ListenerHolder.scheduleListenerOn(executor);
//
//            // 注册连接监听器
//            ListenerHolder.registerLister(getBizId(), new ConnectListener() {
//                @Override
//                public ConnectResponse onConnect(ConnectRequest connectRequest) {
//                    ConnectResponse response = new ConnectResponse();
//                    // 在这里实现连接鉴权逻辑
//                    response.setSuccess(true);
//                    String token = connectRequest.getToken();
//                    log.info("新连接建立: {}", token);
//                    return response;
//                }
//
//                @Override
//                public void onDisconnect(DisconnectRequest disconnectRequest) {
//                    String token = disconnectRequest.getToken();
//                    log.info("连接断开：{}", token);
//                }
//            });
//
//            // 注册消息监听器
//            ListenerHolder.registerLister(getBizId(), new MessageListener() {
//                @Override
//                public void onClientMessage(ClientMessageRequest clientMessage) {
//                    // 接受客户端消息，实现业务逻辑
//                    processClientMessage(clientMessage);
//                }
//
//                @Override
//                public void onSendMessageResult(ServerMessageResult serverMessageResult) {
//                    log.debug("消息发送结果：{}", serverMessageResult);
//                }
//            });
//
//            // 发布监听器
//            ListenerHolder.publishListenerAsPigeonService();
//            log.info("Pike服务初始化完成");
//        } catch (Exception e) {
//            log.error("Pike服务初始化失败", e);
//            // 考虑是否需要重试或者抛出异常
//        }
    }

    public void sendMessageToWebClient(String token, String message) {
        MessageRequest request = MessageRequest.newTokenMessageRequest(getBizId(), token);
        request.setStringContent(message);
        // 10秒超时
        request.setRequestTimeout(10);
        PikeMessageServerClient.sendMessageToSDK(request, new MessageCallback() {
            @Override
            public void onResponse(MessageResponse response) {
                log.info("消息发送成功：id={},message={}", response.getMessageId(), message);
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("消息发送失败：", throwable);
            }
        });
    }

    /**
     * 实现具体的消息处理逻辑
     */
    private void processClientMessage(ClientMessageRequest clientMessage) {
        log.info("收到客户端消息：{}", clientMessage);
        String token = clientMessage.getToken();
        String messageString = new String(clientMessage.getMessage());

        try {
            processMessage(token, messageString);
        } catch (Exception e) {
            exceptionHandle(token, messageString, e);
        } finally {
            PikeThreadLocalHelper.removeCache();
        }
    }

    private void exceptionHandle(String token, String messageString, Exception e) {
        try {
            log.error("执行Pike请求时发生异常 : token-{}, message-{}", token, messageString, e);
            Integer scene = JSONObject.parseObject(messageString).getInteger("scene");
            String errorMsg = e instanceof EvalException ? e.getMessage() : "接口发生异常，请联系开发同学排查";
            sendMessageToWebClient(token, JSONObject.toJSONString(new PikeEventDTO<>(scene, Result.fail(ResultCode.SERVICE_EXCEPTION.getCode(), errorMsg)), SerializerFeature.WriteMapNullValue));
        } catch (Exception ex) {
            log.error("发送异常消息失败: {}", messageString, ex);
        }
    }

    private void processMessage(String token, String messageString) {
        CommonUtils.checkEval(!StringUtils.isEmpty(messageString), "消息为空");
        JSONObject parsedObject = JSONObject.parseObject(messageString);
        CommonUtils.checkEval(parsedObject != null, "消息解析失败");

        Integer scene = parsedObject.getInteger("scene");
        CommonUtils.checkEval(scene != null, "场景为空");

        PikeEventDTO.User user = parsedObject.getObject("user", PikeEventDTO.User.class);
        PikeThreadLocalHelper.setCache(new PikeCacheParam(token, user));

        PikeStrategyService pikeStrategyService = strategyMap.get(scene);
        CommonUtils.checkEval(pikeStrategyService != null, "场景不存在");

        String pikeEventResult = pikeStrategyService.processEvent(messageString);
        log.info("Pike消息处理结束, 请求:{}, :结果 {}", messageString, pikeEventResult);
        sendMessageToWebClient(token, pikeEventResult);
    }


    /**
     * 通过token测试Pike连接,  OFFLINE: -1,ONLINE: 1;
     */
    public DeviceStatusEnum getPikeConnectStatus(String token, int requestTimeout) {
        log.info("测试pike连接,token: {}", token);
        return PikeMessageServerClient.checkConnection(token, requestTimeout);
    }

    private String getBizId() {
        return Lion.getString(ConfigUtil.getAppkey(), LionConstants.PIKE_BIZ_ID, "eval-pike-test");
    }

    public void processMessage(ClientMessageRequest clientMessage) {
        processClientMessage(clientMessage);
    }
}