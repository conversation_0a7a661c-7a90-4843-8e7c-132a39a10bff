package com.meituan.csc.aigc.eval.config.sso;


import com.meituan.csc.aigc.eval.constants.KmsConstants;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.it.sso.sdk.listener.SSOListener;
import com.sankuai.it.sso.sdk.spring.FilterFactoryBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.web.filter.DelegatingFilterProxy;

import static javax.servlet.DispatcherType.REQUEST;

@Configuration
public class FilterConfiguration {

    @Value("${sso.clientId}")
    private String clientId;
    @Bean
    @Order(Integer.MAX_VALUE - 1)
    public FilterRegistrationBean mtFilter() {
        DelegatingFilterProxy filter = new DelegatingFilterProxy();
        FilterRegistrationBean<DelegatingFilterProxy> registration = new FilterRegistrationBean<>();
        filter.setTargetBeanName("mtFilterBean");
        filter.setTargetFilterLifecycle(true);

        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setDispatcherTypes(REQUEST);
        registration.setName("mtFilter");
        registration.setOrder(1);
        return registration;
    }

    /**
     * mtFilter配置
     * 可在本地配置具体的值，每一配置的含义参考第三节
     */
    @Bean
    public FilterFactoryBean mtFilterBean() throws KmsResultNullException {
        FilterFactoryBean filterFactoryBean = new FilterFactoryBean();
        filterFactoryBean.setClientId(clientId);
        String ssoSecret = Kms.getByName(KmsConstants.APP_KEY,KmsConstants.SSO_SECRET);
        filterFactoryBean.setSecret(ssoSecret);
        filterFactoryBean.setSsoListenerBean(new EvalSsoListener());
        /*
         不需要 SSO 检查的 Url 配置, 多个以逗号分隔，允许换行
         单独配 includedUriList，includedUriList 以外的链接都不检查sso登录
         单独配 excludedUriList ， excludedUriList 以外的链接都会检查sso登录
         includedUriList，excludedUriList 都要有的时候，includedUriList 优先级更高  -->
         */
        filterFactoryBean.setExcludedUriList("/static/**,/monitor/alive,/api/aida/**");

        return filterFactoryBean;
    }

}
