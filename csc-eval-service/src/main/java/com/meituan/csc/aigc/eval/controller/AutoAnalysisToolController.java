package com.meituan.csc.aigc.eval.controller;

import com.meituan.csc.aigc.eval.controller.vo.Result;
import com.meituan.csc.aigc.eval.param.autoanalysistool.AnalysisResultResponse;
import com.meituan.csc.aigc.eval.param.autoanalysistool.TaskCreateParam;
import com.meituan.csc.aigc.eval.service.autoanalysistool.AutoAnalysisToolInnerService;
import com.meituan.csc.aigc.eval.service.autoanalysistool.AutoAnalysisToolService;
import com.meituan.csc.aigc.eval.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 自动化分析工具控制器
 * 
 * <AUTHOR>
 * @date 2025-04-12
 */
@Slf4j
@RestController
@RequestMapping("/api/aigc/eval/automation")
@Api(tags = "自动化分析工具接口")
public class AutoAnalysisToolController extends BaseController {
    
    /**
     * 自动化分析工具服务
     */
    @Autowired
    private AutoAnalysisToolService autoAnalysisToolService;

    /**
     * 自动化分析工具内部服务
     */
    @Autowired
    private AutoAnalysisToolInnerService autoAnalysisToolInnerService;
    
    /**
     * 上传Excel文件到S3
     * @param file   上传文件
     * @param misId  用户mis
     * @return 上传结果
     */
    @PostMapping("/upload")
    @ApiOperation(value = "上传Excel文件", notes = "上传Excel文件")
    public Result<ExcelUploadVO> uploadExcelFile(
            @RequestParam(value = "file", required = true) MultipartFile file,
            @RequestParam(value = "misId", required = true) String misId) {
        ExcelUploadVO result = autoAnalysisToolService.uploadFile(file, misId);
        if (StringUtils.isNotBlank(result.getErrorMessage())) {
            return Result.fail(500, result.getErrorMessage());
        }
        return Result.ok(result);
    }

    /**
     * 获取分析场景
     * @return 分析场景列表
     */
    @GetMapping("/getAnalysisScene")
    @ApiOperation(value = "获取分析场景", notes = "获取分析场景")
    public Result<List<AnalysisSceneVO>> getAnalysisScene() {
        return Result.ok(autoAnalysisToolService.getAnalysisScene());
    }

    /**
     * 开始分析任务
     * @return 分析结果
     */
    @PostMapping("/startAnalysis")
    @ApiOperation(value = "开始分析任务", notes = "开始分析任务")
    public Result<AnalysisResultResponse> startAnalysis(
                    @RequestParam(value = "file", required = true) MultipartFile file,
                    @RequestParam(value = "fileName", required = true) String fileName,
                    @RequestParam(value = "misId", required = true) String misId,
                    @RequestParam(value = "userName", required = true) String userName,
                    @RequestParam(value = "fileUrl", required = true) String fileUrl,
                    @RequestParam(value = "businessScene", required = true) String businessScene,
                    @RequestParam(value = "tagList", required = true) String tagList,
                    @RequestParam(value = "tagGroupName", required = true) String tagGroupName,
                    @RequestParam(value = "tagGroupId", required = true) Long tagGroupId,
                    @RequestParam(value = "sceneId", required = true) Long sceneId) {
        TaskCreateParam taskCreateParam = autoAnalysisToolInnerService.createTaskCreateParam(file, fileName, misId, userName, fileUrl, businessScene, tagList, tagGroupName, tagGroupId, sceneId);
        AnalysisResultResponse result = autoAnalysisToolService.startAnalysis(taskCreateParam);
        return Result.ok(result);
    }

    /**
     * 根据总任务id获取人工标注任务id
     * @param taskId 总任务id
     * @return 人工标注任务id
     */
    @GetMapping("/getManualTaskId")
    @ApiOperation(value = "根据总任务id获取人工标注任务id", notes = "根据总任务id获取人工标注任务id")
    public Result<Long> getManualTaskId(@RequestParam(value = "taskId", required = true) Long taskId) {
        return Result.ok(autoAnalysisToolService.getManualTaskId(taskId));
    }

    /**
     * 获取父任务对应的子任务id
     * @param taskId 父任务的ID
     * @return 子任务的ID
     */
    @GetMapping("/getSubTaskId")
    @ApiOperation(value = "获取对应的子任务ID", notes = "根据父任务ID获取对应的子任务ID")
   public Result<Long> getAnalysisResult(@RequestParam(value = "taskId") Long taskId) {
        Long subTaskId = autoAnalysisToolService.getSubTaskId(taskId);
        if (subTaskId == null) {
            return Result.fail(400, "未找到子任务");
        }
        return Result.ok(subTaskId);
    }

    /**
     * 根据子任务id获取分析结果
     * @param subTaskId 子任务id
     * @return 分析结果
     */
    @GetMapping("/getAnalysisResultBySubTaskId")
    @ApiOperation(value = "获取分析结果", notes = "根据子任务id获取分析结果")
    public Result<SessionAnalysisResultVO> getAnalysisResultBySubTaskId(@RequestParam(value = "subTaskId") Long subTaskId) {
        return Result.ok(autoAnalysisToolService.getAnalysisResult(subTaskId));
    }

    /**
     * 获取下载链接
     * @param taskId 文件标识
     * @return 下载链接
     */
    @GetMapping("/downloadUrl")
    @ApiOperation(value = "获取下载链接", notes = "根据文件标识获取下载链接")
    public Result<String> getDownloadUrl(@RequestParam(value = "taskId", required = true) Long taskId) {
        return Result.ok(autoAnalysisToolService.getDownloadUrl(taskId));
    }

    /**
     * 通过misno查询task信息
     * @return task列表
     * @param misNo mis号
     */
    @GetMapping("/taskId")
    @ApiOperation(value = "获取taskId", notes = "根据misno获取taskId")
    public Result<FileRecordVO> getTaskId(@RequestParam(value = "misNo", required = true) String misNo, @RequestParam(value = "pageNo",defaultValue = "1") Integer pageNo, @RequestParam(value = "pageSize",defaultValue = "10") Integer pageSize) {
        return Result.ok(autoAnalysisToolService.getFileRecordByMisNo(misNo,pageNo,pageSize));
    }

    /**
     * 根据TaskId和tags查询sessionId
     * @return sessionId列表
     * @param taskId 任务ID(文件)
     * @param problemTags 问题标签
     */
    @GetMapping("/sessionIdsByTaskIdAndTags")
    @ApiOperation(value = "获取sessionId", notes = "根据TaskId和tags获取sessionId")
    public Result<SessionUrlVO> getSessionIdsByTaskIdAndTags(
                                @RequestParam(value = "taskId", required = true) Long taskId,
                                @RequestParam(value = "problemTags", required = true) String problemTags,
                                @RequestParam(value = "pageNo",defaultValue = "0")Integer pageNo,
                                @RequestParam(value = "pageSize",defaultValue = "10")Integer pageSize
    ) {
        return Result.ok(autoAnalysisToolService.getSessionIdsByTaskIdAndTags(taskId, problemTags,pageNo,pageSize));
    }

    /**
     * 根据sceneID和sceneName获取标签组
     *
     * @param sceneId 场景ID
     * @param sceneName 场景名称
     * @return List<TagGroupVO> 标签组列表
     */
    @GetMapping("/getTagGroup")
    @ApiOperation(value = "获取标签组", notes = "根据sceneId和sceneName获取标签组")
    public Result<List<TagGroupVO>> getTagGroup(@RequestParam(value = "sceneId", required = true) Long sceneId,
                                                @RequestParam(value = "sceneName", required = true) String sceneName) {
        return Result.ok(autoAnalysisToolService.getTagGroup(sceneId,sceneName));
    }

    /**
     * 根据业务场景和标签组，获取标签列表
     * @param groupId 标签组ID
     * @return Tag列表
     */
    @GetMapping("/getTagList")
    @ApiOperation(value = "获取标签列表", notes = "根据业务场景和标签组，获取标签列表")
    public Result<List<TagVO>> getTagList(@RequestParam(value = "groupId", required = true) Long groupId) {
        return Result.ok(autoAnalysisToolService.getTagList(groupId));
    }

    /**
     * 根据父任务id查询子任务列表
     * @param taskId 父任务id
     * @return List<SubTaskVO> 子任务列表
     */
    @GetMapping("/getSubTaskList")
    @ApiOperation(value = "获取子任务列表", notes = "根据sessionId获取子任务列表")
    public Result<List<SubTaskVO>> getSubTask(@RequestParam(value = "taskId", required = true) Long taskId) {
        return Result.ok(autoAnalysisToolService.getSubTaskList(taskId));
    }

    /**
     * 获取人工标注任务结果链接
     * @param taskId 人工标注任务id
     * @return 人工标注任务结果链接
     */
    @GetMapping("/getMaunualTaskResultUrl")
    @ApiOperation(value = "获取人工标注任务结果链接", notes = "获取人工标注任务结果链接")
    public Result<String> getMaunualTaskResultUrl(@RequestParam(value = "taskId") Long taskId) {
        return Result.ok(autoAnalysisToolService.getMaunualTaskResultUrlByTaskId(taskId));
    }
}