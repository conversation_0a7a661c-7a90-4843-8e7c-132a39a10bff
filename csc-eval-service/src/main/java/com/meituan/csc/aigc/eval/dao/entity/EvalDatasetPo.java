package com.meituan.csc.aigc.eval.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 评测系统-数据集表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eval_dataset")
public class EvalDatasetPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据集名称
     */
    private String name;

    /**
     * 平台类型:0系统,1AI搭
     */
    private Integer platformType;

    /**
     * 平台空间:AI搭空间标识
     */
    private String platformWorkspace;

    /**
     * 平台应用唯一标识
     */
    private String platformApp;
    /**
     *平台应用版本id
     */
    private String appModelVersionId;
    /**
     * 业务场景
     */
    private String scene;

    /**
     * 能力类型,默认0,单轮1,多轮2. 多模态-图片分类101
     */
    private Integer ability;

    /**
     * 评测集描述
     */
    private String description;

    /**
     * 数据来源，0:模板上传，1:自定义上传
     */
    private Integer source;

    /**
     * 状态：0可用，1不可用
     */
    private Integer status;

    /**
     * 源文件路径
     */
    private String file;

    /**
     * 业务额外数据，如AI搭config_detail
     */
    private String extra;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 创建人
     */
    private String creatorMis;

    /**
     * 更新人
     */
    private String updaterMis;

    /**
     * 应用列表
     */
    private String applicationList;

    /**
     * 样本集创建方式 1-线上拉取 2-上传数据
     */
    private Integer createMethod;

    /**
     * 使用方 1-工单 2-智能task
     */
    private Integer userSource;

    /**
     * 数据拉取过程信息记录
     */
    private String process;

    /**
     * 数据集标识
     */
    private String referenceId;

    /**
     * 数据集类型,1-数据集 2-回归数据集 3-训练集 4-评测集
     */
    private Integer type;

    /**
     * 最新版本版本号
     */
    private Long versionId;
    /**
     *关联应用大模型节点id
     */
    private String nodeId;

}
