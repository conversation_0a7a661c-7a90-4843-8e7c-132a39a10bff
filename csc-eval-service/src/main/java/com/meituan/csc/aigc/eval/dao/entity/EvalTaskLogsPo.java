package com.meituan.csc.aigc.eval.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 评测系统-调用日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eval_task_logs")
public class EvalTaskLogsPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务唯一id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 数据集id
     */
    private Long datasetId;

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * query唯一id
     */
    private Long queryId;

    /**
     * 调用模型
     */
    private String model;

    /**
     * 调用输入
     */
    private String input;

    /**
     * 调用输出
     */
    private String output;

    /**
     * 毫秒数
     */
    private Integer cost;

    /**
     * 总消耗tokens
     */
    private Integer tokens;

    /**
     * 成功0，失败1
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 更新时间
     */
    private Date gmtModified;


}
