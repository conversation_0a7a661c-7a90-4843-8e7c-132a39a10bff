package com.meituan.csc.aigc.eval.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 评测任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eval_task")
public class EvalTaskPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务唯一id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 平台类型:0系统,1AI搭
     */
    private Integer platformType;

    /**
     * 平台空间:AI搭空间标识
     */
    private String platformWorkspace;

    /**
     * 平台应用唯一标识
     */
    private String platformApp;

    /**
     * 业务场景
     */
    private String scene;

    /**
     * 数据集id
     */
    private String datasetIds;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 评测类型 0-人工评测 1-自动评测 2-竞技评测 3-定期巡检 4-标注任务
     */
    private Integer type;

    /**
     * 模型输出 0-在线 1-离线
     */
    private Integer callType;

    /**
     * 被测模型
     */
    private String testModel;

    /**
     * 裁判模型
     */
    private String judgeModel;

    /**
     * 自动评测结果
     */
    private String autoResult;

    /**
     * 成本统计结果
     */
    private String costResult;

    /**
     * 性能统计结果
     */
    private String perfResult;

    /**
     * 评测能力
     */
    private String ability;

    /**
     * 评测的指标列表
     */
    private String metrics;

    /**
     * 评测维度 0-query 1-session
     */
    private Integer dimension;

    /**
     * 任务状态，0:创建中，1:排队中，2:评测中，3:已完成，4:失败
     */
    private Integer status;

    /**
     * 评测数据总量
     */
    private Integer totalCount;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 创建人
     */
    private String creatorMis;

    /**
     * 更新人
     */
    private String updaterMis;

    /**
     * 业务额外数据
     */
    private String extra;

    /**
     * 质检人
     */
    private String inspectors;

    /**
     * 被测应用列表
     */
    private String applicationConfig;

    /**
     * 打分方式 0-数据集 1-人工模拟
     */
    private Integer inputSource;

    /**
     * 模拟次数，人工模拟需要配置
     */
    private Long mockTimes;

    /**
     * 训练集版本id
     */
    private Long versionId;

    /**
     * 是否删除: 0-否, 1-是
     */
    private Boolean isDeleted;


}
