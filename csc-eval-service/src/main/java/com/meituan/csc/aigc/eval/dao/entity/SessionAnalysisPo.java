package com.meituan.csc.aigc.eval.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 会话分析结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("session_analysis")
public class SessionAnalysisPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id（关联sub_task_manage.id）
     */
    private Long taskId;

    /**
     * 数据行号
     */
    private Long dataIndex;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 业务场景
     */
    private String businessScene;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 问题标签
     */
    private String questionTags;

    /**
     * 分析详情
     */
    private String analysisDetails;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 任务类型 0-自动标注任务 1-人工标注任务
     */
    private Integer taskType;

    /**
     * 人工标注任务id（标注系统）
     */
    private Long manualLabelTaskId;

    /**
     * 会话时间
     */
    private Date sessionTime;

    /**
     * 标签组id
     */
    private Long tagGroupId;

    /**
     * 标签组名称
     */
    private String tagGroupName;

    /**
     * 备注
     */
    private String comment;


}
