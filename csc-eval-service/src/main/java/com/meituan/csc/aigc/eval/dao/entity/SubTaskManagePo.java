package com.meituan.csc.aigc.eval.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 子任务管理表（关联文件处理记录表）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sub_task_manage")
public class SubTaskManagePo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 总任务ID（关联file_records.id）
     */
    private Long taskId;

    /**
     * 子任务类型（0-自动标注任务 1-人工标注任务）
     */
    private Integer subTaskType;

    /**
     * 子任务状态：0-待处理，1-处理中，2-处理完成
     */
    private Integer subTaskStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 子任务处理后结果文件的S3存储路径
     */
    private String resultFileUrl;

    /**
     * 人工标注任务id（标注系统）
     */
    private Long manualLabelTaskId;

    /**
     * 错误信息（任务失败时记录）
     */
    private String errorInfo;
}
