package com.meituan.csc.aigc.eval.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 会话分析评价记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("workbench_message_review_record")
public class WorkbenchMessageReviewRecordPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 评价人mis
     */
    private String reviewMis;

    /**
     * 评价人名称
     */
    private String reviewName;

    /**
     * 评论内容
     */
    private String reviewContent;

    /**
     * 点赞数量
     */
    private Integer likeNumber;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除: 0-否, 1-是
     */
    private Boolean isDeleted;


}
