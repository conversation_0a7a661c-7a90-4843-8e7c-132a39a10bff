package com.meituan.csc.aigc.eval.dao.service.generator;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meituan.csc.aigc.eval.dao.entity.AutoInspectDetailPo;
import com.meituan.csc.aigc.eval.param.autoInspect.AutoInspectDetailConditionParam;

import java.util.List;

public interface AutoInspectDetailGeneratorService extends IService<AutoInspectDetailPo> {
    List<AutoInspectDetailPo> getByTaskIdList(List<Long> taskIds);

    List<AutoInspectDetailPo> getByCondition(AutoInspectDetailConditionParam autoInspectDetailParam);

    List<AutoInspectDetailPo> getEvalTask();
}
