package com.meituan.csc.aigc.eval.dao.service.generator;

import com.meituan.csc.aigc.eval.dao.entity.EvalTaskAssignPo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 任务分配表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-23
 */
public interface EvalTaskAssignGeneratorService extends IService<EvalTaskAssignPo> {

    /**
     * 根据任务id批量获取分配信息
     *
     * @param taskIdList 标注任务id
     * @return 分配信息列表
     */
    List<EvalTaskAssignPo> getByTaskIdList(List<Long> taskIdList);

    /**
     * 根据任务id获取分配信息
     *
     * @param taskId 标注任务id
     * @return 分配信息列表
     */
    List<EvalTaskAssignPo> getByTaskId(Long taskId);

    /**
     * 根据任务id获取分配信息
     *
     * @param taskId 标注任务id
     * @return 分配信息列表
     */
    int getCountByTaskId(Long taskId);

    /**
     * 根据任务id和mis获取分配信息
     *
     * @param taskId 标注任务id
     * @param mis    mis号
     * @return 分配信息列表
     */
    List<EvalTaskAssignPo> getByTaskAndMis(Long taskId, String mis);
    /**
     * 根据任务id删除分配信息
     *
     * @param taskId 标注任务id
     * @return 分配信息列表
     */
    void deleteByMarkTaskId(Long taskId);

}
