package com.meituan.csc.aigc.eval.dao.service.generator.impl;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meituan.csc.aigc.eval.dao.entity.ApplicationConfigPo;
import com.meituan.csc.aigc.eval.dao.mapper.ApplicationConfigMapper;
import com.meituan.csc.aigc.eval.dao.service.generator.ApplicationConfigGeneratorService;
import com.meituan.csc.aigc.eval.enums.ApplicationSourceEnum;
import com.meituan.csc.aigc.eval.param.application.ApplicationConditionParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 应用配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Service
public class ApplicationConfigGeneratorServiceImpl extends ServiceImpl<ApplicationConfigMapper, ApplicationConfigPo> implements ApplicationConfigGeneratorService {

    @Override
    public List<ApplicationConfigPo> getByCondition(ApplicationConditionParam condition) {
        condition.setNotSource(ApplicationSourceEnum.VIRTUAL.getCode());
        return getByCondition(condition, false);
    }

    @Override
    public List<ApplicationConfigPo> getByCondition(ApplicationConditionParam condition, boolean includeSystem) {
        LambdaQueryChainWrapper<ApplicationConfigPo> query = lambdaQuery();
        if (condition.getSource() != null) {
            query.eq(ApplicationConfigPo::getSource, condition.getSource());
        }
        if (condition.getNotSource() != null) {
            query.ne(ApplicationConfigPo::getSource, condition.getNotSource());
        }
        if (StringUtils.isNotBlank(condition.getName())) {
            query.eq(ApplicationConfigPo::getName, condition.getName());
        }
        if (StringUtils.isNotBlank(condition.getNameLike())) {
            query.like(ApplicationConfigPo::getName, "%" + condition.getNameLike() + "%");
        }
        if (condition.getStartTime() != null && condition.getEndTime() != null) {
            query.between(ApplicationConfigPo::getGmtCreated, condition.getStartTime(), condition.getEndTime());
        }
        if (StringUtils.isNotBlank(condition.getRobotId())) {
            query.eq(ApplicationConfigPo::getRobotId, condition.getRobotId());
        }
        if (StringUtils.isNotBlank(condition.getWorkspaceId())) {
            query.eq(ApplicationConfigPo::getPlatformWorkspace, condition.getWorkspaceId());
        }
        if (StringUtils.isNotBlank(condition.getAppId())) {
            query.eq(ApplicationConfigPo::getPlatformApp, condition.getAppId());
        }
        if (includeSystem && StringUtils.isNotBlank(condition.getCreateMis())) {
            query.and(child -> child.eq(ApplicationConfigPo::getCreatorMis, condition.getCreateMis()).or().eq(ApplicationConfigPo::getCreatorMis, "system"));
        } else if (StringUtils.isNotBlank(condition.getCreateMis())) {
            Optional.ofNullable(condition.getCreateMis()).filter(StringUtils::isNotBlank).ifPresent(creatorMis -> query.eq(ApplicationConfigPo::getCreatorMis, creatorMis));
        }
        return query.orderByDesc(ApplicationConfigPo::getGmtModified).list();
    }

    @Override
    public List<ApplicationConfigPo> getByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return lambdaQuery().in(ApplicationConfigPo::getId, idList).list();
    }

    @Override
    public ApplicationConfigPo getByRobotIdAndType(String modelConfigVersionId, Integer type) {
        return lambdaQuery()
                .eq(ApplicationConfigPo::getRobotId, modelConfigVersionId)
                .eq(ApplicationConfigPo::getType, type)
                .orderByDesc(ApplicationConfigPo::getGmtModified) // 按照修改时间降序排序
                .last("LIMIT 1") // 只取最新的一条数据
                .one(); // 执行查询并返回单个结果
    }
}
