package com.meituan.csc.aigc.eval.dao.service.generator.impl;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meituan.csc.aigc.eval.dao.entity.FileRecordsPo;
import com.meituan.csc.aigc.eval.dao.mapper.FileRecordsMapper;
import com.meituan.csc.aigc.eval.dao.service.generator.FileRecordsGeneratorService;
import com.meituan.csc.aigc.eval.enums.TaskProcessEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 文件处理记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-12
 */
@Service
@Slf4j
public class FileRecordsGeneratorServiceImpl extends ServiceImpl<FileRecordsMapper, FileRecordsPo> implements FileRecordsGeneratorService {

    @Override
    public Long insertSelective(FileRecordsPo fileRecordsPo) {
        // 使用mybatis-plus的save方法保存记录
        boolean saveResult = this.save(fileRecordsPo);
        if (!saveResult) {
            return null;
        }
        // 返回插入后的主键ID
        return fileRecordsPo.getId();
    }

    @Override
    public Long getIdByFileKey(Long taskId) {
        // 使用mybatis-plus查询key对应的taskId
        FileRecordsPo fileRecordsPo = this.lambdaQuery().eq(FileRecordsPo::getId, taskId).one();
        if (fileRecordsPo == null) {
            return null;
        }
        return fileRecordsPo.getId();
    }

    @Override
    public String getFileUrlByFileKey(Long taskId) {
        if (Objects.isNull(taskId)) {
            return null;
        }
        FileRecordsPo one = this.lambdaQuery().eq(FileRecordsPo::getId, taskId).one();
        if (Objects.isNull(one)) {
            return null;
        }
        return one.getResultFileUrl();
    }

    @Override
    public List<FileRecordsPo> getListByMis(String misNo) {
        if (StringUtils.isBlank(misNo)) {
            return null;
        }
        List<FileRecordsPo> fileRecordsPo = this.lambdaQuery().eq(FileRecordsPo::getMisId, misNo).list();
        if (CollectionUtils.isEmpty(fileRecordsPo)) {
            return null;
        }
        return fileRecordsPo;
    }

    @Override
    public List<FileRecordsPo> getListByMisAndPage(String misNo, Integer pageNo, Integer pageSize) {
        if (StringUtils.isBlank(misNo)) {
            return null;
        }
        Page<FileRecordsPo> page = new Page<>(pageNo, pageSize);
        IPage<FileRecordsPo> fileRecordsPoPage = this.page(page, new LambdaQueryWrapper<FileRecordsPo>().eq(FileRecordsPo::getMisId, misNo));
        if (Objects.isNull(fileRecordsPoPage)) {
            return null;
        }
        return fileRecordsPoPage.getRecords();
    }

    @Override
    public Integer countByStatus(Integer status) {
        return lambdaQuery().eq(FileRecordsPo::getTaskStatus, status).count();
    }

    @Override
    public void updateStatus(Long taskId, Integer status) {
        lambdaUpdate().set(FileRecordsPo::getTaskStatus, status).eq(FileRecordsPo::getId, taskId).update();
    }

    @Override
    public Integer getCountByMis(String misNo) {
        if (StringUtils.isBlank(misNo)) {
            return null;
        }
        Integer count = this.lambdaQuery().eq(FileRecordsPo::getMisId, misNo).count();
        if (Objects.isNull(count)) {
            return null;
        }
        return count;
    }

    @Override
    public List<FileRecordsPo> getListByMisAndPageSortedByCreatedAt(String misNo, Integer pageNo, Integer pageSize) {
        // 校验参数
        if(StringUtils.isBlank(misNo)) {
            return null;
        }

        if (Objects.isNull(pageNo) || Objects.isNull(pageSize)) {
            return null;
        }

        // 根据创建时间排序,分页
        Page<FileRecordsPo> page = new Page<>(pageNo, pageSize);
        IPage<FileRecordsPo> fileRecordsPos = this.page(page, new LambdaQueryWrapper<FileRecordsPo>().eq(FileRecordsPo::getMisId, misNo).orderByDesc(FileRecordsPo::getCreateTime));
        // 校验
        if (Objects.isNull(fileRecordsPos)) {
            return null;
        }

        if (CollectionUtils.isEmpty(fileRecordsPos.getRecords())) {
            return null;
        }

        return fileRecordsPos.getRecords();
    }

    @Override
    public boolean updateByIdAndStatus(FileRecordsPo updatedRecord) {
        if (Objects.isNull(updatedRecord)) {
            return false;
        }
        // 根据id和status来update
        LambdaUpdateWrapper<FileRecordsPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FileRecordsPo::getId, updatedRecord.getId());
        updateWrapper.set(FileRecordsPo::getTaskStatus, updatedRecord.getTaskStatus());
        updateWrapper.set(FileRecordsPo::getErrorInfo, updatedRecord.getErrorInfo());

        // 更新
        return this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean countByStatusAndCompare(Integer maxTaskSize, Long taskId) {
        // 1. 参数校验
        if (Objects.isNull(maxTaskSize) || Objects.isNull(taskId)) {
            log.warn("countByStatusAndCompare 参数为空");
            return false;
        }

        try {
            // 2. 使用悲观锁查询，确保原子性
            Integer count = this.lambdaQuery()
                    .eq(FileRecordsPo::getTaskStatus, TaskProcessEnum.PROCESSING.getCode())
                    .last("FOR UPDATE")
                    .count();

            // 3. 查询结果校验
            if (Objects.isNull(count)) {
                log.warn("查询结果为空, taskId={}", taskId);
                return false;
            }

            // 4. 如果查询的数量小于compare返回true，并更新任务状态，否则返回false
            if (count < maxTaskSize) {
                // 幂等处理，如果更新成功则true
                Boolean updateResult = updateByIdAndStatusWithLock(taskId, TaskProcessEnum.PROCESSING.getCode(), TaskProcessEnum.PENDING.getCode());
                if (Boolean.TRUE.equals(updateResult)) {
                    return true;
                } else {
                    log.warn("重复提交任务，更新任务状态失败, taskId={}", taskId);
                    return false;
                }
            } else {
                log.warn("任务状态更新失败，超过并行处理上限，taskId:{}", taskId);
                return false;
            }

        } catch (Exception e) {
            log.error("查询任务状态数量失败, maxTaskSize={}, taskId={}", maxTaskSize, taskId, e);
            return false;
        }
    }

    @Override
    public Boolean updateByIdAndStatusWithLock(Long id, Integer newStatus, Integer oldStatus) {
        if (Objects.isNull(id) || Objects.isNull(newStatus) || Objects.isNull(oldStatus)) {
            return false;
        }

        // 根据id和status来update
        LambdaUpdateWrapper<FileRecordsPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FileRecordsPo::getId, id);
        updateWrapper.eq(FileRecordsPo::getTaskStatus, oldStatus);
        updateWrapper.set(FileRecordsPo::getTaskStatus, newStatus);

        // 更新
        return this.update(updateWrapper);
    }

}
