package com.meituan.csc.aigc.eval.dao.service.generator.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.meituan.csc.aigc.eval.dao.entity.WorkbenchMessageReviewRecordPo;
import com.meituan.csc.aigc.eval.dao.mapper.WorkbenchMessageReviewRecordMapper;
import com.meituan.csc.aigc.eval.dao.service.generator.WorkbenchMessageReviewRecordGeneratorService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 会话分析评价记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
public class WorkbenchMessageReviewRecordGeneratorServiceImpl extends ServiceImpl<WorkbenchMessageReviewRecordMapper, WorkbenchMessageReviewRecordPo> implements WorkbenchMessageReviewRecordGeneratorService {

    @Override
    public List<WorkbenchMessageReviewRecordPo> listBySessionId(String sessionId) {

        if (StringUtils.isBlank(sessionId)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(WorkbenchMessageReviewRecordPo::getSessionId, sessionId)
                .eq(WorkbenchMessageReviewRecordPo::getIsDeleted, false)
                .orderByDesc(WorkbenchMessageReviewRecordPo::getAddTime)
                .list();
    }
}
