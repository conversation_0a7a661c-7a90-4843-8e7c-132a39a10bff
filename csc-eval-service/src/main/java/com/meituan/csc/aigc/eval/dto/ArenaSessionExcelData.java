package com.meituan.csc.aigc.eval.dto;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ArenaSessionExcelData implements Serializable {


    private static final long serialVersionUID = -52334497160588437L;
    /**
     * 会话ID
     */
    @ExcelProperty(value = "会话ID", index = 0)
    private String sessionId;

    /**
     * 对话历史
     */
    @ExcelProperty(value = "对话历史", index = 1)
    private String dialogHistory;

    /**
     * 模型名称
     */
    @ExcelProperty(value = "模型名称", index = 2)
    private String modelName;

    /**
     * 投票状态
     */
    @ExcelProperty(value = "投票状态", index = 3)
    private String voteStatus;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人", index = 4)
    private String creator;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", index = 5)
    private Date createTime;
}
