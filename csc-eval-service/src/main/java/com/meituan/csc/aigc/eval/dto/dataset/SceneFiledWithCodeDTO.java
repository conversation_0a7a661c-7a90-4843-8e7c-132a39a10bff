package com.meituan.csc.aigc.eval.dto.dataset;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SceneFiledWithCodeDTO extends SceneFiledDTO {
    private String code;
    /**
     * 查询前缀，表示哪张表的字段
     */
    private String prefix;
    /**
     * 拼接语句模版，有些复杂语句需要自定义拼接模板
     */
    private String template;
    /**
     * 字段来源 1-hive 2-AC接口
     */
    private Integer fieldSource;
}
