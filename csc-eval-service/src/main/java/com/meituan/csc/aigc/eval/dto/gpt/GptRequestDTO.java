package com.meituan.csc.aigc.eval.dto.gpt;

import com.meituan.csc.aigc.eval.param.gpt.ChatGptHttpRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class GptRequestDTO implements Serializable {

    private String conversionId;

    /**
     * 模型配置版本ID
     */
    private String modelConfigVersionId;

    /**
     * 请求方
     */
    private String user;

    /**
     * API请求秘钥
     */
    private String apiSecretKey;

    private String inputContent;
    private Map<String, String> dataParams;
    private String model;
    private String aidaAppId;

    private Boolean isInner;
    private List<ChatGptHttpRequest.GptMessage> messageList;
    /**
     * 大模型节点id
     */
    private String nodeId;

    private String nodeAppModelVersionId;

    /**
     * 节点id的调用密钥
     */
    private String nodeApiToken;
    /**
     * 业务参数
     */
    private String businessParam;
}
