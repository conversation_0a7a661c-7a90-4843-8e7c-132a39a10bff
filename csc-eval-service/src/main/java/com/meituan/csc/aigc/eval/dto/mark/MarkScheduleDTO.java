package com.meituan.csc.aigc.eval.dto.mark;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MarkScheduleDTO implements Serializable {
    /**
     * 任务已标注数量
     */
    private Integer markedNum;
    /**
     * 任务标注总数
     */
    private Integer markTotal;
    /**
     * 当前用户已标注数量
     */
    private Integer currentMisMarked;
    /**
     * 当前用户标注总数
     */
    private Integer currentMisMarkTotal;
    /**
     * 任务下每个标注人的标注信息
     */
    private List<MisSchedule> misScheduleList;

    @Data
    public static class MisSchedule implements Serializable{
        /**
         * 标注人mis
         */
        private String markMis;
        /**
         * 已标注数量
         */
        private Integer markedNum;
        /**
         * 总数
         */
        private Integer markTotal;
    }
}
