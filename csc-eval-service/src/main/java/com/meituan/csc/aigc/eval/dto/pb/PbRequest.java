package com.meituan.csc.aigc.eval.dto.pb;

import com.meituan.csc.aigc.eval.param.gpt.ChatGptHttpRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class PbRequest implements Serializable {
    private Map<String, String> extraSlotMap;
    private String conversationId;
    private String sceneId;
    private List<ChatGptHttpRequest.GptMessage> messageList;
    private String applicationExtra;
}
