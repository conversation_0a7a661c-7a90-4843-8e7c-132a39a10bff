package com.meituan.csc.aigc.eval.dto.task;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class TaskDetailDTO implements Serializable {

    private Long id;

    private String sessionId;

    private String conversationId;

    private String input;


    private Map<String, String> params;

    private String content;

    private String expectOutput;

    private Long datasetId;

    private Integer status;
    private Integer type;


    private List<EvalModelDTO> result;

    private String datasetName;

    private String createTime;

    private String updateTime;

    @Data
    public static class EvalModelDTO implements Serializable {
        /**
         * 模型名称
         */
        private String modelName;

        /**
         * 应用版本
         */
        private String version;

        /**
         * 结果数据
         */
        private List<EvalOutputDTO> data;
    }

    @Data
    public static class EvalOutputDTO implements Serializable {
        /**
         * 模型完整输出
         */
        private String output;

        /**
         * 查询query明细id
         */
        private List<Long> queryDetailIdList;

        private List<EvalInfo> eval;
    }

    @Data
    public static class EvalInfo implements Serializable {
        private Long queryDetailId;

        private String outputKey;

        private Integer metricId;

        private String metricName;

        private String result;

        /**
         * 结果备注
         */
        private String resultNote;

        private String inspector;

        private Integer inspectResult;

        private String correctAnswer;

        private String reason;

        private Boolean canInspect;

        private String output;

        private String expectOutput;

        private String markMis;
        private String markTime;
        private String head;
    }
}