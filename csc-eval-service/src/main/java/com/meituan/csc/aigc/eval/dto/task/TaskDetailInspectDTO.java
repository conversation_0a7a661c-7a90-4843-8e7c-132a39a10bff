package com.meituan.csc.aigc.eval.dto.task;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 任务质检信息DTO
 */
@Data
@Builder
public class TaskDetailInspectDTO implements Serializable {


    private static final long serialVersionUID = 1728082424502838844L;
    /**
     * 质检状态
     */
    private Integer status;

    /**
     * 质检结果
     */
    private String result;

    /**
     * 质检原因
     */
    private String reason;

    /**
     * 质检人mis
     */
    private String mis;

    /**
     * 质检时间
     */
    private String time;
}
