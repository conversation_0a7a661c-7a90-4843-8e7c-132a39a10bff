package com.meituan.csc.aigc.eval.dto.task;

import com.meituan.csc.aigc.eval.dto.dataset.TemplateFieldBindDTO;
import com.meituan.csc.aigc.eval.param.application.ApplicationResultParam;
import com.meituan.csc.aigc.eval.param.task.AidaModelConfig;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class TaskInfoDTO implements Serializable {
    /**
     * 任务名称
     */
    private String name;
    /**
     * 模型输出获取方式(评测方式) 0-在线 1-离线
     */
    private Integer callType;
    /**
     * 输入来源 0-样本集 1-人工模拟 2-机器模拟
     */
    private Integer inputSource;
    /**
     * 模拟次数
     */
    private Integer mockTimes;
    /**
     * 数据集名称列表
     */
    private List<String> datasetNameList;
    /**
     * 指标列表
     */
    private List<MetricInfo> metricList;
    /**
     * 质检人列表
     */
    private String inspectors;
    /**
     * 待测模型来源(待测应用来源) 0-评测系统 1-ai搭
     */
    private Integer applicationSource;
    /**
     * ai搭配置，模型来源为ai搭时填写
     */
    private List<AidaModelConfig> aidaModelConfigList;
    /**
     * 对话历史来源 1-预期结果 2-模型输出
     */
    private Integer historySource;
    /**
     * 历史上下文拼接结构化参数
     */
    private List<ApplicationResultParam> modelResultParamList;
    /**
     * 预期结果拼接结构化参数
     */
    private List<ApplicationResultParam> resultParamList;
    /**
     * 应用配置，模型来源为系统时填写
     */
    private List<Long> applicationConfig;
    /**
     * 绑定字段
     */
    private Map<Long, List<TemplateFieldBindDTO>> bindFields;
    /**
     * 机器人模拟名称
     */
    private String robotMockName;
    /**
     * 机器人模拟绑定字段
     */
    private Map<Long, List<TemplateFieldBindDTO>> robotMockBindFields;


    @Data
    public static class MetricInfo implements Serializable {
        /**
         * 指标名称
         */
        private String name;
        /**
         * 指标类型 1-打分类 2-枚举类
         */
        private Integer type;
        /**
         * 指标阈值,对于打分型指标，value是阈值，double类型，对于文本型指标，value是合格枚举列表，字符串列表
         */
        private Object scoreThreshold;
    }
}
