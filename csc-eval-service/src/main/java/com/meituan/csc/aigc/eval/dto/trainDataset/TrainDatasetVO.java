package com.meituan.csc.aigc.eval.dto.trainDataset;

import lombok.Data;
import java.io.Serializable;

@Data
public class TrainDatasetVO implements Serializable {

    /**
     * 训练集id
     */
    private Long trainDatasetId;

    /**
     * 训练集名称
     */
    private String name;

    /**
     * 样本集创建方式 1-线上拉取 2-上传数据 目前仅上传数据
     */
    private Integer createMethod;

    /**
     * 最新版本id
     */
    private Long versionId;

    /**
     * 最新版本版本号
     */
    private Integer versionNumber;

    /**
     * 评测集下数据的数量
     */
    private Long totalNumber;

    /**
     * 状态 0-已完成 2-失败
     */
    private Integer status;

    /**
     * 失败原因 status为2时有值
     */
    private String failMessage;

    /**
     * 更新人
     */
    private String updateMis;

    /**
     * 更新时间
     */
    private String updateTime;
}