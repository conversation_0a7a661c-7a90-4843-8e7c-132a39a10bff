package com.meituan.csc.aigc.eval.dto.workbench;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InspectWorkbenchDTO implements Serializable {
    /**
     * 大模型会话ID-conversationId集合
     */
    private List<String> id;

    /**
     * 访问ID列表，英文逗号分隔
     */
    private String visitIds;
    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 空间ID
     */
    private String workspaceId;

    /**
     * 空间名称
     */
    private String workspaceName;

    /**
     * 应用ID
     */
    private String applicationId;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * 机器人版本id
     */
    private String versionId;

    /**
     * 机器人版本名称
     */
    private String versionName;

    /**
     * 提问时间
     */
    private Date time;

    /**
     * 大模型轮次
     */
    private Integer turn;

    /**
     * 消息数量
     */
    private Integer messageCount;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 当前用户赞踩信息
     */
    private Status status;

    /**
     * 所有用户赞踩信息
     */
    private Status allStatus;

    /**
     * 平台类型
     */
    private Integer platformType;
    /**
     * 渠道标识
     */
    private String channel;

    /**
     * 满意度 0-5星
     */
    private String stars;

    /**
     * 问题是否已解决
     */
    private String sessionSolved;

    /**
     * 转人工信息
     */
    private String transferStaff;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 标准问
     */
    private String standardQuestion;

    /**
     * aida空间和应用信息
     */
    private List<WorkspaceAppDTO> workspaceAppInfo;

    /**
     * 参考调试窗口宽度设置
     */
    private ReferenceDebuggingWindowWidthSetUpDTO windowWidthSetUp;

    /**
     * 服务过程
     */
    private String serviceProcessInfo;

    @Data
    public static class Status implements Serializable {

        /**
         * 是否有点踩
         */
        private Boolean isMark;

        /**
         * 点赞数量
         */
        private Integer agreeNum;

        /**
         * 踩数量
         */
        private Integer disAgreeNum;

        /**
         * 质检人
         */
        private String inspector;

        /**
         * 质检状态
         */
        private Integer inspectionStatus;

        public static Status createStatus(Integer agreeNum, Integer disAgreeNum, Integer inspectionStatus, String inspector) {
            Status status = new Status();
            status.agreeNum = agreeNum;
            status.disAgreeNum = disAgreeNum;
            status.isMark = agreeNum + disAgreeNum > 0;
            status.inspector = inspector;
            status.inspectionStatus = inspectionStatus;
            return status;
        }
    }
}
