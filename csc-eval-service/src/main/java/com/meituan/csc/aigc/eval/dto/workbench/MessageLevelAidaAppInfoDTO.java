package com.meituan.csc.aigc.eval.dto.workbench;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description: 消息维护aida应用信息结构
 * Date: 2025/3/17 11:26
 * Author: libin111
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageLevelAidaAppInfoDTO implements Serializable{

    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 工作空间ID
     */
    private String workspaceId;

    /**
     * 工作空间名称
     */
    private String workspaceName;

    /**
     * 应用ID
     */
    private String applicationId;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * 应用类型
     */
    private Integer applicationType;

    /**
     * 应用版本
     */
    private String applicationVersion;

    /**
     * 应用版本名称
     */
    private String applicationVersionName;

    /**
     * aida消息id
     */
    private String llmMessageId;

    /**
     * aida会话id
     */
    private String llmSessionId;
}
