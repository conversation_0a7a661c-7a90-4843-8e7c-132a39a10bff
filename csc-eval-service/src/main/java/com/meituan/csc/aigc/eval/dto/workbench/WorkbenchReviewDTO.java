package com.meituan.csc.aigc.eval.dto.workbench;

import lombok.Data;

import java.util.Date;

/**
 * 会话分析评价类
 *
 * <AUTHOR>
 * @date 2025/5/27
 */
@Data
public class WorkbenchReviewDTO {

    /**
     * 评价ID
     */
    private Long id;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 评价内容
     */
    private String reviewContent;

    /**
     * 评价人MIS
     */
    private String reviewMis;

    /**
     * 评价人名称
     */
    private String reviewName;

    /**
     * 评价时间
     */
    private Date reviewDate;

    /**
     * 点赞数量
     */
    private Integer likeNumber;
}
