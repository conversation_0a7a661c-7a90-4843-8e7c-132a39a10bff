package com.meituan.csc.aigc.eval.dto.workbench.process;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.csc.aigc.eval.dto.workbench.process.enums.ServiceProcessType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ServiceProcessItem
 */
@Data
public class ServiceProcessItem<T extends ServiceProcessDetail> implements Serializable {
    @JsonProperty("uuid")
    private String uuid;

    @JsonProperty("serviceProcessType")
    private String serviceProcessType;

    @JsonProperty("serviceProcessName")
    private String serviceProcessName;

    @JsonProperty("triggerTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date triggerTime;

    @JsonProperty("endTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @JsonProperty("businessInfo")
    private PageLinkInfoVO businessInfo;

    @JsonProperty("displayLevel")
    private String displayLevel = "HIGH";

    @JsonProperty("detail")
    private T detail;

    @JsonProperty("messageIdList")
    private List<String> messageIdList;

    public ServiceProcessItem() {
    }

    public ServiceProcessItem(ServiceProcessType type) {
        this.serviceProcessType = type.getCode();
        this.serviceProcessName = type.getName();
    }
}

