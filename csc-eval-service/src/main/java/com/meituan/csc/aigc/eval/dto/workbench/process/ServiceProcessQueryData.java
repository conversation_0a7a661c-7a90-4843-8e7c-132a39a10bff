package com.meituan.csc.aigc.eval.dto.workbench.process;

import com.dianping.csc.eagle.query.dto.Response;
import com.dianping.csc.eagle.query.dto.page.PageDTO;
import com.dianping.csc.eagle.query.dto.portal.*;
import com.dianping.csc.flow.config.result.AutoTaskLogVO;
import com.dianping.csc.flow.dto.TaskTraceLogDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 服务过程查询中间数据
 *
 * <AUTHOR>
 */
@Data
public class ServiceProcessQueryData implements Serializable {

    /**
     * 会话ID
     */
    private Long sessionId;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 会话搜索结果
     */
    private Response<SessionSearchResult> session;

    /**
     * 基础业务信息
     */
    private PageLinkInfoVO basicBusinessInfo;

    /**
     * 门户通知日志搜索结果
     */
    private Response<PortalNoticeLogSearchResult> portalNoticeLog;

    /**
     * 聊天队列日志搜索结果
     */
    private Response<ChatLogSearchResult> chatQueueLog;

    /**
     * 聊天规则曝光日志搜索结果
     */
    private Response<ChatRuleLogSearchResult> chatRuleExposureLog;

    /**
     * 规则详情命中搜索结果
     */
    private Response<RuleDetailHitSearchResult> ruleDetail;

    /**
     * 机器人调用日志搜索结果
     */
    private Response<RobotInvokeLogSearchResult> robotInvokeLog;

    /**
     * 自动任务日志列表
     */
    private List<AutoTaskLogVO> autoTaskList;

    /**
     * 轻交互快照数据
     */
    private Response<LightSnapshotLogDTO> lightSnapshotLog;

    /**
     * 智慧门户用户操作日志
     */
    private Response<OperationLogSearchResult> operationLog;

    /**
     * 推荐解决方案日志
     */
    private Response<List<RecommendSolutionLogDTO>> recommendSolutionLog;

    /**
     * 新路由结果信息
     */
    private Response<List<SkillRoutingLogDTO>> SkillRoutingLog;

    /**
     * 会话中涉及到的服务进度节点信息
     */
    private Response<List<ProgressNodeDTO>> progressNodeList;

    /**
     * 会话中涉及到的解决方案Task信息
     */
    private Response<List<CommonSolutionRelationInfoDTO>> commonSolutionTaskList;

    /**
     * 会话中涉及到的子task的信息
     */
    private Response<PageDTO<TaskLogSearchResult>> subTaskInfoList;

//    /**
//     * 从托管侧获取到的托管/虚拟客服基本信息
//     */
//    private ResponseDTO<List<NodeInfoForDialogFlowDTO>> hostAndVirtualBasicInfo;

    /**
     * 从logcenter查询到的托管/虚拟客服task信息
     */
    private List<TaskTraceLogDTO> hostAndVirtualTaskInfo;
}


