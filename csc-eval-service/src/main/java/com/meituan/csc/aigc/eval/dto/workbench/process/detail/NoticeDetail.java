package com.meituan.csc.aigc.eval.dto.workbench.process.detail;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.csc.aigc.eval.dto.workbench.process.RuleVO;
import com.meituan.csc.aigc.eval.dto.workbench.process.ServiceProcessDetail;
import lombok.Data;

import java.util.List;

/**
 * 公告详情
 *
 * <AUTHOR>
 */
@Data
public class NoticeDetail implements ServiceProcessDetail {
    @JsonProperty("noticeId")
    private Integer noticeId;

    @JsonProperty("recommendId")
    private Long recommendId;

    @JsonProperty("name")
    private String name;

    @JsonProperty("content")
    private String content;

    @JsonProperty("rules")
    private List<RuleVO> rules;

    @JsonProperty("applySceneId")
    private Integer applySceneId;

    @JsonProperty("applySceneName")
    private String applySceneName;
}

