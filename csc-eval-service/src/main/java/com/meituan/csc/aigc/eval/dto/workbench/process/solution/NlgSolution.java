package com.meituan.csc.aigc.eval.dto.workbench.process.solution;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.csc.aigc.eval.dto.workbench.process.ButtonClickDTO;
import com.meituan.csc.aigc.eval.dto.workbench.process.RobotSolution;
import com.meituan.csc.aigc.eval.dto.workbench.process.enums.RobotSolutionType;
import lombok.Data;

import java.util.List;

@Data
public class NlgSolution implements RobotSolution {

    @JsonProperty("typicalQuestionId")
    private Integer typicalQuestionId;

    @JsonProperty("typicalQuestionName")
    private String typicalQuestionName;

    @JsonProperty("buttonClickList")
    private List<ButtonClickDTO> buttonClickList;

    @JsonProperty("taskKey")
    private String taskKey;

    @JsonProperty("taskName")
    private String taskName;

    @JsonProperty("taskType")
    private Integer taskType = 0;

    @JsonProperty("taskVersion")
    private String taskVersion;

    @JsonProperty("taskInstanceId")
    private String taskInstanceId;

    @JsonProperty("taskTag")
    private String taskTag;

    @Override
    public String getSolutionType() {
        return RobotSolutionType.AiModelSolution.getCode();
    }

    @Override
    public String getSolutionName() {
        return RobotSolutionType.AiModelSolution.getName();
    }
}

