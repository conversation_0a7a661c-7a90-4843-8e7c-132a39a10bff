package com.meituan.csc.aigc.eval.enums;

import lombok.Getter;

public enum ArenaSessionStatusEnum {
    NOT_VOTED(0, "未投票"),
    VOTED(1, "已投票");

    @Getter
    private int code;
    @Getter
    private String info;

    ArenaSessionStatusEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    public static ArenaSessionStatusEnum parse(int code) {
        for (ArenaSessionStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return NOT_VOTED;
    }
}
