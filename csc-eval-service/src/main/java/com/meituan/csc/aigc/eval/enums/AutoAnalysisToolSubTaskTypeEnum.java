package com.meituan.csc.aigc.eval.enums;

import lombok.Getter;

/**
 * 自动分析工具子任务类型枚举
 * 
 * <AUTHOR>
 * @since 2025-05-08
 */
@Getter
public enum AutoAnalysisToolSubTaskTypeEnum {
    
    AUTO_LABEL(0, "自动标注任务"),
    MANUAL_LABEL(1, "人工标注任务"),
    ;

    private final Integer code;

    private final String name;

    AutoAnalysisToolSubTaskTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public static AutoAnalysisToolSubTaskTypeEnum getByCode(Integer code) {
        for (AutoAnalysisToolSubTaskTypeEnum value : AutoAnalysisToolSubTaskTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
