package com.meituan.csc.aigc.eval.enums;

import java.util.Arrays;
import java.util.List;

public enum AutoInspectConfigStatusEnum {
    /**
     * 自动巡检配置状态
     */
    AVAILABLE(1, "开启巡检"),
    UNAVAILABLE(0, "不可用"),
    UN_TURN(2, "未开启");

    private final int code;
    private final String description;

    AutoInspectConfigStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 所有状态列表
     */
    public static final List<AutoInspectConfigStatusEnum> ALL_STATUS = Arrays.asList(values());

    /**
     * 根据状态码获取枚举值
     */
    public static AutoInspectConfigStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AutoInspectConfigStatusEnum statusEnum : values()) {
            if (statusEnum.getCode() == code) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 将前端给的是否开启转换为枚举
     */
    public static AutoInspectConfigStatusEnum transStatus(Integer code) {
        if (code == null) {
            return null;
        }
        if (code == 0) {
            return UN_TURN;
        }
        return getByCode(code);
    }
}
