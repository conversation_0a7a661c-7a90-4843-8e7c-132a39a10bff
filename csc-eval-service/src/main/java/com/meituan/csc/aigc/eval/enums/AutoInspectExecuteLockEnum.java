package com.meituan.csc.aigc.eval.enums;

public enum AutoInspectExecuteLockEnum {
    AUTO_INSPECT_EXECUTE_LOCK_SYSTEM(0, "auto_inspect_execute_lock_EvalSystem"),
    LOCK_SYSTEM(2, "auto_inspect_execute_lock_EvalSystem"),
    AUTO_INSPECT_EXECUTE_LOCK_USER_SPACE(1, "auto_inspect_execute_lock_UserSpace");
    private final int code;
    private final String type;

    AutoInspectExecuteLockEnum(int code, String type) {
        this.code = code;
        this.type = type;
    }

    public int getCode() {
        return code;
    }

    public String getType() {
        return type;
    }

    public static AutoInspectExecuteLockEnum parse(int code) {
        for (AutoInspectExecuteLockEnum status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
}
