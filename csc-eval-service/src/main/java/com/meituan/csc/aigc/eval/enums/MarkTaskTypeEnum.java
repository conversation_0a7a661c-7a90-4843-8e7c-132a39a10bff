package com.meituan.csc.aigc.eval.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum MarkTaskTypeEnum {

    /**
     * 标注任务类型
     */
    MANUAL_MOCK(1, "人工模拟"),
    METRIC_MARK(2, "指标标注"),
    TRAIN_MARK(3,"训练集标注")
    ;

    private final int code;
    private final String info;

    MarkTaskTypeEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }
}
