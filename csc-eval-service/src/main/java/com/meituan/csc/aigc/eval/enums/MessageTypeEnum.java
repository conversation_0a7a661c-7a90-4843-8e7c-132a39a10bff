package com.meituan.csc.aigc.eval.enums;


import lombok.Getter;

import java.util.stream.Stream;

@Getter
public enum MessageTypeEnum {
    TEXT(1,"text","文本消息"),
    IMAGE(2,"image","图片消息"),
    LINK(3,"link","图文消息");

    private int code;
    private String type;
    private String desc;

    MessageTypeEnum(int code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }


    public static MessageTypeEnum findByCode(Integer code) {
        return Stream.of(values())
                .filter(e -> e.code == code)
                .findFirst().orElse(null);
    }
}
