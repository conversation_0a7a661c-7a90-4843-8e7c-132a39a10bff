package com.meituan.csc.aigc.eval.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 */

@Getter
public enum MetricClassifyTypeEnum {

    /**
     * 指标分类类型
     */
    COMMON(0, "通用指标"),
    CUSTOM(1, "自定义指标"),
    ARENA(2, "竞技指标"),
    SUB_METRIC(3, "级联指标"),
    PROPERTY(4, "属性指标"),
    ANNOTATION(5, "标注指标"),
    ;

    public static final List<Integer> SUB_METRIC_LIST = Lists.newArrayList(SUB_METRIC.getCode(), PROPERTY.getCode());

    private final int code;

    private final String description;

    MetricClassifyTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
}
