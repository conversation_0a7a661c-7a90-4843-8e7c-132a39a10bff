package com.meituan.csc.aigc.eval.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum MetricDimensionEnum {

    /**
     * 打分维度
     */
    QUERY(0, "query"),
    SESSION(1, "session"),
    ;

    private final int code;

    private final String description;

    MetricDimensionEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static MetricDimensionEnum parse(int code) {
        for (MetricDimensionEnum metricTypeEnum : MetricDimensionEnum.values()) {
            if (metricTypeEnum.getCode() == code) {
                return metricTypeEnum;
            }
        }
        return null;
    }
}
