package com.meituan.csc.aigc.eval.enums;

import lombok.Getter;

/**
 * 打标方式枚举
 * 0：人工打标
 * 1：自动打标
 */
public enum TagMethodEnum {
    MANUAL(0, "人工打标"),
    AUTOMATIC(1, "自动打标");

    @Getter
    private int code;
    @Getter
    private String info;

    TagMethodEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }
    public static TagMethodEnum getByCode(int code) {
        for (TagMethodEnum value : TagMethodEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
