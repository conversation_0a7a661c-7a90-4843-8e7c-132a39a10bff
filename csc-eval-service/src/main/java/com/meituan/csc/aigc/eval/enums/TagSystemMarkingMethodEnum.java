package com.meituan.csc.aigc.eval.enums;

import lombok.Getter;

/**
 * 标签系统-标签打标方式枚举
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Getter
public enum TagSystemMarkingMethodEnum {

    MODEL(1, "模型打标"),
    NATIVE_INTERFACE(2, "原生接口打标"),
    AIDA_APPLICATION(3, "aida应用打标"),
    MANUAL(4, "人工打标");

    private int code;
    private String name;

    TagSystemMarkingMethodEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TagSystemMarkingMethodEnum getByCode(int code) {
        for (TagSystemMarkingMethodEnum value : TagSystemMarkingMethodEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
