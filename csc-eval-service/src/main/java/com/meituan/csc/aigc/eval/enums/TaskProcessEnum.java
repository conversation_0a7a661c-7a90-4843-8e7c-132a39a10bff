package com.meituan.csc.aigc.eval.enums;

import lombok.Getter;

@Getter
public enum TaskProcessEnum {
    /**
     * 任务处理状态
     * 0 待处理
     * 1 处理中
     * 2 处理完成
     */
    PENDING(0, "待处理"),
    PROCESSING(1, "处理中"),
    COMPLETED(2, "处理完成"),
    ;

    private final Integer code;

    private final String name;

    TaskProcessEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TaskProcessEnum getByCode(Integer code) {
        for (TaskProcessEnum value : TaskProcessEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
