package com.meituan.csc.aigc.eval.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public enum TaskQueryStatusEnum {

    /**
     * 评测状态 0-评测中 1-评测完成 2-评测失败 3-竞技任务评测中(用于退出窗口后恢复)
     */
    EVALUATING(0, "评测中", "#0000AA"),
    COMPLETED(1, "评测完成", "#00AA00"),
    FAILED(2, "评测失败", "#AA0000"),
    ARENA_EVALUATING(3, "竞技任务评测中", "#0000FF"),
    INSPECTING(4, "待质检", "#0000AA"),
    INSPECT_PASS(5, "质检通过", "#00AA00"),
    INSPECT_FAILED(6, "质检驳回", "#AA0000"),
    PENDING(7, "待定", "#0000AA"),
    INTERRUPT(8, "中断", "#AA0000"),
    EXECUTING(9, "执行应用中", "#0000AA"),
    EXECUTE_FAILED(10, "执行应用失败", "#AA0000");

    /**
     * 自动评测的状态
     */
    public final static List<TaskQueryStatusEnum> AUTO_STATUS = Arrays.asList(EVALUATING, COMPLETED, FAILED, INSPECTING, INSPECT_PASS, INSPECT_FAILED, INTERRUPT, EXECUTING, EXECUTE_FAILED);

    /**
     * 竞技评测的状态
     */
    public final static List<TaskQueryStatusEnum> ARENA_STATUS = Arrays.asList(EVALUATING, COMPLETED, FAILED, ARENA_EVALUATING);

    /**
     * 评测完成的状态
     */
    public final static List<Integer> COMPLETE_STATUS = Arrays.asList(COMPLETED.code, FAILED.code, INSPECTING.code, INSPECT_PASS.code, INSPECT_FAILED.code, INTERRUPT.code, EXECUTE_FAILED.code);

    /**
     * 不需要质检的状态
     */
    public final static List<Integer> NOT_INSPECT_STATUS = Arrays.asList(FAILED.code, EXECUTE_FAILED.code, INTERRUPT.code, EVALUATING.code);

    /**
     * 机器重启后需要扫描的状态
     */
    public static final List<Integer> RESTART_SCAN_STATUS = Arrays.asList(
            EVALUATING.code,
            EXECUTING.code
    );

    private final int code;

    private final String name;

    private final String rgb;

    TaskQueryStatusEnum(int code, String name, String rgb) {
        this.code = code;
        this.name = name;
        this.rgb = rgb;
    }

    public static TaskQueryStatusEnum parse(int code) {
        for (TaskQueryStatusEnum taskQueryStatusEnum : TaskQueryStatusEnum.values()) {
            if (taskQueryStatusEnum.getCode() == code) {
                return taskQueryStatusEnum;
            }
        }
        return null;
    }
}
