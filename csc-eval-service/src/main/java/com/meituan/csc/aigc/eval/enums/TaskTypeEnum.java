package com.meituan.csc.aigc.eval.enums;

public enum TaskTypeEnum {
    MANUAL(0, "人工评测", "ManualEvaluation"),
    AUTO(1, "自动评测", "AutoEvaluation"),
    ARENA(2, "竞技评测", "ArenaEvaluation"),
    INSPECT(3,"定期巡检", "RegularInspection"),

    ANNOTATION(4,"标注任务", "AnnotationTask"),
    TRAIN_MANUAL_ANNOTATION(5,"训练集人工标注任务", "TrainingSetManualAnnotation");
    private int code;
    private String info;
    private String name;

    TaskTypeEnum(int code, String info, String name) {
        this.code = code;
        this.info = info;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public String getName() {
        return name;
    }

    public static TaskTypeEnum parse(int code) {
        for (TaskTypeEnum taskTypeEnum : TaskTypeEnum.values()) {
            if (taskTypeEnum.getCode() == code) {
                return taskTypeEnum;
            }
        }
        return null;
    }
}
