package com.meituan.csc.aigc.eval.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum YesNoEnum {
    /**
     * 是
     */
    YES(1, "是"),
    /**
     * 否
     */
    NO(0, "否");

    private final Integer code;
    private final String description;

    YesNoEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static YesNoEnum getByCode(Integer code) {
        for (YesNoEnum yesNoEnum : YesNoEnum.values()) {
            if (yesNoEnum.getCode().equals(code)) {
                return yesNoEnum;
            }
        }
        return null;
    }
}
