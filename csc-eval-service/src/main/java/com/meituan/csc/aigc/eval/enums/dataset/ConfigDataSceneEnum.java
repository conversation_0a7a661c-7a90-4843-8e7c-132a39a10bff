package com.meituan.csc.aigc.eval.enums.dataset;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ConfigDataSceneEnum {
    /**
     * 获取配置数据场景枚举
     */
    OFFLINE_CASE_CONDITION(1, "离线工单筛选条件"),
    ONLINE_CASE_CONDITION(2, "离线智能task筛选条件"),
    CHAT_RECORD_TIME_RANGE(3, "聊天记录时间范围"),
    CASE_MAJOR_FILED(4, "工单主表字段"),
    AI_TASK_FIELD(5, "智能Task字段"),
    THIRD_LINK_FIELD(6, "三方联络记录字段"),
    ;

    private Integer code;

    private String name;
}
