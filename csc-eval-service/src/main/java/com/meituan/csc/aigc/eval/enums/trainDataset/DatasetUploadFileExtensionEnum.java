package com.meituan.csc.aigc.eval.enums.trainDataset;

public enum DatasetUploadFileExtensionEnum {
    JSONL("jsonl"),
    XLSX("xlsx"),
    XLS("xls");

    private final String extension;

    DatasetUploadFileExtensionEnum(String extension) {
        this.extension = extension;
    }

    public String getExtension() {
        return extension;
    }

    public static boolean isAllowed(String extension) {
        for (DatasetUploadFileExtensionEnum fileExtension : values()) {
            if (fileExtension.getExtension().equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }
}