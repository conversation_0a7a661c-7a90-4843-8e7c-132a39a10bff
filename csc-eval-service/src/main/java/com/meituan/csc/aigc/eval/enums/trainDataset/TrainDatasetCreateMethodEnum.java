package com.meituan.csc.aigc.eval.enums.trainDataset;

import lombok.Getter;

/**
 * 训练集创建方式枚举
 */
@Getter
public enum TrainDatasetCreateMethodEnum {
    /**
     * 单轮会话
     */
    CREATE(1, "create", "", "新建训练集"),
    /**
     * 多轮会话
     */
    ITERATE(2, "iterate", "", "迭代训练集");

    private final int code;
    private final String name;
    private final String standard;
    private final String description;

    TrainDatasetCreateMethodEnum(int code, String name, String standard, String description) {
        this.code = code;
        this.name = name;
        this.standard = standard;
        this.description = description;
    }

    /**
     * 根据code解析枚举
     */
    public static TrainDatasetCreateMethodEnum parse(Integer code) {
        for (TrainDatasetCreateMethodEnum method : TrainDatasetCreateMethodEnum.values()) {
            if (method.getCode() == code) {
                return method;
            }
        }
        return null;
    }

    /**
     * 根据name解析枚举
     */
    public static TrainDatasetCreateMethodEnum parse(String name) {
        for (TrainDatasetCreateMethodEnum method : TrainDatasetCreateMethodEnum.values()) {
            if (method.getName().equals(name)) {
                return method;
            }
        }
        return null;
    }
}
