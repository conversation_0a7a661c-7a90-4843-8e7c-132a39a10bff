package com.meituan.csc.aigc.eval.enums.trainDataset;

public enum TrainDatasetErrorMessage {
    FILE_CANNOT_BE_NULL(1, "文件不能为空"),
    FILE_NAME_CANNOT_BE_NULL(2, "文件名不能为空"),
    CREATION_METHOD_CANNOT_BE_NULL(3, "创建方式不能为空"),
    INVALID_FILE_TYPE(4, "文件类型必须为 .jsonl, .xlsx 或 .xls"),
    ORIGINAL_VERSION_ID_CANNOT_BE_NULL(5, "原始版本id不能为空"),
    DATASET_NOT_EXIST(6, "训练集不存在"),
    NAME_CANNOT_BE_NULL(7, "名称不能为空"),
    VERSION_NOT_EXIST(8, "版本不存在"),
    EXIST_RUNNING_VERSION(9, "存在正在运行中的版本"),
    VERSION_DATA_IS_EMPTY(10, "版本数据为空"),
    ANNOTATION_TYPE_CANNOT_BE_NULL(11, "标注方式不能为空"),
    METRICS_CANNOT_BE_NULL(12, "指标不能为空"),
    TRAIN_DATASET_ID_CANNOT_BE_NULL(13, "训练数据集ID不能为空"),
    TRAIN_DATASET_VERSION_ID_CANNOT_BE_NULL(14, "训练数据集版本ID不能为空"),
    ANNOTATION_LIST_CANNOT_BE_NULL(15, "标注项不能为空"),
    UPLOAD_DATA_IS_EMPTY(16, "上传数据为空"),
    EVAL_TASK_ID_CANNOT_BE_NULL(17,"人工标注任务ID不能为空");
    private final int code;
    private final String description;

    TrainDatasetErrorMessage(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
