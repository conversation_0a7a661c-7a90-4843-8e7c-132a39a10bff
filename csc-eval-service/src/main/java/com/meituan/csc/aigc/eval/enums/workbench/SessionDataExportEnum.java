package com.meituan.csc.aigc.eval.enums.workbench;

import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 会话数据导出枚举
 *
 * <AUTHOR>
 */
@Getter
public enum SessionDataExportEnum {
    SESSION_ID("会话id"),
    WORKSPACE_NAME("空间名称"),
    APPLICATION_NAME("应用名称"),
    CREATE_TIME("创建时间"),
    USER_ID("用户ID"),
    ORDER_ID("订单ID"),
    TURNS("大模型对话轮次"),
    INPUT_CONTENT("输入内容"),
    OUTPUT_CONTENT("输出内容"),
    HISTORY("对话上文"),
    SIGNAL("信号"),
    CALLS("调用信息"),
    STATUS("状态"),
    LLM_MESSAGE_ID("大模型消息id"),
    ;

    private final String field;

    SessionDataExportEnum(String field) {
        this.field = field;
    }

    public static List<String> listFields() {
        return Arrays.stream(SessionDataExportEnum.values())
                .map(SessionDataExportEnum::getField)
                .collect(Collectors.toList());
    }
}
