package com.meituan.csc.aigc.eval.excel.listener;


import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public class SortedExcelCommonListener implements ReadListener<Map<Integer, String>> {

    /**
     * index映射的map
     */
    List<Map<Integer, String>> excelIndexDataList = new ArrayList<>();

    /**
     * 列名映射的列表
     */
    List<Map<String, String>> excelDataList = new ArrayList<>();
    /**
     * index和列名的映射关系
     */
    Map<Integer, String> headIndexMap = new HashMap<>();
    /**
     * 按照顺序排列的列名列表
     */
    List<String> headList = new ArrayList<>();

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        // 只保留有对应表头的数据
        Map<Integer, String> filteredData = new HashMap<>();
        data.forEach((index, value) -> {
            if (headIndexMap.containsKey(index)) {
                filteredData.put(index, value);
            }
        });
        excelIndexDataList.add(filteredData);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 按顺序填充excelDataList
        for (Map<Integer, String> map : excelIndexDataList) {
            Map<String, String> data = new LinkedHashMap<>();
            List<Map.Entry<Integer, String>> sortMapList = new ArrayList<>(map.entrySet());
            sortMapList.sort(Map.Entry.comparingByKey());
            for (Map.Entry<Integer, String> entry : sortMapList) {
                Integer key = entry.getKey();
                String value = entry.getValue();
                String head = headIndexMap.get(key);
                if (StringUtils.isBlank(head)) {
                    head = "未知标题" + key;
                    headIndexMap.put(key, head);
                }
                data.put(head, value);
            }
            excelDataList.add(data);
        }
        // 按顺序填充headList
        List<Map.Entry<Integer, String>> list = new ArrayList<>(headIndexMap.entrySet());
        list.sort(Map.Entry.comparingByKey());
        headList = list.stream().map(Map.Entry::getValue).collect(Collectors.toList());
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        headMap.forEach((index, cellData) -> {
            if(StringUtils.isNotBlank(cellData.getStringValue())) {
                headIndexMap.put(index, cellData.getStringValue());
            }
        });
    }
}
