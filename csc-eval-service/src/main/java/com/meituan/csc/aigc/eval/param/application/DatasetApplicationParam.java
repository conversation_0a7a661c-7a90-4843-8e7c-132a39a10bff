package com.meituan.csc.aigc.eval.param.application;

import com.meituan.csc.aigc.eval.param.task.TaskMetricParam;
import com.meituan.csc.aigc.eval.enums.TaskInputSourceEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DatasetApplicationParam extends ApplicationListInfoParam implements Serializable {
    private Integer uploadType;
    private String createMis;
    private Integer evalType;
    //指标id
    private List<TaskMetricParam> metricList;
    /**
     * 输入来源 {@link TaskInputSourceEnum}
     */
    private Integer inputSource;
}
