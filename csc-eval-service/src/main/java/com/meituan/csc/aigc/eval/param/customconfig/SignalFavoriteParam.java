package com.meituan.csc.aigc.eval.param.customconfig;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 工作台自定义配置-信号重排序。
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
public class SignalFavoriteParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * AI搭空间标识
     */
    private String platformWorkspace;

    /**
     * ai搭应用ID
     */
    private String appId;

    /**
     * ai搭机器人id
     */
    private String appVersionId;

    /**
     * 信号key
     */
    private String signalKey;

    /**
     * 是否收藏
     */
    private Boolean isFavorite;

}
