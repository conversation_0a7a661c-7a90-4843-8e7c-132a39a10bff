package com.meituan.csc.aigc.eval.param.dataset;

import com.meituan.csc.aigc.eval.dao.entity.ApplicationConfigPo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * <P>@date 2025/2/17 14:55</P>
 * <P>Description: 描述. </P>
 */

@Data
public class DatasetListParam {
    /**
     * @see com.meituan.csc.aigc.eval.enums.PlatformTypeEnum
     */
    private Integer source;

    private List<ApplicationConfig> applicationConfig;

    @Data
    public static class ApplicationConfig {
        private String applicationId;
        private String applicationVersionId;
    }
}