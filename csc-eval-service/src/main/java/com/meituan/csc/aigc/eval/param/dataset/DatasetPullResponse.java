package com.meituan.csc.aigc.eval.param.dataset;

import com.meituan.csc.aigc.eval.dao.entity.EvalDatasetDetailPo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class DatasetPullResponse implements Serializable {
    /**
     * 数据集拉取结果
     */
    private List<EvalDatasetDetailPo> dataList;
    /**
     * 数据集拉取状态
     */
    private DatasetPullStatus status;
    /**
     * 处理过程数据
     */
    private DatasetProcessParam processData;

    public DatasetPullResponse() {
        this.processData = new DatasetProcessParam();
    }

    @Getter
    @AllArgsConstructor
    public enum DatasetPullStatus {
        /**
         * 数据拉取状态
         */
        SUCCESS(0, "成功"),
        PRE_HANDLE_ERROR(100, "预处理失败"),
        HIVE_ERROR(200, "查询Hive失败"),
        AC_ERROR(300, "AC接口调用失败"),
        PROCESS_ERROR(400, "数据加工失败"),
        INNER_ERROR(500, "内部错误"),
        INTERRUPT(999, "中断"),
        ;

        private final int code;
        private final String description;

        public static String getStatusString(DatasetPullStatus status) {
            if (status == null || status == DatasetPullStatus.SUCCESS) {
                return "success";
            }
            return "failed";
        }
    }
}
