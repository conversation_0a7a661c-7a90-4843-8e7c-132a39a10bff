package com.meituan.csc.aigc.eval.param.gpt;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChatParamConfig implements Serializable {
    /**
     * system角色的提示词
     */
    private String systemInstruction;
    /**
     * 调用模型
     */
    private String model;
    /**
     * 这个值越高会使生成结果更随机
     * 不能与topP同时使用
     */
    private Double temperature;
    /**
     * 模型考虑具有top_p概率质量的标记的结果。0.1表示只考虑组成前10％概率质量的标记
     * 不能与temperature同时使用
     */
    private Double topP;
    /**
     * 最大模型生成的结果长度（以token计），不填写则为模型最大长度（4096个token）
     */
    private Integer maxTokens;
    /**
     * 正值惩罚新标记的出现次数，以此来增强模型谈论新话题的能力
     */
    private Double presencePenalty;
    /**
     * 正值根据文本中新标记的现有频率对其进行惩罚，从而降低模型重复相同行的几率
     */
    private Double frequencyPenalty;
}
