package com.meituan.csc.aigc.eval.param.inspect;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InspectInfo implements Serializable {

    private Integer inspectRatio;

    private Integer inspectCount;

    private List<InspectDetailDTO> inspectDetails;

    @Data
    public static class InspectDetailDTO implements Serializable {
        /**
         * 质检比例
         */
        private Integer ratio;
        /**
         * 质检数量
         */
        private Integer inspectCount;
        /**
         * 质检人
         */
        private String inspectors;
        /**
         * 评测目标
         */
        private String evalTarget;
        /**
         * 指标id
         */
        private String metricId;
        private String metricName;
        /**
         * 评测结果
         */
        private String evalResult;
    }

}
