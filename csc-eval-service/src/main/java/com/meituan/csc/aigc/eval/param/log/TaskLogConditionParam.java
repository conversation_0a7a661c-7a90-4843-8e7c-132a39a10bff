package com.meituan.csc.aigc.eval.param.log;

import com.meituan.csc.aigc.eval.param.dataset.CommonConditionParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskLogConditionParam extends CommonConditionParam implements Serializable {

    private Integer type;

    private Long queryDetailId;

    private Long queryId;

    private Long taskId;

    private String application;

    private Long metricId;
}
