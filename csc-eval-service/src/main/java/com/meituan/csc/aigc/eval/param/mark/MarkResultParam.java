package com.meituan.csc.aigc.eval.param.mark;

import com.meituan.csc.aigc.eval.param.metric.ChildMetricParam;
import lombok.Data;
import org.apache.commons.compress.utils.Lists;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MarkResultParam implements Serializable {
    /**
     * 唯一会话ID
     */
    private String conversationId;
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 应用
     */
    private String application;
    /**
     * 消息id
     */
    private String messageId;
    /**
     * 指标结果
     */
    private ScoreParam score;
    /**
     * 人工标注结果列表
     */
    private List<ChildMetricParam> metrics = Lists.newArrayList();
}
