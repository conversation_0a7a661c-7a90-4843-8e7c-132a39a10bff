package com.meituan.csc.aigc.eval.param.mark;

import com.meituan.csc.aigc.eval.dto.mark.TaskAssignDetail;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TaskTransferParam implements Serializable {

    /**
     * 标注任务id
     */
    private Long taskId;

    /**
     * 原标注人mis
     */
    private String originMarkMis;

    /**
     * 新标注分组
     */
    private List<TaskAssignDetail> targetMarkGroupList;
}
