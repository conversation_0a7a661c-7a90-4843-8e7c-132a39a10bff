package com.meituan.csc.aigc.eval.param.task;

import com.meituan.csc.aigc.eval.param.application.DatasetApplicationParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskDatasetApplicationParam extends DatasetApplicationParam implements Serializable {
    private List<Long> datasetIdList;
}
