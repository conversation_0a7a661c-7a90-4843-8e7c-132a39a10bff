package com.meituan.csc.aigc.eval.param.task;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TaskListParam implements Serializable {
    private Integer abilityType;
    private String modelType;
    private String creatorMis;
    private String startTime;
    private String endTime;
    private String name;
    private Integer status;
    private Integer pageSize;
    private Integer pageNum;
    private Long id;
    private Integer callType;
    private String inspector;
    private Integer modelSource;
    private String scene;
    private Long datasetId;
    private Integer taskType;
    private List<Integer> taskTypes;
}
