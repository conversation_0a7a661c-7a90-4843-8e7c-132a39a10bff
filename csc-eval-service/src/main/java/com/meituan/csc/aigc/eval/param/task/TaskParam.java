package com.meituan.csc.aigc.eval.param.task;

import com.meituan.csc.aigc.eval.dto.dataset.TemplateFieldBindDTO;
import com.meituan.csc.aigc.eval.param.application.ApplicationResultParam;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class TaskParam implements Serializable {
    /**
     * 任务名称
     */
    private String name;

    /**
     * prompt
     */
    private String prompt;

    /**
     * 能力类型 0-单轮问答 1-多轮问答
     */
    private int abilityType;

    /**
     * # 任务类型 0-人工评测 1-自动评测 2-竞技评测 3-自动巡检
     */
    private Integer taskType;

    /**
     * 模型类型，以逗号分隔
     */
    private List<String> modelTypeList;

    /**
     * 数据集id，以逗号分隔
     */
    private List<Long> datasetIdList;

    /**
     * 竞技任务指标, 单选 20-GSB（任务类型为0-竞技场任务时需要填写，其他不需要）
     */
    private Integer arenaMetric;

    /**
     * 大模型节点id
     */
    private String nodeId;

    private String nodeAppModelVersionId;

    /**
     * 节点id的调用密钥
     */
    private String nodeApiToken;
    /**
     * 指标列表
     */
    private List<TaskMetricParam> metricList;
    /**
     * 相似度阈值
     */
    private List<ScoreThresholdParam> scoreThresholdList;

    /**
     * 模型输出获取方式 0-离线 1-在线
     */
    private Integer callType;

    /**
     * # 质检人列表，英文逗号分隔
     */
    private List<String> inspectors;

    /**
     * 输入来源 0-样本集 1-人工模拟
     */
    private Integer inputSource;

    /**
     * 模拟次数，人工模拟时必填
     */
    private Integer mockTimes;

    /**
     * 待测模型来源 0-系统 1-ai搭
     */
    private Integer modelSource;
    /**
     * ai搭配置，模型来源为ai搭时填写
     */
    private List<AidaModelConfig> aidaModelConfig;

    /**
     * 应用配置，模型来源为系统时填写
     */
    private List<Long> applicationConfig;

    /**
     * 对话历史来源 1-预期结果 2-模型输出
     */
    private Integer historySource;

    /**
     * 话术字段
     */
    private List<ApplicationResultParam> modelResultParamList;

    /**
     * 字段映射关系
     */
    private Map<Long, List<TemplateFieldBindDTO>> bindFields;
    /**
     * 用户模拟器关联信息(robotMockBindFields)
     */
    private Map<Long, List<TemplateFieldBindDTO>> robotMockBindFields;
    /**
     * 用户模拟器id
     */
    private Long robotMockId;
    /**
     * 模型输出结果结构化解析
     */
    private List<ApplicationResultParam> resultParamList;

    /**
     * 是否评测任务
     */
    private Boolean whetherRegressDataset;


    private Map<String, String> fieldMap;
}
