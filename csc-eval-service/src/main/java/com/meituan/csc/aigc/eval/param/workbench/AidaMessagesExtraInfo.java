package com.meituan.csc.aigc.eval.param.workbench;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Description: aida extraInfo信息查询结构
 * Date: 2025/4/21 20:50
 * Author: libin111
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AidaMessagesExtraInfo implements Serializable {

    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private String id;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 业务传递的信息
     */
    private String extraInfo;
}
