package com.meituan.csc.aigc.eval.param.workbench;

import com.meituan.csc.aigc.eval.param.PageParam;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InspectWorkbenchConditionParam extends PageParam implements Serializable {
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 空间ID
     */
    private String workspaceId;
    /**
     * 应用ID
     */
    private String applicationId;

    /**
     * 应用版本id
     */
    private String applicationVersionId;
    /**
     * 质检状态
     */
    private Integer inspectionStatus;
    /**
     * 应用ID列表, 批量查询
     */
    private List<String> applicationIdList;

    /**
     * 会话是否解决
     */
    private String sessionSolved;

    /**
     * 访问ID
     */
    private String visitId;

    /**
     * 问题名称
     */
    private String questionName;

    /**
     * 是否超时结束
     */
    private Boolean isTimeoutEnd;

    /**
     * 是否异常结束
     */
    private Boolean isExceptionEnd;

    /**
     * 渠道 ChanelEnum
     */
    private String channel;

    /**
     * 场景
     */
    private String scene;

    /**
     * 业务线
     */
    private String bu;

    /**
     * 子业务线
     */
    private String subBu;

    /**
     * 星级
     */
    private List<String> stars;

    /**
     * 转人工客服
     */
    private List<String> transferStaff;

    /**
     * 任务key
     */
    private String taskKey;

    /**
     * 任务版本
     */
    private String taskVersion;
    /**
     * 任务节点
     */
    private String taskNode;

    /**
     * 用户输入检索
     */
    private String userInput;

    /**
     * 回复内容检索
     */
    private String finalOutput;
}
