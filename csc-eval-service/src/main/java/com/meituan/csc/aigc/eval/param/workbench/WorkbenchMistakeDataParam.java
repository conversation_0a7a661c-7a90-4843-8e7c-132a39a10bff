package com.meituan.csc.aigc.eval.param.workbench;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class WorkbenchMistakeDataParam implements Serializable {
    /**
     * 用户id
     */
    private String userId;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 消息id
     */
    private String messageId;
    /**
     * 历史信息
     */
    private String history;
    /**
     * 模型输入
     */
    private String input;
    /**
     * 信号
     */
    private String signal;
    /**
     * 模型输出
     */
    private String output;
    /**
     * 点赞数
     */
    private Integer agreeCount;
    /**
     * 点踩数
     */
    private Integer disagreeCount;
}
