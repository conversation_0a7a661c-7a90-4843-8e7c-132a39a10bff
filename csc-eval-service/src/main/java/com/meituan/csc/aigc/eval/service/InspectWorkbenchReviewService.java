package com.meituan.csc.aigc.eval.service;

import com.meituan.csc.aigc.eval.dto.workbench.WorkbenchReviewDTO;
import com.meituan.csc.aigc.eval.param.workbench.WorkbenchReviewParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
public interface InspectWorkbenchReviewService {

    /**
     * 添加评价
     *
     * @param param 评价参数
     */
    Long addReview(WorkbenchReviewParam param);

    /**
     * 查看评价列表
     *
     * @param sessionId 会话ID
     * @return 评价列表
     */
    List<WorkbenchReviewDTO> listReviews(String sessionId);
}
