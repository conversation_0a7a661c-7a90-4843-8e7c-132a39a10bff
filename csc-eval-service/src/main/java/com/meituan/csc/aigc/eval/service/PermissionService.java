package com.meituan.csc.aigc.eval.service;

import com.meituan.csc.aigc.eval.exception.CheckException;
import com.meituan.csc.aigc.eval.exception.EvalException;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PermissionService {

    /**
     * 检查权限
     *
     * @param createMis    用于创建MIS的权限
     * @param errorMessage 错误信息
     * @throws CheckException 检查异常，当权限不足时抛出
     */
    void checkPermission(String createMis, String errorMessage) throws CheckException;

    /**
     * 检查权限
     *
     * @param adminGroup   管理员组
     * @param createMis    创建MIS
     * @param errorMessage 错误信息
     * @throws CheckException 检查异常，当权限不足时抛出
     */
    void checkPermission(List<String> adminGroup, String createMis, String errorMessage) throws CheckException;
}
