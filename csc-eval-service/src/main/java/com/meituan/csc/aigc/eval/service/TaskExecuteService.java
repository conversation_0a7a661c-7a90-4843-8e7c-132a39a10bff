package com.meituan.csc.aigc.eval.service;

import com.meituan.csc.aigc.eval.dao.entity.EvalTaskPo;
import com.meituan.csc.aigc.eval.dao.entity.EvalTaskQueryPo;
import com.meituan.csc.aigc.eval.dao.entity.EvalTaskSessionPo;
import com.meituan.csc.aigc.eval.dto.PageData;
import com.meituan.csc.aigc.eval.dto.mark.RobotMockInfoDTO;
import com.meituan.csc.aigc.eval.dto.task.*;
import com.meituan.csc.aigc.eval.param.PageParam;
import com.meituan.csc.aigc.eval.param.task.*;
import com.meituan.csc.aigc.runtime.dto.UserDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface TaskExecuteService {

    /**
     * 创建任务
     *
     * @param taskParam 任务参数
     */
    void createTask(TaskParam taskParam);

    /**
     * 获取任务状态列表
     *
     * @return 任务状态列表
     */
    List<CustomStatusDTO> listTaskStatus();

    /**
     * 获取query状态列表
     *
     * @return query状态列表
     */
    List<CustomStatusDTO> listTaskQueryStatus(Integer taskType);

    /**
     * 分页查询任务详情
     *
     * @param pageParam 查询条件
     * @return 分页数据对象，包含任务详情信息
     */
    PageData<TaskDetailDTO> pageTaskDetail(PageParam<TaskQueryConditionParam> pageParam);

    /**
     * 分页查询任务列表
     *
     * @param pageParam 分页参数
     * @return 任务列表的分页数据
     */
    PageData<TaskDTO> pageTask(PageParam<TaskConditionParam> pageParam);

    /**
     * 删除任务
     *
     * @param taskId 任务ID
     */
    void deleteTask(Long taskId);

    /**
     * 获取任务概览信息
     *
     * @param taskId 任务ID
     * @return 任务的概览信息
     */
    TaskDTO overviewTask(Long taskId);

    /**
     * 下载任务详情
     *
     * @param conditionParam 查询条件参数
     * @return 下载任务的详细信息
     */
    String downloadTaskDetail(TaskQueryConditionParam conditionParam);

    /**
     * 获取任务报告
     *
     * @param taskId 任务ID
     * @return 任务报告DTO
     */
    TaskReportDTO taskReport(Long taskId);

    /**
     * 分页查询任务会话数据
     *
     * @param pageParam 分页参数对象，包含当前页码和每页显示数量
     * @return 分页结果对象，包含当前页的任务会话数据
     */
    PageData<TaskSessionResultDTO> pageSessionData(PageParam<TaskQuerySessionConditionParam> pageParam);

    /**
     * 获取会话详情
     *
     * @param conversationId 会话唯一ID
     * @param datasetId      数据集ID
     * @param taskId         任务ID
     * @param sessionId      会话ID
     * @return 会话详情
     */
    List<TaskDetailDTO> getSessionDetail(String conversationId, Long taskId, String sessionId, Long datasetId);

    /**
     * 根据查询ID获取任务
     *
     * @param queryId 查询ID
     * @return 任务信息
     */
    TaskQueryDTO getByQueryId(Long queryId);

    /**
     * 获取会话状态列表
     *
     * @return 会话状态列表
     */
    List<CustomStatusDTO> getSessionStatusList(Integer taskType);

    /**
     * 获取任务执行日志
     *
     * @param type          日志类型
     * @param queryDetailId query详情id
     * @param queryId       queryID
     * @return 任务执行日志
     */
    TaskLogDTO getTaskLog(Integer type, Long queryDetailId, Long queryId);

    /**
     * 查询用户信息
     *
     * @param mis      用户的MIS账号
     * @param pageSize 每页显示的数量
     * @param pageNum  当前页码
     * @return 用户信息
     */
    List<UserDTO> getMis(String mis, Integer pageSize, Integer pageNum);

    /**
     * 获取任务进度
     *
     * @param task          任务
     * @param taskQueryList 任务query列表
     * @param sessionList   任务会话列表
     * @return 任务进度
     */
    String buildProgress(EvalTaskPo task, TaskDetailCountDTO taskQueryList, TaskDetailCountDTO sessionList);

    /**
     * 获取默认字段映射
     *
     * @param param 查询参数
     * @return 默认字段映射的映射表
     */
    Map<Long, TaskColumnDTO> getFiledMap(TaskDatasetApplicationParam param);

    /**
     * 获取任务信息
     *
     * @param taskId 任务ID
     * @return 任务信息DTO
     */
    TaskInfoDTO getTaskInfo(Long taskId);

    /**
     * 获取用户模拟器列表
     *
     * @return
     */
    List<RobotMockInfoDTO> getRobotMockInfoList();

    TaskEvalDetailInfoDTO getEvalDetailInfo(Long taskId);

    List<TaskTestDetailDTO.AidaModelConfigDTO> evalTaskTest(TaskParam taskParam);

    void pauseTask(String taskId);

    void resumeTask(String taskId);
}
