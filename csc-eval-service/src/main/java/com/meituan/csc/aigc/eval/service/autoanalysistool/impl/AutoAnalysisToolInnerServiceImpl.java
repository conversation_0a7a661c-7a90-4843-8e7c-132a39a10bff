package com.meituan.csc.aigc.eval.service.autoanalysistool.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Maps;
import com.meituan.csc.aigc.eval.config.http.HttpConfig;
import com.meituan.csc.aigc.eval.constants.CatConstants;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.enums.TagMethodEnum;
import com.meituan.csc.aigc.eval.enums.TagSystemMarkingMethodEnum;
import com.meituan.csc.aigc.eval.excel.listener.ExcelDataListener;
import com.meituan.csc.aigc.eval.param.autoanalysistool.AnalysisResultToS3Param;
import com.meituan.csc.aigc.eval.param.autoanalysistool.AnalysisRobotResponse;
import com.meituan.csc.aigc.eval.param.autoanalysistool.TaskCreateParam;
import com.meituan.csc.aigc.eval.service.autoanalysistool.AutoAnalysisToolInnerService;
import com.meituan.csc.aigc.eval.service.impl.S3Service;
import com.meituan.csc.aigc.eval.service.push.DxPushTextService;
import com.meituan.csc.aigc.eval.service.strategy.eval.impl.CommonEvalStrategyService;
import com.meituan.csc.aigc.eval.utils.CommonUtils;
import com.meituan.csc.aigc.eval.utils.DateUtil;
import com.meituan.csc.aigc.eval.utils.HttpUtil;
import com.meituan.csc.aigc.eval.vo.ExcelUploadVO;
import com.meituan.csc.aigc.eval.vo.TagVO;
import com.meituan.csc.aigc.runtime.api.AidaGptService;
import com.meituan.csc.aigc.runtime.dto.aida.AidaFinalRes;
import com.meituan.csc.aigc.runtime.dto.aida.GptRequestParam;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.meituan.mtrace.Tracer;
import com.sankuai.csccratos.csc.aida.label.client.api.AidaSceneRemoteService;
import com.sankuai.csccratos.csc.aida.label.client.dto.common.AidaResponse;
import com.sankuai.csccratos.csc.aida.label.client.dto.scene.AidaSceneApplicationDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自动分析工具内部服务实现类
 * 
 * <AUTHOR>
 * @date 2025-05-18
 */
@Slf4j
@Service
public class AutoAnalysisToolInnerServiceImpl implements AutoAnalysisToolInnerService {

    @Autowired
    private S3Service s3Service;

    @Autowired
    private AidaSceneRemoteService aidaSceneRemoteService;

    @Autowired
    private DxPushTextService dxPushTextService;

    @Autowired
    private AidaGptService aidaGptService;

    @Autowired
    private HttpConfig httpConfig;

    /**
     * 上传Excel文件S3存储路径
     */
    public static final String UPLOAD_EXCEL_FILE_S3_PATH = "auto-analysis-tool/excel/upload/";

    /**
     * 导出分析结果Excel文件S3存储路径
     */
    public static final String EXPORT_EXCEL_FILE_S3_PATH = "auto-analysis-tool/excel/export/";

    /**
     * 上传Excel文件S3文件名间隔符
     */
    public static final String FILE_NAME_SEPARATOR = "-";

    /**
     * 自动化分析工具-分析结果接口名称
     */
    private static final String ANALYZE_RESULT_API_NAME = "analysis/tools/automation/?taskId=";

    /**
     * 调用aida机器人请求参数key: sessionId、businessScene
     */
    private static final String ROBOT_REQUEST_PARAM_SESSION_ID_KEY = "sessionId";
    private static final String ROBOT_REQUEST_PARAM_BUSINESS_SCENE_KEY = "businessScene";
    private static final String ROBOT_REQUEST_PARAM_APP_ID_KEY = "appId";

    /**
     * 使用http接口调用aida机器人请求头key
     */
    private static final String INPUTS = "inputs";
    private static final String APPLICATION_JSON = "application/json";
    
    /**
     * 使用http接口调用aida机器人请求URL
     */
    private static final String URL = "https://aida.vip.sankuai.com/v1/chat-messages";

    /**
     * AI搭用户
     */
    private static final String AIDA_USER = "auto-analysis-tool";

    /**
     * 鉴权前缀
     */
    private static final String AUTH_PREFIX = "Bearer ";

    @Override
    public TaskCreateParam createTaskCreateParam(MultipartFile file, String fileName, String misId, String userName,
            String fileUrl, String businessScene, String tagList, String tagGroupName, Long tagGroupId, Long sceneId) {
                TaskCreateParam taskCreateParam = new TaskCreateParam();
                taskCreateParam.setFileName(fileName);
                taskCreateParam.setMisId(misId);
                taskCreateParam.setUserName(userName);
                taskCreateParam.setFileUrl(fileUrl);
                taskCreateParam.setBusinessScene(businessScene);
                taskCreateParam.setTagList(parseTagList(tagList));
                taskCreateParam.setTagGroupName(tagGroupName);
                taskCreateParam.setTagGroupId(tagGroupId);
                taskCreateParam.setSceneId(sceneId);
                taskCreateParam.setFile(file);
        
                return taskCreateParam;
    }


    @Override
    public String exportAnalysisResultToExcel(List<AnalysisResultToS3Param> resultList) {
        Transaction transaction = Cat.newTransaction(CatConstants.AUTO_ANALYSIS_TOOL, "exportAnalysisResultToS3");
        try {
            if (Objects.isNull(resultList) || resultList.isEmpty()) {
                log.warn("导出分析结果失败，结果列表为空");
                return null;
            }

            // 生成S3文件名
            String s3FileName = new StringBuilder()
                .append(EXPORT_EXCEL_FILE_S3_PATH)
                .append("result")
                .append(FILE_NAME_SEPARATOR)
                .append(CommonUtils.uuid())
                .append(FILE_NAME_SEPARATOR)
                .append(DateUtil.getSimpleToday())
                .append(".xlsx")
                .toString();

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream)
                    .sheet("分析结果")
                    .head(AnalysisResultToS3Param.class)
                    .doWrite(resultList);
            
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            String fileUrl = s3Service.upload(inputStream, s3FileName);

            return fileUrl;
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("生成分析结果Excel并上传到S3失败", e);
            return null;
        } finally {
            transaction.complete();
        }
    }

    @Override
    public String getAppIdByAnalysisScene(Long sceneId) {
        Transaction transaction = Cat.newTransaction(CatConstants.AUTO_ANALYSIS_TOOL, "getAppIdByAnalysisScene");
        try {
            if (Objects.isNull(sceneId)) {
                log.warn("根据分析场景id获取对应的appId失败, sceneId={}", sceneId);
                return null;
            }

            AidaResponse<List<AidaSceneApplicationDTO>> applicationDTOList = aidaSceneRemoteService.listApplicationsBySceneId(sceneId);
            if (applicationDTOList.getCode() != 0 || CollectionUtils.isEmpty(applicationDTOList.getData())) {
                log.info("根据分析场景id获取对应的appId失败, sceneId={}", sceneId);
                return null;
            }

            return applicationDTOList.getData().get(0).getAppId();
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("根据分析场景id获取对应的appId异常, sceneId={}", sceneId, e);
            return null;
        } finally {
            transaction.complete();
        }
    }

    @Override
    public ExcelUploadVO validateExcelHeaders(ExcelDataListener listener) {
        Transaction transaction = Cat.newTransaction(CatConstants.AUTO_ANALYSIS_TOOL, "validateExcelHeaders");
        try {
            if (Objects.isNull(listener)) {
                return ExcelUploadVO.builder()
                        .errorMessage("无法解析文件内容，请检查文件格式")
                        .build();
            }

            Map<Integer, String> secondRowHeaders = listener.getSecondRowHeaders();
            
            if (Objects.isNull(secondRowHeaders) || secondRowHeaders.isEmpty()) {
                return ExcelUploadVO.builder()
                        .errorMessage("文件表头格式不正确，请确保包含表头")
                        .build();
            }
            
            // 检查表头中是否包含sessionId和time
            boolean hasSessionIdHeader = false;
            boolean hasTimeHeader = false;
            
            for (Map.Entry<Integer, String> entry : secondRowHeaders.entrySet()) {
                String header = entry.getValue();
                
                if ("sessionId".equals(header)) {
                    hasSessionIdHeader = true;
                } else if ("time".equals(header)) {
                    hasTimeHeader = true;
                }
            }
            
            if (!hasSessionIdHeader) {
                return ExcelUploadVO.builder()
                        .errorMessage("文件缺少必要的列：sessionId，请检查文件内容")
                        .build();
            }
            
            if (!hasTimeHeader) {
                return ExcelUploadVO.builder()
                        .errorMessage("文件缺少必要的列：time，请检查文件内容")
                        .build();
            }
            
            // 所有校验通过
            return null;
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("验证excel表头异常, listener={}", listener, e);
            return null;
        } finally {
            transaction.complete();
        }
    }

    @Override
    public ExcelDataListener parseExcelFile(MultipartFile file) {
        Transaction transaction = Cat.newTransaction(CatConstants.AUTO_ANALYSIS_TOOL, "parseExcelFile");
        try {
            if (Objects.isNull(file) || file.isEmpty()) {
                log.warn("解析excel文件失败, fileName={}", file.getOriginalFilename());
                return null;
            }
    
            // 创建自定义监听器
            ExcelDataListener listener = new ExcelDataListener();
    
            // 使用EasyExcel读取文件
            EasyExcel.read(file.getInputStream())
                    .sheet()
                    .headRowNumber(2) // 表头占2行
                    .registerReadListener(listener)
                    .doRead();
    
            return listener;
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("解析excel文件异常, fileName={}", file.getOriginalFilename(), e);
            return null;
        } finally {
            transaction.complete();
        }
    }

    @Override
    public List<String> extractSessionIdsFromExcel(MultipartFile file) {
        Transaction transaction = Cat.newTransaction(CatConstants.AUTO_ANALYSIS_TOOL, "extractSessionIdsFromExcel");
        try {
            List<String> sessionIds = new ArrayList<>();

            // 解析excel文件
            ExcelDataListener listener = parseExcelFile(file);
            if (Objects.isNull(listener)) {
                log.error("解析excel文件失败, fileName={}", file.getOriginalFilename());
                return sessionIds;
            }

            // 获取首行和第二行表头，以及内容
            Map<Integer, String> firstRowHeaders = listener.getFirstRowHeaders();
            Map<Integer, String> secondRowHeaders = listener.getSecondRowHeaders();
            List<Map<Integer, String>> dataList = listener.getDataList();
            
            if (Objects.isNull(firstRowHeaders) || firstRowHeaders.isEmpty() || 
                Objects.isNull(secondRowHeaders) || secondRowHeaders.isEmpty()) {
                log.error("表头格式不正确，无法提取sessionId, fileName={}", file.getOriginalFilename());
                return sessionIds;
            }

            // 首先找出所有"会话信息"列的索引
            Set<Integer> sessionInfoColumns = new HashSet<>();
            for (Map.Entry<Integer, String> entry : firstRowHeaders.entrySet()) {
                if ("会话信息".equals(entry.getValue())) {
                    sessionInfoColumns.add(entry.getKey());
                }
            }
            
            if (sessionInfoColumns.isEmpty()) {
                log.error("未找到会话信息列, fileName={}", file.getOriginalFilename());
                return sessionIds;
            }

            // 在"会话信息"列下找到"sessionId"列的索引
            Integer sessionIdColumnIndex = null;
            for (Map.Entry<Integer, String> entry : secondRowHeaders.entrySet()) {
                int columnIndex = entry.getKey();
                String header = entry.getValue();
                
                if (sessionInfoColumns.contains(columnIndex) && "sessionId".equals(header)) {
                    sessionIdColumnIndex = columnIndex;
                    break;
                }
            }
            
            if (Objects.isNull(sessionIdColumnIndex)) {
                log.error("未找到sessionId列或该列不在会话信息下方, fileName={}", file.getOriginalFilename());
                return sessionIds;
            }
            
            // 提取数据
            for (Map<Integer, String> row : dataList) {
                String sessionId = row.get(sessionIdColumnIndex);
                if (StringUtils.isNotBlank(sessionId)) {
                    sessionIds.add(sessionId);
                }
            }

            return sessionIds;
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("从Excel文件中提取sessionId异常, fileName={}", file.getOriginalFilename(), e);
            return new ArrayList<>();
        } finally {
            transaction.complete();
        }
    }

    @Override
    public ExcelUploadVO uploadFileToS3(MultipartFile file, String misId) {
        Transaction transaction = Cat.newTransaction(CatConstants.AUTO_ANALYSIS_TOOL, "uploadFileToS3");
        try {
            if (Objects.isNull(file) || file.isEmpty()) {
                log.error("上传文件不能为空, uploadMis={}", misId);
                return null;
            }
            
            String originalFilename = file.getName();
            // 生成S3文件名
            String s3FileName = new StringBuilder()
                .append(UPLOAD_EXCEL_FILE_S3_PATH)
                .append(Objects.requireNonNull(originalFilename))
                .append(FILE_NAME_SEPARATOR)
                .append(CommonUtils.uuid())
                .append(FILE_NAME_SEPARATOR)
                .append(DateUtil.getSimpleToday())
                    .append(".xlsx")
                .toString();
            // 上传到S3
            String fileUrl = null;
            // 转换输入流

            fileUrl = s3Service.upload(file, s3FileName);

            if (Objects.isNull(fileUrl)) {
                log.info("文件上传失败, s3FileName={}, fileUrl={}, uploadMis={}", s3FileName, fileUrl, misId);
                return null;
            }
    
            // 构建返回对象
            return ExcelUploadVO.builder()
                .url(fileUrl)
                .fileKey(s3FileName)
                .build();
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("上传文件到S3异常, uploadMis={}", misId, e);
            return null;
        } finally {
            transaction.complete();
        }
    }

    @Override
    public void sendElephantMessage(Boolean isSuccess, Long subTaskId, String mis) {
        String url = Lion.getString(ConfigUtil.getAppkey(), LionConstants.AUTO_ANALYZE_TOOL_DOMAIN) + ANALYZE_RESULT_API_NAME + subTaskId + "&view=statistics";
        String message = String.format("您上传的自动标注任务已处理完成，[点击查看详情|%s]", url);
        if (Boolean.FALSE.equals(isSuccess)) {
            message = String.format("您上传的自动标注任务未全部处理完成，[点击查看已处理详情|%s]", url);
        }
        try {
            dxPushTextService.pushTextByMisName(message, mis);
            log.info("自动标注任务完成, 发送大象消息成功, mis: {}, message: {}", mis, message);
        } catch (Exception e) {
            log.error("自动标注任务完成, 发送大象消息失败, mis: {}, message: {}", mis, message, e);
        }
    }

    @Override
    public AidaFinalRes callAidaRobot(String sessionId, String businessScene, String appId) {
        Transaction transaction = Cat.newTransaction(CatConstants.AUTO_ANALYSIS_TOOL, "callAidaRobot");
        try {
            // 1. 构建aida请求参数
            Map<String, String> inputs = Maps.newHashMap();
            inputs.put(ROBOT_REQUEST_PARAM_SESSION_ID_KEY, sessionId);
            inputs.put(ROBOT_REQUEST_PARAM_BUSINESS_SCENE_KEY, businessScene);
            inputs.put(ROBOT_REQUEST_PARAM_APP_ID_KEY, appId);

            AidaFinalRes aidaFinalRes = null;

            Boolean useHttpInterface = Lion.getBoolean(ConfigUtil.getAppkey(), LionConstants.AUTO_ANALYZE_TOOL_USE_HTTP_INTERFACE_SWITCH, false);
            if (Boolean.TRUE.equals(useHttpInterface)) {
                // 使用http接口调用aida机器人
                Map<String, Object> requestBody = new HashMap<>();
                requestBody.put(INPUTS, inputs);

                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.add(HttpHeaders.AUTHORIZATION, AUTH_PREFIX + Lion.getString(ConfigUtil.getAppkey(), LionConstants.AUTO_ANALYZE_TOOL_AIDA_ROBOT_AUTH));
                httpHeaders.add(HttpHeaders.CONTENT_TYPE, APPLICATION_JSON);

                String result = HttpUtil.sendPost(JSONObject.toJSONString(requestBody), httpHeaders, URL, String.class, httpConfig.getRestTemplate());
                log.info("http请求aida机器人结果result: {}, sessionId={}", result, sessionId);
                if (StringUtils.isNotBlank(result)) {
                    aidaFinalRes = JSONObject.parseObject(result, AidaFinalRes.class);
                    log.info("http请求aida机器人结果aidaFinalRes: {}, sessionId={}", aidaFinalRes, sessionId);
                    if (aidaFinalRes != null) {
                        aidaFinalRes.setCode(CommonEvalStrategyService.GPT_SUCCESS_CODE);
                    }
                }
            } else {
                // 使用pigeon接口调用aida机器人
                GptRequestParam requestParam = new GptRequestParam();
                requestParam.setQuery(Tracer.id());
                requestParam.setAuthorization(AUTH_PREFIX + Lion.getString(ConfigUtil.getAppkey(), LionConstants.AUTO_ANALYZE_TOOL_AIDA_ROBOT_AUTH));
                requestParam.setUser(AIDA_USER);
                requestParam.setInputs(inputs);
                aidaFinalRes = aidaGptService.completion(requestParam);

                // 2. 判断aida响应结果是否正常
                // 2.1 如果响应结果为429，则重新请求aida，直到响应结果不为429
                int retryCount = 0;
                int retryMaxCount = Lion.getInt(ConfigUtil.getAppkey(), LionConstants.AUTO_ANALYZE_TOOL_AIDA_ROBOT_RETRY_COUNT, 3);
                while (Objects.nonNull(aidaFinalRes) && "429".equals(aidaFinalRes.getCode()) && retryCount < retryMaxCount){
                    log.info("请求aida被限流, inputs={}", inputs);
                    aidaFinalRes = aidaGptService.completion(requestParam);
                    retryCount++;
                    Thread.sleep(1000);
                }
            }
            
            // 2.2 如果请求失败则返回空
            if (Objects.isNull(aidaFinalRes) || !"200".equals(aidaFinalRes.getCode()) || StringUtils.isBlank(aidaFinalRes.getAnswer())) {
                log.error("请求aida失败, inputs={}, aidaFinalRes={}", inputs, aidaFinalRes);
                return null;
            }

            return aidaFinalRes;   
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("请求aida异常, sessionId={}", sessionId, e);
            return null;
        } finally {
            transaction.complete();
        }
    }

    @Override
    public List<AnalysisRobotResponse> parseAidaResponse(AidaFinalRes aidaFinalRes, List<String> autoTags, String sessionId) {
        Transaction transaction = Cat.newTransaction(CatConstants.AUTO_ANALYSIS_TOOL, "parseAidaResponse");
        try {
            if (Objects.isNull(aidaFinalRes)) {
                log.error("aida响应结果为空, sessionId={}", sessionId);
                return null;
            }

            // 首先解析外层JSON，获取tagResult字段
            JSONObject jsonObject = JSONObject.parseObject(aidaFinalRes.getAnswer());
            log.info("解析aida响应结果jsonObject: {}, sessionId={}", jsonObject, sessionId);
            String tagResultStr = jsonObject.getString("tagResult");
            log.info("解析aida响应结果tagResultStr: {}, sessionId={}", tagResultStr, sessionId);
            // 解析tagResult字符串为AnalysisRobotResponse列表
            List<AnalysisRobotResponse> resultList = JSONObject.parseArray(tagResultStr, AnalysisRobotResponse.class);
            log.info("解析aida响应结果resultList: {}, sessionId={}", resultList, sessionId);

            // 4. 根据autoTags过滤resultList
            if (CollectionUtils.isNotEmpty(autoTags)) {
                resultList = resultList.stream()
                        .filter(response -> response.getQuestionTag().contains("未识别")
                                || autoTags.contains(response.getQuestionTag()))
                        .collect(Collectors.toList());
            }
            return resultList;
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("解析aida响应结果异常, sessionId={}", sessionId, e);
            return null;
        } finally {
            transaction.complete();
        }
    }

    /**
     * 解析标签列表
     * @param tagListStr 标签列表字符串
     * @return 标签列表
     */
    private List<TagVO> parseTagList(String tagListStr) {
        if (StringUtils.isBlank(tagListStr)) {
            log.warn("标签列表字符串为空");
            return new ArrayList<>();
        }

        try {
            List<TagVO> tagList = JSON.parseArray(tagListStr, TagVO.class);
            if (CollectionUtils.isEmpty(tagList)) {
                log.warn("解析标签列表为空，tagListStr={}", tagListStr);
                return new ArrayList<>();
            }

            List<TagVO> result = new ArrayList<>(tagList.size());
            for (TagVO tag : tagList) {
                if (Objects.isNull(tag) || Objects.isNull(tag.getTagMethod())) {
                    continue;
                }

                Integer tagMethod = tag.getTagMethod();
                if (TagSystemMarkingMethodEnum.MODEL.getCode() == tagMethod
                        || TagSystemMarkingMethodEnum.AIDA_APPLICATION.getCode() == tagMethod) {
                    tag.setTagMethod(TagMethodEnum.AUTOMATIC.getCode());
                } else if (TagSystemMarkingMethodEnum.MANUAL.getCode() == tagMethod) {
                    tag.setTagMethod(TagMethodEnum.MANUAL.getCode());
                }
                result.add(tag);
            }
            return result;
        } catch (Exception e) {
            log.error("解析标签列表过程中发生异常，tagListStr={}", tagListStr, e);
            return new ArrayList<>();
        }
    }
}
