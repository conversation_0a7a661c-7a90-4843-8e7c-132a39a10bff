package com.meituan.csc.aigc.eval.service.filter.autoinspect;

import com.meituan.csc.aigc.eval.param.autoInspect.AutoInspectParam;
import com.meituan.csc.aigc.eval.enums.filter.AutoInspectChainMarkEnum;
import com.meituan.csc.aigc.eval.service.filter.AbstractFilterChainHandler;

public interface AutoInspectChainFilter<T extends AutoInspectParam> extends AbstractFilterChainHandler<AutoInspectParam> {
    @Override
    default String mark() {
        return AutoInspectChainMarkEnum.AUTO_INSPECT_UPDATE_FILTER.name();
    }
}
