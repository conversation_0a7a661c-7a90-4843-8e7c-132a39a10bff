package com.meituan.csc.aigc.eval.service.filter.autoinspect;

import com.meituan.csc.aigc.eval.enums.EvalMetricTypeEnum;
import com.meituan.csc.aigc.eval.param.autoInspect.AutoInspectParam;
import com.meituan.csc.aigc.eval.utils.CommonUtils;
import org.springframework.stereotype.Component;

@Component
public class AutoInspectParamNotNullChainFilter implements AutoInspectChainFilter<AutoInspectParam> {

    @Override
    public void handler(AutoInspectParam requestParam) {
        CommonUtils.checkEval(requestParam.getAidaModelConfig() != null, "巡检应用不能为空");
        CommonUtils.checkEval(requestParam.getMetricId() != null, "指标不能为空");
        if (requestParam.getMetricId() != EvalMetricTypeEnum.AUTO.getCode()) {
            CommonUtils.checkEval(requestParam.getMetricContent() != null, "metricContent不能为空");
        }
        CommonUtils.checkEval(requestParam.getMaxScale() != null, "最大量级不能为空");
        CommonUtils.checkEval(requestParam.getStatus() != null, "配置状态不能为空");
        CommonUtils.checkEval(requestParam.getMode() != null, "执行方式不能为空");
    }

    @Override
    public int getOrder() {
        return 100;
    }
}
