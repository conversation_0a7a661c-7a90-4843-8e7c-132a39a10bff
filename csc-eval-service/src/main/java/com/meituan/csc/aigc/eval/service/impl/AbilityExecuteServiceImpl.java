package com.meituan.csc.aigc.eval.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.csc.aigc.eval.dao.entity.MetricConfigPo;
import com.meituan.csc.aigc.eval.dao.service.generator.MetricConfigGeneratorService;
import com.meituan.csc.aigc.eval.dto.ability.AbilityDTO;
import com.meituan.csc.aigc.eval.dto.metric.MetricDTO;
import com.meituan.csc.aigc.eval.enums.AbilityEnum;
import com.meituan.csc.aigc.eval.enums.MetricClassifyTypeEnum;
import com.meituan.csc.aigc.eval.enums.MetricTypeEnum;
import com.meituan.csc.aigc.eval.param.metric.MetricConditionParam;
import com.meituan.csc.aigc.eval.service.AbilityExecuteService;
import com.sankuai.meituan.auth.util.UserUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class AbilityExecuteServiceImpl implements AbilityExecuteService {


    @Autowired
    private MetricConfigGeneratorService metricConfigGeneratorService;

    @Override
    public List<AbilityDTO> listAbility() {
        // 自定义指标
        MetricConditionParam condition = new MetricConditionParam();
        condition.setCreateMis(UserUtils.getUser().getLogin());
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByCondition(condition);
        // 通用指标
        condition = new MetricConditionParam();
        condition.setMetricClassifyType(MetricClassifyTypeEnum.COMMON.getCode());
        List<MetricConfigPo> commonMetricConfigList = metricConfigGeneratorService.getByCondition(condition);

        List<AbilityDTO> abilityList = new ArrayList<>();
        // 单轮会话
        AbilityDTO abilityDTO = new AbilityDTO();
        abilityDTO.setAbilityName(AbilityEnum.SINGLE_ROUND.getDescription());
        abilityDTO.setAbilityType(AbilityEnum.SINGLE_ROUND.getCode());
        AbilityDTO.Metric metric = new AbilityDTO.Metric();
        metric.setCommonMetric(getCommonMetricDTO(commonMetricConfigList));
        metric.setCustomMetric(getCustomMetricDTO(metricConfigList, AbilityEnum.SINGLE_ROUND));
        abilityDTO.setMetric(metric);
        abilityList.add(abilityDTO);
        // 多轮会话
        abilityDTO = new AbilityDTO();
        abilityDTO.setAbilityName(AbilityEnum.MULTI_ROUND.getDescription());
        abilityDTO.setAbilityType(AbilityEnum.MULTI_ROUND.getCode());
        metric = new AbilityDTO.Metric();
        metric.setCommonMetric(getCommonMetricDTO(commonMetricConfigList));
        metric.setCustomMetric(getCustomMetricDTO(metricConfigList, AbilityEnum.MULTI_ROUND));
        abilityDTO.setMetric(metric);
        abilityList.add(abilityDTO);
        return abilityList;
    }

    private List<MetricDTO> getCommonMetricDTO(List<MetricConfigPo> metricConfigList) {
        return metricConfigList.stream()
                .map(config -> {
                    MetricDTO metricDTO = new MetricDTO();
                    metricDTO.setMetricId(config.getId().intValue());
                    metricDTO.setMetricName(config.getName());
                    metricDTO.setMetricDescription(config.getStandard());
                    return metricDTO;
                })
                .collect(Collectors.toList());
    }

    private List<MetricDTO> getCustomMetricDTO(List<MetricConfigPo> metricConfigList, AbilityEnum abilityEnum) {
        List<MetricConfigPo> allMetricConfigList = new ArrayList<>();
        MetricConditionParam condition = new MetricConditionParam();
        condition.setCreateMis("system");
        List<MetricConfigPo> metricConfigPoList = metricConfigGeneratorService.getByCondition(condition);
        if (CollectionUtils.isNotEmpty(metricConfigPoList)) {
            allMetricConfigList.addAll(metricConfigPoList);
        }
        if (CollectionUtils.isNotEmpty(metricConfigList)) {
            allMetricConfigList.addAll(metricConfigList);
        }
        return allMetricConfigList.stream()
                .filter(config -> config.getType() != null && config.getType() == MetricClassifyTypeEnum.CUSTOM.getCode())
                .filter(config -> {
                    if (abilityEnum == null || "system".equals(config.getCreatorMis())) {
                        return true;
                    }
                    return config.getAbilityType() != null && abilityEnum.getCode() == config.getAbilityType();
                })
                .map(config -> {
                    MetricDTO metricDTO = new MetricDTO();
                    metricDTO.setMetricId(config.getId().intValue());
                    metricDTO.setMetricName(config.getName());
                    metricDTO.setMetricType(config.getMetricType());
                    metricDTO.setMetricDescription(config.getStandard());
                    JSONObject jsonObject = JSON.parseObject(config.getRanges());
                    if (config.getMetricType() == MetricTypeEnum.NUMBER.getCode()) {
                        metricDTO.setStartScore(Integer.parseInt(jsonObject.getString("startScore")));
                        metricDTO.setEndScore(Integer.parseInt(jsonObject.getString("endScore")));
                    } else if(config.getMetricType() == MetricTypeEnum.ENUMERATION.getCode()) {
                        metricDTO.setOutputEnum(jsonObject.getJSONArray("resultEnum").toJavaList(String.class));
                    }
                    return metricDTO;
                })
                .collect(Collectors.toList());
    }
}
