package com.meituan.csc.aigc.eval.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.StoreBoundResponse;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.meituan.csc.aigc.eval.constants.CatConstants;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.constants.RedisConstants;
import com.meituan.csc.aigc.eval.dao.entity.*;
import com.meituan.csc.aigc.eval.dao.mapper.AutoInspectConfigMapper;
import com.meituan.csc.aigc.eval.dao.service.generator.*;
import com.meituan.csc.aigc.eval.dto.PageData;
import com.meituan.csc.aigc.eval.dto.application.ApplicationInfoDTO;
import com.meituan.csc.aigc.eval.dto.autoInspect.AutoInspectMetricDTO;
import com.meituan.csc.aigc.eval.dto.autoInspect.AutoInspectPanelDimensionDTO;
import com.meituan.csc.aigc.eval.dto.autoInspect.AutoInspectPanelPageDTO;
import com.meituan.csc.aigc.eval.dto.dataset.TemplateFieldBindDTO;
import com.meituan.csc.aigc.eval.dto.metric.MetricDTO;
import com.meituan.csc.aigc.eval.enums.*;
import com.meituan.csc.aigc.eval.enums.dataset.DatasetCreateionMethodEnum;
import com.meituan.csc.aigc.eval.enums.dataset.DatasetDetailStatusEnum;
import com.meituan.csc.aigc.eval.enums.task.TaskHistorySourceEnum;
import com.meituan.csc.aigc.eval.exception.EvalException;
import com.meituan.csc.aigc.eval.helper.PullDataThreadLocalHelper;
import com.meituan.csc.aigc.eval.param.application.ApplicationConditionParam;
import com.meituan.csc.aigc.eval.param.application.ApplicationParam;
import com.meituan.csc.aigc.eval.param.application.DatasetApplicationParam;
import com.meituan.csc.aigc.eval.param.autoInspect.AutoInspectDetailConditionParam;
import com.meituan.csc.aigc.eval.param.autoInspect.AutoInspectParam;
import com.meituan.csc.aigc.eval.enums.filter.AutoInspectChainMarkEnum;
import com.meituan.csc.aigc.eval.param.dataset.ConditionConfig;
import com.meituan.csc.aigc.eval.param.dataset.DataSetParam;
import com.meituan.csc.aigc.eval.param.autoInspect.AutoInspectConfigConditionParam;
import com.meituan.csc.aigc.eval.param.dataset.DatasetPullResponse;
import com.meituan.csc.aigc.eval.param.dataset.PullDataParam;
import com.meituan.csc.aigc.eval.param.task.*;
import com.meituan.csc.aigc.eval.proxy.AidaInvokeServiceProxy;
import com.meituan.csc.aigc.eval.proxy.RedisClientProxy;
import com.meituan.csc.aigc.eval.service.*;
import com.meituan.csc.aigc.eval.service.filter.AbstractFilterChainContext;
import com.meituan.csc.aigc.eval.service.push.DxPushTextService;
import com.meituan.csc.aigc.eval.service.strategy.eval.EvalStrategyService;
import com.meituan.csc.aigc.eval.service.strategy.eval.impl.CommonEvalStrategyService;
import com.meituan.csc.aigc.eval.utils.CommonUtils;
import com.meituan.csc.aigc.eval.utils.DateUtil;
import com.meituan.csc.aigc.eval.utils.ThreadUtils;
import com.meituan.csc.aigc.runtime.inner.dto.InnerAppConfigDTO;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.meituan.csc.aigc.eval.enums.AbilityEnum.MULTI_ROUND;

@Slf4j
@Service
@RequiredArgsConstructor
public class AutoInspectServiceImpl implements AutoInspectService {
    private final AbstractFilterChainContext<AutoInspectParam> autoInspectFilterChainContext;
    @Autowired
    private AidaExecuteService aidaExecuteService;

    @Autowired
    private ApplicationConfigGeneratorService applicationConfigGeneratorService;
    @Autowired
    private AutoInspectConfigGeneratorService autoInspectConfigGeneratorService;
    @Autowired
    private AutoInspectConfigMapper autoInspectConfigMapper;
    @Autowired
    private RedisClientProxy redisStoreClient;
    @Autowired
    private TaskExecuteService taskExecuteService;
    @Autowired
    private DatasetPullService datasetPullService;
    @Autowired
    private ApplicationExecuteService applicationExecuteService;
    @Autowired
    private EvalDatasetGeneratorService evalDatasetGeneratorService;

    @Autowired
    private EvalDatasetDetailGeneratorService evalDatasetDetailGeneratorService;
    @Autowired
    private EvalTaskQueryDetailGeneratorService evalTaskQueryDetailGeneratorService;
    @Autowired
    private EvalTaskQueryGeneratorService evalTaskQueryGeneratorService;

    @Autowired
    private AidaInvokeServiceProxy aidaInvokeServiceProxy;
    @Autowired
    private DxPushTextService dxPushTextService;
    @Autowired
    private TaskExecuteServiceImpl taskExecuteServiceImpl;
    @Autowired
    private CommonEvalStrategyService commonEvalStrategyService;
    @Autowired
    private List<EvalStrategyService> evalStrategyServiceList;

    private Map<String, EvalStrategyService> evalServiceMap;

    @PostConstruct
    public void init() {
        if (MapUtils.isEmpty(evalServiceMap)) {
            evalServiceMap = evalStrategyServiceList.stream().collect(Collectors.toMap(EvalStrategyService::getName, Function.identity()));
        }
    }

    @Autowired
    private EvalTaskGeneratorService evalTaskGeneratorService;
    @Autowired
    private MetricConfigGeneratorService metricConfigGeneratorService;
    @Autowired
    private MetricExecuteService metricExecuteService;
    @Autowired
    private AutoInspectDetailGeneratorService autoInspectDetailGeneratorService;
    private final ExecutorService executor = ThreadUtils.createPoolWithTraceAndCat(10, 10, 100, "auto-inspect-thread-%s");

    @Override
    public Long create(AutoInspectParam autoInspectParam) {
        autoInspectFilterChainContext.handler(AutoInspectChainMarkEnum.AUTO_INSPECT_UPDATE_FILTER.name(), autoInspectParam);
        AutoInspectConfigPo autoInspectConfigPo = new AutoInspectConfigPo();
        // 设定入表参数
        buildAutoInspectConfig(autoInspectConfigPo, autoInspectParam);
        // 判断是否立即执行,如果立即执行则直接执行。
        if (autoInspectParam.getMode() == AutoInspectExecutionModeEnum.IMMEDIATE.getCode()) {
            AutoInspectDetailPo autoInspectDetailPo = saveToAutoInspectDetail(autoInspectConfigPo);
            submitImmediateTask(autoInspectDetailPo);
            return autoInspectDetailPo.getId();
        }
        // 如果是定期巡检,并且配置为生效,则取消其他配置
        if (autoInspectParam.getStatus() == AutoInspectConfigStatusEnum.AVAILABLE.getCode() && autoInspectParam.getMode() == AutoInspectExecutionModeEnum.SCHEDULED.getCode()) {
            cancelOtherConfig(autoInspectParam.getAidaModelConfig().get(0).getApplicationId());
        }
        // 将配置入库
        autoInspectConfigGeneratorService.save(autoInspectConfigPo);
        // 返回巡检配置Id
        return autoInspectConfigPo.getId();
    }

    private AutoInspectDetailPo saveToAutoInspectDetail(AutoInspectConfigPo autoInspectConfigPo) {
        AutoInspectDetailPo autoInspectDetailPo = new AutoInspectDetailPo();
        // 如果是立即执行,则config为空
        if (autoInspectConfigPo.getMode() == AutoInspectExecutionModeEnum.IMMEDIATE.getCode()) {
            autoInspectDetailPo.setAutoInspectConfigId(null);
        } else {
            autoInspectDetailPo.setAutoInspectConfigId(autoInspectConfigPo.getId());
        }
        autoInspectDetailPo.setStatus(AutoInspectEvalStatusEnum.CREATING.getCode());
        autoInspectDetailPo.setFrequencyType(autoInspectConfigPo.getFrequencyType());
        autoInspectDetailPo.setFrequencyDay(autoInspectConfigPo.getFrequencyDay());
        autoInspectDetailPo.setMetricId(Long.valueOf(autoInspectConfigPo.getMetricId()));
        autoInspectDetailPo.setCreatorMis(autoInspectConfigPo.getCreatorMis());
        autoInspectDetailPo.setUpdaterMis(autoInspectConfigPo.getUpdaterMis());
        autoInspectDetailPo.setGmtCreated(new Date());
        autoInspectDetailPo.setGmtModified(new Date());
        autoInspectDetailPo.setPlatformApp(autoInspectConfigPo.getPlatformApp());
        AutoInspectParam autoInspectParam = new AutoInspectParam();
        buildAutoInspectParamResult(autoInspectParam, autoInspectConfigPo);
        String config = JSONObject.toJSONString(autoInspectParam,SerializerFeature.WriteMapNullValue);
        autoInspectDetailPo.setExtra(config);
        autoInspectDetailPo.setModelConfigVersionId(autoInspectConfigPo.getModelConfigVersionId());
        autoInspectDetailGeneratorService.save(autoInspectDetailPo);
        return autoInspectDetailPo;
    }

    /**
     * 巡检任务从这里执行
     *
     * @param autoInspectDetailPo
     */
    public void submitInspectTask(AutoInspectDetailPo autoInspectDetailPo) {
        AutoInspectParam autoInspectParam = JSON.parseObject(autoInspectDetailPo.getExtra(), AutoInspectParam.class);
        AutoInspectConfigPo autoInspectConfigPo = new AutoInspectConfigPo();
        autoInspectConfigPo.setCreatorMis(autoInspectDetailPo.getCreatorMis());
        autoInspectConfigPo.setUpdaterMis(autoInspectDetailPo.getUpdaterMis());
        buildAutoInspectConfig(autoInspectConfigPo, autoInspectParam);
        executor.submit(() -> autoInspectExecute(autoInspectConfigPo, autoInspectDetailPo));
    }

    /**
     * 立即执行走这里执行
     *
     * @param autoInspectDetailPo
     */
    public void submitImmediateTask(AutoInspectDetailPo autoInspectDetailPo) {
        AutoInspectParam autoInspectParam = JSON.parseObject(autoInspectDetailPo.getExtra(), AutoInspectParam.class);
        AutoInspectConfigPo autoInspectConfigPo = new AutoInspectConfigPo();
        buildAutoInspectConfig(autoInspectConfigPo, autoInspectParam);
        // 提交任务到executor执行
        executor.submit(() -> autoInspectExecute(autoInspectConfigPo, autoInspectDetailPo));
    }

    /**
     * 构建自动巡检配置
     *
     * @param autoInspectConfigPo 自动巡检配置PO对象，用于存储到数据库
     * @param autoInspectParam    自动巡检参数，包含用户输入的配置信息
     */
    private void buildAutoInspectConfig(AutoInspectConfigPo autoInspectConfigPo, AutoInspectParam autoInspectParam) {
        // 获取AIDA模型配置
        AidaModelConfig aidaModelConfig = autoInspectParam.getAidaModelConfig().get(0);
        // 设置工作空间相关信息
        autoInspectConfigPo.setWorkSpaceId(aidaModelConfig.getWorkspaceId());
        autoInspectConfigPo.setWorkSpaceName(aidaModelConfig.getWorkspaceName());
        // 设置模型配置版本相关信息
        autoInspectConfigPo.setModelConfigVersionId(aidaModelConfig.getModelConfigVersionId());
        autoInspectConfigPo.setModelConfigVersionName(aidaModelConfig.getModelConfigVersionName());
        // 设置平台应用和工作空间
        autoInspectConfigPo.setPlatformApp(aidaModelConfig.getApplicationId());
        autoInspectConfigPo.setPlatformWorkspace(aidaModelConfig.getWorkspaceId());
        // 根据modelConfigVersionId查询应用配置
        ApplicationParam applicationParam = new ApplicationParam();
        applicationParam.setName("ai搭机器人:" + aidaModelConfig.getModelConfigVersionId());
        applicationParam.setSource(ApplicationSourceEnum.VIRTUAL.getCode());
        applicationParam.setCreateMis(autoInspectConfigPo.getCreatorMis());
        applicationParam.setAidaModelConfig(aidaModelConfig);
        ApplicationConfigPo applicationConfig = applicationExecuteService.getOrCreateFromThirdSystem(applicationParam);
        // 将评分阈值列表转换为JSON字符串
        String scoreThresholdList = JSON.toJSONString(autoInspectParam.getScoreThresholdList(),
                SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullStringAsEmpty);
        autoInspectConfigPo.setScoreThresholdList(scoreThresholdList);
        // 设置应用ID
        autoInspectConfigPo.setApplicationId(applicationConfig.getId());
        // 设置创建者和更新者信息
        if (autoInspectConfigPo.getCreatorMis() == null && autoInspectConfigPo.getUpdaterMis() == null) {
            autoInspectConfigPo.setCreatorMis(UserUtils.getUser().getLogin());
            autoInspectConfigPo.setUpdaterMis(UserUtils.getUser().getLogin());
        } else {
            autoInspectConfigPo.setCreatorMis(autoInspectConfigPo.getUpdaterMis());
            autoInspectConfigPo.setUpdaterMis(autoInspectConfigPo.getUpdaterMis());
        }
        // 设置指标相关信息
        autoInspectConfigPo.setMetricId(autoInspectParam.getMetricId());
        autoInspectConfigPo.setMetricContent(autoInspectParam.getMetricContent());
        // 设置创建和修改时间
        if (autoInspectConfigPo.getGmtCreated() == null) {
            autoInspectConfigPo.setGmtModified(new Date());
        }
        autoInspectConfigPo.setGmtCreated(new Date());
        // 设置频率相关信息
        autoInspectConfigPo.setFrequencyDay(autoInspectParam.getFrequency().getDay());
        autoInspectConfigPo.setFrequencyType(autoInspectParam.getFrequency().getType());
        // 设置警报阈值和最大规模
        autoInspectConfigPo.setAlertThreshold(autoInspectParam.getAlertThreshold());
        autoInspectConfigPo.setMaxScale(autoInspectParam.getMaxScale());
        // 设置状态
        autoInspectConfigPo.setStatus(AutoInspectConfigStatusEnum.transStatus(autoInspectParam.getStatus()).getCode());
        // 设置执行模式
        autoInspectConfigPo.setMode(autoInspectParam.getMode());
    }


    @Override
    public void update(AutoInspectParam autoInspectParam) {
        // 调用责任链处理器
        autoInspectFilterChainContext.handler(AutoInspectChainMarkEnum.AUTO_INSPECT_UPDATE_FILTER.name(), autoInspectParam);
        // 查看更新后的版本是否存在配置相应配置,如果没有配置,则走新建逻辑
        AutoInspectConfigConditionParam autoInspectConfigConditionParam = new AutoInspectConfigConditionParam();
        autoInspectConfigConditionParam.setModelConfigVersionId(autoInspectParam.getAidaModelConfig().get(0).getModelConfigVersionId());
        List<AutoInspectConfigPo> configPos = autoInspectConfigGeneratorService.getByCondition(autoInspectConfigConditionParam);
        if (configPos.isEmpty()) {
            create(autoInspectParam);
            return;
        }
        AutoInspectConfigPo existingConfig = configPos.get(0);
        buildAutoInspectConfig(existingConfig, autoInspectParam);
        // 判断是否立即执行
        if (autoInspectParam.getMode() == AutoInspectExecutionModeEnum.IMMEDIATE.getCode()) {
            AutoInspectDetailPo autoInspectDetailPo = saveToAutoInspectDetail(existingConfig);
            submitImmediateTask(autoInspectDetailPo);
            return;
        }
        // 如果是定期巡检,并且配置为生效,则取消其他配置
        if (autoInspectParam.getStatus() == AutoInspectConfigStatusEnum.AVAILABLE.getCode() && autoInspectParam.getMode() == AutoInspectExecutionModeEnum.SCHEDULED.getCode()) {
            cancelOtherConfig(autoInspectParam.getAidaModelConfig().get(0).getApplicationId());
        }
        // 如果不是立即执行,则更新数据库中的配置
        autoInspectConfigMapper.updateById(existingConfig);
    }

    /**
     * 取消其他配置。
     * 此方法用于将指定应用ID的所有有效（状态为1）的自动巡检配置状态更新为无效（状态为0）。
     * 主要用于在更新或创建新的自动巡检配置时，确保同一应用下只有一个有效配置。
     *
     * @param applicationId 应用ID，用于标识需要取消配置的应用。
     */
    private void cancelOtherConfig(String applicationId) {
        // 创建查询条件对象
        AutoInspectConfigConditionParam autoInspectConfigConditionParam = new AutoInspectConfigConditionParam();
        autoInspectConfigConditionParam.setApplicationId(applicationId);
        // 找到巡检配置中,类型为定期巡检,状态为可用的配置
        autoInspectConfigConditionParam.setStatus(AutoInspectConfigStatusEnum.AVAILABLE.getCode());
        autoInspectConfigConditionParam.setMode(AutoInspectExecutionModeEnum.SCHEDULED.getCode());
        // 根据条件查询所有有效的自动巡检配置
        List<AutoInspectConfigPo> autoInspectConfigPos = autoInspectConfigGeneratorService.getByCondition(autoInspectConfigConditionParam);
        // 如果没有找到有效配置，直接返回
        if (CollectionUtils.isEmpty(autoInspectConfigPos)) {
            return;
        }
        // 遍历所有找到的有效配置
        autoInspectConfigPos.forEach(autoInspectConfigPo -> {
            // 将配置状态设置为无效
            autoInspectConfigPo.setStatus(AutoInspectConfigStatusEnum.UNAVAILABLE.getCode());
            // 更新配置的修改者为当前用户
            autoInspectConfigPo.setUpdaterMis(UserUtils.getUser().getLogin());
            // 更新配置的修改时间为当前时间
            autoInspectConfigPo.setGmtModified(new Date());
            // 更新数据库中的配置信息
            autoInspectConfigGeneratorService.updateById(autoInspectConfigPo);
        });
    }

    @Override
    public AutoInspectParam panelInfo(String applicationId, String workSpaceId, String modelConfigVersionId) {
        AutoInspectParam autoInspectParam = new AutoInspectParam();
        List<AutoInspectConfigPo> autoInspectConfigPos = autoInspectConfigGeneratorService.getCurrentValidConfig(applicationId, workSpaceId);
        if (CollectionUtils.isEmpty(autoInspectConfigPos)) {
            return autoInspectParam;
        }
        // 筛选出状态为AVAILABLE的配置
        Optional<AutoInspectConfigPo> availableConfig = autoInspectConfigPos.stream()
                .filter(autoInspectConfigPo -> autoInspectConfigPo.getStatus() == AutoInspectConfigStatusEnum.AVAILABLE.getCode())
                .findFirst();
        // 如果存在状态为AVAILABLE的配置，则使用该配置，否则使用列表中的第一个配置
        AutoInspectConfigPo selectedConfig = availableConfig.orElse(autoInspectConfigPos.get(0));
        return buildAutoInspectParamResult(autoInspectParam, selectedConfig);
    }

    private AutoInspectParam buildAutoInspectParamResult(AutoInspectParam autoInspectParam, AutoInspectConfigPo autoInspectConfigPo) {
        BeanUtils.copyProperties(autoInspectConfigPo, autoInspectParam);
        // 设定scoreThresholdList,frequency,aidaModelConfig
        AidaModelConfig aidaModelConfig = new AidaModelConfig();
        aidaModelConfig.setWorkspaceId(autoInspectConfigPo.getWorkSpaceId());
        aidaModelConfig.setWorkspaceName(autoInspectConfigPo.getWorkSpaceName());
        aidaModelConfig.setModelConfigVersionId(autoInspectConfigPo.getModelConfigVersionId());
        aidaModelConfig.setApplicationId(autoInspectConfigPo.getPlatformApp());
        aidaModelConfig.setApplicationName(autoInspectConfigPo.getModelConfigVersionName());
        aidaModelConfig.setModelConfigVersionName(autoInspectConfigPo.getModelConfigVersionName());
        autoInspectParam.setAidaModelConfig(Collections.singletonList(aidaModelConfig));
        autoInspectParam.setFrequency(new AutoInspectParam.Frequency());
        autoInspectParam.getFrequency().setDay(autoInspectConfigPo.getFrequencyDay());
        autoInspectParam.getFrequency().setType(autoInspectConfigPo.getFrequencyType());
        // 获取JSON字符串
        String jsonStr = autoInspectConfigPo.getScoreThresholdList();
        // 使用Fastjson将JSON字符串转换为Map<Integer, Object>
        Map<Integer, Object> scoreThresholdList = JSON.parseObject(jsonStr, new TypeReference<Map<Integer, Object>>() {});
        // 设置到autoInspectParam对象中
        autoInspectParam.setScoreThresholdList(scoreThresholdList);
        return autoInspectParam;
    }

    @Override
    public AutoInspectParam taskInfo(Long taskId) {
        // 根据taskId查询detail表
        AutoInspectDetailConditionParam autoInspectDetailConditionParam = new AutoInspectDetailConditionParam();
        autoInspectDetailConditionParam.setTaskId(taskId);
        List<AutoInspectDetailPo> autoInspectDetailPos = autoInspectDetailGeneratorService.getByCondition(autoInspectDetailConditionParam);
        if (CollectionUtils.isEmpty(autoInspectDetailPos)) {
            return null;
        }
        AutoInspectParam autoInspectParam = JSON.parseObject(autoInspectDetailPos.get(0).getExtra(), AutoInspectParam.class);
        if (autoInspectParam.getId() == null) {
            autoInspectParam.setId(0L);
        }
        return autoInspectParam;
    }

    private void autoInspectExecute(AutoInspectConfigPo autoInspectConfigPo, AutoInspectDetailPo autoInspectDetailPo) {
        // 获取分布式锁,锁后面再加,先不急
        boolean spaceLock = false;
        boolean autoInspectLock = false;
        Date startTime = new Date();
        try {
            // 如果绑定的数据集为空,则去创建并拉取数据集
            if (autoInspectDetailPo.getDatasetId() == null) {
                DataSetParam param = new DataSetParam();
                buildDatasetParam(param, autoInspectConfigPo);
                Date date = new Date();
                EvalDatasetPo evalDataset = buildDataset(param, date);
                evalDatasetGeneratorService.save(evalDataset);
                autoInspectDetailPo.setDatasetId(evalDataset.getId());
                // 拉取数据集
                PullDataThreadLocalHelper.setSource(PullDataParam.Source.INSPECT);
                autoInspectDetailPo.setStatus(AutoInspectEvalStatusEnum.PULL_DATA.getCode());
                autoInspectDetailGeneratorService.updateById(autoInspectDetailPo);
                DatasetPullResponse response = datasetPullService.pullData(param, evalDataset.getId());
                if (response.getStatus().getCode() != DatasetPullResponse.DatasetPullStatus.SUCCESS.getCode()) {
                    throw new RuntimeException("数据集拉取失败");
                }
                // 更新数据集
                autoInspectDetailGeneratorService.updateById(autoInspectDetailPo);
            } else {
                // 如果不为空，查看数据集的状态;
                EvalDatasetPo evalDataset = evalDatasetGeneratorService.getById(autoInspectDetailPo.getDatasetId());
                if (evalDataset.getStatus() != DatasetPullResponse.DatasetPullStatus.SUCCESS.getCode()) {
                    autoInspectDetailPo.setStatus(AutoInspectEvalStatusEnum.FAILED.getCode());
                    return;
                }
            }
            // 更新状态为排队中
            autoInspectDetailPo.setStatus(AutoInspectEvalStatusEnum.QUEUE.getCode());
            autoInspectDetailGeneratorService.updateById(autoInspectDetailPo);
            // 获取系统锁
            autoInspectLock = getLock(AutoInspectExecuteLockEnum.LOCK_SYSTEM.getType(), AutoInspectExecuteLockEnum.AUTO_INSPECT_EXECUTE_LOCK_SYSTEM.getType());
            if (!autoInspectLock) {
                return;
            }
            // 获取空间锁
            spaceLock = getLock(autoInspectConfigPo.getWorkSpaceId(), AutoInspectExecuteLockEnum.AUTO_INSPECT_EXECUTE_LOCK_USER_SPACE.getType());
            if (!spaceLock) {
                return;
            }
            // 获取详细数据
            List<EvalDatasetDetailPo> datasetDetailList = evalDatasetDetailGeneratorService.getByDatasetId(autoInspectDetailPo.getDatasetId());
            // 如果设定了metricContent,即是包含或者相似,那么设定预期结果
            for (EvalDatasetDetailPo detail : datasetDetailList) {
                JSONObject jsonObject = JSONObject.parseObject(detail.getContent());
                jsonObject.put("预期结果", autoInspectConfigPo.getMetricContent());
                detail.setContent(JSONObject.toJSONString(jsonObject));
            }
            JSONObject content = JSONObject.parseObject(datasetDetailList.get(0).getContent());
            TaskParam taskParam = buildTaskParam(autoInspectDetailPo.getDatasetId(), content, autoInspectConfigPo);
            datasetDetailList = taskExecuteServiceImpl.bindField(taskParam, datasetDetailList, autoInspectConfigPo.getCreatorMis());
            EvalTaskRequest evalRequest = new EvalTaskRequest();
            setBasicEvalTaskRequest(evalRequest, taskParam, autoInspectConfigPo);
            taskExecuteServiceImpl.buildInspectEvalTaskRequest(evalRequest, taskParam, datasetDetailList);
            InnerAppConfigDTO innerAppConfigDTO = aidaInvokeServiceProxy.getAidaRobotInfo(autoInspectConfigPo.getPlatformApp(), autoInspectConfigPo.getModelConfigVersionId());
            CommonEvalStrategyService.EvalIdInfo evalIdInfo = commonEvalStrategyService.logData2Db(evalRequest);
            evalRequest.setStartTime(startTime);
            // 修改巡检任务执行状态为执行中
            autoInspectDetailPo.setStatus(AutoInspectEvalStatusEnum.EVALUATING.getCode());
            autoInspectDetailPo.setTaskId(evalIdInfo.getEvalTaskId());
            autoInspectDetailGeneratorService.updateById(autoInspectDetailPo);
            evalRequest.setSessionList(commonEvalStrategyService.getSessionList(evalIdInfo.getEvalTaskId()));
            evalRequest.setSampleDataList(commonEvalStrategyService.getSampleDataList(evalIdInfo.getEvalTaskId()));
            evalServiceMap.get(transformAbilityName(innerAppConfigDTO.getAppType())).executeTask(evalRequest, evalIdInfo);
            // 判断任务是否超时，从主库取数据，避免主从延迟问题
            EvalTaskPo taskPo = getEvalTaskPoInMaster(evalIdInfo.getEvalTaskId());
            if (taskPo.getStatus() == AutoTaskStatusEnum.TIMEOUT.getCode()) {
                autoInspectDetailPo.setStatus(AutoInspectEvalStatusEnum.TIMEOUT.getCode());
                autoInspectDetailGeneratorService.updateById(autoInspectDetailPo);
                return;
            } else if (taskPo.getStatus() != AutoTaskStatusEnum.FINISHED.getCode()) {
                autoInspectDetailPo.setStatus(AutoInspectEvalStatusEnum.FAILED.getCode());
                autoInspectDetailGeneratorService.updateById(autoInspectDetailPo);
                return;
            }
            autoInspectDetailPo.setStatus(AutoInspectEvalStatusEnum.FINISHED.getCode());
            autoInspectDetailGeneratorService.updateById(autoInspectDetailPo);
            Long threshold = 0L;
            threshold = saveAutoEvalResult(autoInspectConfigPo, evalIdInfo.getEvalTaskId(), autoInspectDetailPo);
            // 根据阈值是否进行告警
            if (threshold < autoInspectConfigPo.getAlertThreshold()) {
                sendElephantMessage(threshold, autoInspectConfigPo);
            }
        } catch (Exception e) {
            AutoInspectDetailPo update = new AutoInspectDetailPo();
            update.setId(autoInspectDetailPo.getId());
            update.setStatus(AutoInspectEvalStatusEnum.FAILED.getCode());
            autoInspectDetailGeneratorService.updateById(update);
            log.error("【巡检任务执行失败】,参数 = {}",JSONObject.toJSONString(autoInspectConfigPo),e);
        } finally {
            // 执行完毕释放分布式锁
            if (autoInspectLock) {
                releaseLock(AutoInspectExecuteLockEnum.LOCK_SYSTEM.getType());
            }
            if (spaceLock) {
                releaseLock(autoInspectConfigPo.getWorkSpaceId());
            }
            PullDataThreadLocalHelper.remove();
        }
    }

    /**
     * 获取主库任务数据
     */
    private EvalTaskPo getEvalTaskPoInMaster(Long taskId) {
        ZebraForceMasterHelper.forceMasterInLocalContext();
        EvalTaskPo taskPo = evalTaskGeneratorService.getById(taskId);
        ZebraForceMasterHelper.clearLocalContext();
        return taskPo;
    }

    private void setBasicEvalTaskRequest(EvalTaskRequest evalRequest, TaskParam taskParam, AutoInspectConfigPo autoInspectConfigPo) {
        ApplicationConditionParam applicationConditionParam = new ApplicationConditionParam();
        applicationConditionParam.setRobotId(autoInspectConfigPo.getModelConfigVersionId());
        evalRequest.setIsInner(aidaInvokeServiceProxy.isInner(autoInspectConfigPo.getCreatorMis()));
        evalRequest.setCreatorMis(autoInspectConfigPo.getCreatorMis());
        evalRequest.setCreatorName(autoInspectConfigPo.getCreatorMis());
        evalRequest.setWorkspaceId(autoInspectConfigPo.getWorkSpaceId());
        evalRequest.setApplicationId(autoInspectConfigPo.getPlatformApp());
        evalRequest.setName(taskParam.getName());
        evalRequest.setAbility(AbilityEnum.parse(taskParam.getAbilityType()).getName());
        evalRequest.setCallType(taskParam.getCallType());
        evalRequest.setApplicationSource(taskParam.getModelSource());
        evalRequest.setTaskType(taskParam.getTaskType());
    }


    private TaskParam buildTaskParam(Long datasetId, JSONObject content, AutoInspectConfigPo autoInspectConfigPo) {
        TaskParam taskParam = new TaskParam();
        // 设定巡检任务名称
        taskParam.setName("巡检任务-" + DateUtil.getNow() + "-" + autoInspectConfigPo.getApplicationId());
        // 获取单轮会话还是多轮会话.通过AidaProxy获取机器人基本信息
        InnerAppConfigDTO innerAppConfigDTO = aidaInvokeServiceProxy.getAidaRobotInfo(autoInspectConfigPo.getPlatformApp(), autoInspectConfigPo.getModelConfigVersionId());
        taskParam.setAbilityType(transformAbilityType(innerAppConfigDTO.getAppType()));
        // 设定类型为定期巡检
        taskParam.setTaskType(TaskTypeEnum.INSPECT.getCode());
        taskParam.setDatasetIdList(Lists.newArrayList(datasetId));
        taskParam.setMetricList(Lists.newArrayList(new TaskMetricParam(null, autoInspectConfigPo.getMetricId().longValue())));
        JSONObject jsonObject = JSON.parseObject(autoInspectConfigPo.getScoreThresholdList());
        Map<Integer, Object> scoreThresholdMap = new HashMap<>();
        List<Long> metricIdList = Lists.newArrayList();
        jsonObject.forEach((key, value) -> {
            Long metricId = Long.valueOf(key);
            metricIdList.add(metricId);
        });
        List<MetricConfigPo> metricConfigPOList = metricConfigGeneratorService.getByIdList(metricIdList);
        Map<Long, MetricConfigPo> metricIdMap = metricConfigPOList
                .stream()
                .collect(Collectors.toMap(MetricConfigPo::getId, Function.identity(), (a, b) -> a));
        List<ScoreThresholdParam> scoreThresholdParams = Lists.newArrayList();
        jsonObject.forEach((key, value) -> {
            Long metricId = Long.valueOf(key);
            MetricConfigPo metricConfigPo = metricIdMap.get(metricId);
            ScoreThresholdParam scoreThresholdParam = new ScoreThresholdParam();
            if (null != metricConfigPo && metricConfigPo.getMetricType().equals(MetricTypeEnum.NUMBER.getCode())) {
                //数字类指标
                scoreThresholdParam.setScoreThreshold(Double.parseDouble(value.toString()));
            } else {
                //枚举类
                scoreThresholdParam.setScoreThresholdList(Lists.newArrayList(value.toString()));
            }
        });
        taskParam.setScoreThresholdList(scoreThresholdParams);
        // 模型获取输出方式 巡检为离线模式,
        taskParam.setCallType(CallTypeEnum.ONLINE.getCode());
        // 设定输入方式 0-数据集 1-人工模拟
        taskParam.setInputSource(TaskInputSourceEnum.DATASET.getCode());
        // 设定模型来源 0-评测系统 1-ai搭
        taskParam.setModelSource(TaskModelSourceEnum.AI.getCode());
        // 设定ai搭机器人配置
        AidaModelConfig aidaModelConfig = new AidaModelConfig();
        aidaModelConfig.setWorkspaceId(autoInspectConfigPo.getWorkSpaceId());
        aidaModelConfig.setWorkspaceName(autoInspectConfigPo.getWorkSpaceName());
        aidaModelConfig.setApplicationId(applicationConfigGeneratorService.getById(autoInspectConfigPo.getApplicationId()).getPlatformApp());
        aidaModelConfig.setApplicationName(autoInspectConfigPo.getModelConfigVersionName());
        aidaModelConfig.setModelConfigVersionId(autoInspectConfigPo.getModelConfigVersionId());
        aidaModelConfig.setModelConfigVersionName(autoInspectConfigPo.getModelConfigVersionName());
        taskParam.setAidaModelConfig(Lists.newArrayList(aidaModelConfig));
        // 设定历史来源为预期结果
        taskParam.setHistorySource(TaskHistorySourceEnum.EXPECT.getCode());
        DatasetApplicationParam datasetApplicationParam = new DatasetApplicationParam();
        datasetApplicationParam.setUploadType(UploadTypeEnum.CUSTOM_TEMPLATE_UPLOAD.getCode());
        datasetApplicationParam.setApplicationSource(ApplicationSourceEnum.VIRTUAL.getCode());
        datasetApplicationParam.setAidaModelConfigList(Lists.newArrayList(aidaModelConfig));
        datasetApplicationParam.setCreateMis(autoInspectConfigPo.getCreatorMis());
        ApplicationInfoDTO datasetApplicationInfo = applicationExecuteService.getDatasetApplicationInfo(datasetApplicationParam);
        List<ApplicationInfoDTO.Param> resultFieldList = new ArrayList<>();
        // 映射系统参数
        resultFieldList.addAll(datasetApplicationInfo.getSystemParamList());
        resultFieldList.addAll(datasetApplicationInfo.getApplicationParamList());
        Map<Long, List<TemplateFieldBindDTO>> bindFiledMap = new HashMap<>();
        bindFiledMap.put(datasetId, datasetMapApp(resultFieldList, content, autoInspectConfigPo));
        taskParam.setBindFields(bindFiledMap);
        taskParam.setResultParamList(null);
        return taskParam;
    }

    private void buildDatasetParam(DataSetParam param, AutoInspectConfigPo autoInspectConfigPo) throws ParseException {
        param.setName("巡检任务-" + DateUtil.getNow() + "-" + autoInspectConfigPo.getApplicationId());
        param.setCreationMethod(DatasetCreateionMethodEnum.PULL.getCode());
        ConditionConfig conditionConfig = new ConditionConfig();
        String endTime = DateUtil.getNow();
        String beginTime = DateUtil.addDays(endTime, "", -30);
        conditionConfig.setBeginTime(beginTime);
        conditionConfig.setEndTime(endTime);
        conditionConfig.setApplicationId(applicationConfigGeneratorService.getById(autoInspectConfigPo.getApplicationId()).getPlatformApp());
        conditionConfig.setWorkSpaceId(autoInspectConfigPo.getWorkSpaceId());
        conditionConfig.setMaxScale(autoInspectConfigPo.getMaxScale());
        param.setConditionConfig(conditionConfig);
        InnerAppConfigDTO innerAppConfigDTO = aidaInvokeServiceProxy.getAidaRobotInfo(autoInspectConfigPo.getPlatformApp(), autoInspectConfigPo.getModelConfigVersionId());
        param.setPlatformType(AbilityEnum.parse(innerAppConfigDTO.getAppType()).getCode());
        param.setCreatorMis(autoInspectConfigPo.getUpdaterMis());
        param.setSceneId(SceneTypeEnum.MODEL_LOG.getCode());
    }


    public List<AutoInspectDetailPo> convertToDetailPo(List<AutoInspectConfigPo> configPos) {
        List<AutoInspectDetailPo> detailPos = new ArrayList<>();
        configPos.forEach(configPo -> {
            AutoInspectDetailPo detailPo = new AutoInspectDetailPo();
            detailPo.setAutoInspectConfigId(configPo.getId());
            detailPo.setStatus(AutoInspectEvalStatusEnum.CREATING.getCode());
            detailPo.setGmtCreated(new Date());
            detailPo.setGmtModified(new Date());
            detailPo.setMetricId(Long.valueOf(configPo.getMetricId()));
            detailPo.setFrequencyDay(configPo.getFrequencyDay());
            detailPo.setFrequencyType(configPo.getFrequencyType());
            detailPo.setCreatorMis(configPo.getUpdaterMis());
            detailPo.setUpdaterMis(configPo.getUpdaterMis());
            detailPo.setModelConfigVersionId(configPo.getModelConfigVersionId());
            detailPo.setPlatformApp(configPo.getPlatformApp());
            AutoInspectParam param = new AutoInspectParam();
            buildAutoInspectParamResult(param, configPo);
            String extra = JSONObject.toJSONString(param);
            detailPo.setExtra(extra);
            detailPos.add(detailPo);
        });
        // 根据实际情况填充 detailPo 的属性
        return detailPos;
    }

    private EvalDatasetPo buildDataset(DataSetParam param, Date date) {
        EvalDatasetPo evalDatasetPo = new EvalDatasetPo();
        evalDatasetPo.setName(param.getName());
        evalDatasetPo.setPlatformType(PlatformTypeEnum.AI.getCode());
        evalDatasetPo.setPlatformWorkspace(param.getConditionConfig().getWorkSpaceId());
        evalDatasetPo.setScene(param.getSceneId().toString());
        evalDatasetPo.setGmtCreated(date);
        evalDatasetPo.setGmtModified(date);
        evalDatasetPo.setCreatorMis(param.getCreatorMis());
        evalDatasetPo.setUpdaterMis(param.getCreatorMis());
        evalDatasetPo.setCreateMethod(param.getCreationMethod());
        return evalDatasetPo;
    }

    public void releaseLock(String key) {
        try {
            long lockSize = redisStoreClient.get(new StoreKey(RedisConstants.EVAL_AUTO_INSPECT_TASK_EXECUTE_LOCK, key));
            if (lockSize > 0) {
                redisStoreClient.decrease(new StoreKey(RedisConstants.EVAL_AUTO_INSPECT_TASK_EXECUTE_LOCK, key), 1);
            } else {
                Cat.logEvent(CatConstants.AUTO_INSPECT_TASK_LOCK_EXCEPTION, key);
                log.warn("任务锁发生计数错误,mis={}", key);
            }
        } catch (Exception e) {
            Cat.logError(e);
            log.error("释放分布式锁失败,createMis={},msg={}", key, e.getMessage(), e);
        }
    }

    public boolean getLock(String key, String lockType) {
        try {
            Map<String, Integer> parallelMap = Lion.getMap(ConfigUtil.getAppkey(), LionConstants.EVAL_TASK_AUTO_INSPECT_MAP, Integer.class);
            int parallel = 1;
            if (parallelMap != null && parallelMap.containsKey(lockType)) {
                parallel = parallelMap.get(lockType);
            }
            long expireTime = Lion.getLong(ConfigUtil.getAppkey(), LionConstants.EVAL_RUNNING_CACHE_EXPIRE_TIME, 3153600000L);
            StoreBoundResponse storeBoundResponse = redisStoreClient.incrWithUpperBound(new StoreKey(RedisConstants.EVAL_AUTO_INSPECT_TASK_EXECUTE_LOCK, key), 1, parallel, 0, expireTime);
            return storeBoundResponse.isOperated();
        } catch (Exception e) {
            Cat.logError(e);
            log.error("获取分布式锁失败,createMis={},mssg={}", key, e.getMessage(), e);
        }
        return false;
    }

    @Override
    public Long transferTask(Long taskId) {
        EvalTaskPo evalTaskPo = evalTaskGeneratorService.getById(taskId);
        CommonUtils.checkEval(evalTaskPo != null, "任务不存在");
        Long datasetId = Long.valueOf(evalTaskPo.getDatasetIds());
        return transferDataset(taskId, datasetId);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long transferDataset(Long taskId, Long datasetId) {
        // 先拿到最初的数据集
        EvalDatasetPo originDataset = evalDatasetGeneratorService.getById(datasetId);
        // 先创立一个新数据集的信息
        EvalDatasetPo evalDatasetPo = new EvalDatasetPo();
        BeanUtils.copyProperties(originDataset, evalDatasetPo);
        evalDatasetPo.setId(null);
        evalDatasetPo.setGmtCreated(new Date());
        evalDatasetPo.setGmtModified(new Date());
        evalDatasetPo.setCreatorMis(UserUtils.getUser().getLogin());
        evalDatasetPo.setUpdaterMis(UserUtils.getUser().getLogin());
        evalDatasetPo.setName(originDataset.getName() + taskId);
        evalDatasetGeneratorService.save(evalDatasetPo);
        List<EvalDatasetDetailPo> transferDatasetDetails = new ArrayList<>();
        // 先拿eval_task_query表,里面存了content
        List<EvalTaskQueryPo> evalTaskQueryPos = evalTaskQueryGeneratorService.getByTaskId(taskId);
        // 再过滤掉质检过的
        List<EvalTaskQueryPo> filteredQueryPos = evalTaskQueryPos.stream()
                .filter(queryPo -> queryPo.getStatus().equals(TaskQueryStatusEnum.INSPECT_PASS.getCode())
                        || queryPo.getStatus().equals(TaskQueryStatusEnum.INSPECT_FAILED.getCode()))
                .collect(Collectors.toList());
        // 拿到质检过的queryDetail的ids，来查询queryDetail.
        // 拿到queryDetail的ids,根据ids拿到所有的评测详情
        List<Long> queryIds = filteredQueryPos.stream().map(EvalTaskQueryPo::getId).collect(Collectors.toList());
        List<EvalTaskQueryDetailPo> evalTaskQueryDetailPos = evalTaskQueryDetailGeneratorService.getByQueryIds(queryIds);
        // 拿到query的map,此处
        Map<Long, EvalTaskQueryPo> queryMap = filteredQueryPos.stream().collect(Collectors.toMap(EvalTaskQueryPo::getId, Function.identity()));
        // 获取到每条query对应的detail条数,即每条query对应的评测详情,可能有多条,因此设定为一个map。
        Map<Long, List<EvalTaskQueryDetailPo>> queryDetailMap = evalTaskQueryDetailPos.stream().collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getQueryId));
        // 拿到metricIdList,根据idList拿到metricConfig,获取指标的信息
        List<Long> metricIdList = evalTaskQueryDetailPos.stream().map(EvalTaskQueryDetailPo::getMetricId).map(Integer::longValue).distinct().collect(Collectors.toList());
        List<MetricConfigPo> metricConfigPos = metricConfigGeneratorService.getByIdList(metricIdList);
        Map<Long, MetricConfigPo> metricConfigMap = metricConfigPos.stream().collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));
        // 先遍历每个query,每个query构造一条数据集
        queryDetailMap.forEach((key, value) -> {
            EvalDatasetDetailPo detailPo = new EvalDatasetDetailPo();
            detailPo.setCreatorMis(UserUtils.getUser().getLogin());
            detailPo.setUpdaterMis(UserUtils.getUser().getLogin());
            detailPo.setGmtCreated(new Date());
            detailPo.setGmtModified(new Date());
            // 先将原来的query的content留存下来
            Map<String, Object> contentMap = new HashMap<>();
            String oldContent = queryMap.get(key).getContent();
            if (StringUtils.isNotBlank(oldContent)) {
                contentMap.putAll(JSONObject.parseObject(oldContent));
            }
            // 再遍历query下的每个detail,根据不同的指标添加对应的新列
            int i = 1;
            for (EvalTaskQueryDetailPo queryDetail : value) {
                String metricName = metricConfigMap.get(queryDetail.getMetricId().longValue()).getName();
                String metricExpect = "指标" + i + "-" + metricName + "-预期结果";
                if (queryDetail.getStatus() == AutoTaskEvalStatusEnum.INSPECT_PASS.getCode()) {
                    contentMap.put(metricExpect, queryDetail.getMetricResult());
                } else if (queryDetail.getStatus() == AutoTaskEvalStatusEnum.INSPECT_FAILED.getCode()) {
                    contentMap.put(metricExpect, queryDetail.getAnswer());
                }
                i++;
            }
            detailPo.setContent(JSONObject.toJSONString(contentMap));
            detailPo.setTurn(queryMap.get(key).getTurn());
            detailPo.setStatus(DatasetDetailStatusEnum.VALID.getCode());
            detailPo.setDatasetId(evalDatasetPo.getId());
            transferDatasetDetails.add(detailPo);
        });
        evalDatasetDetailGeneratorService.saveBatch(transferDatasetDetails);
        return evalDatasetPo.getId();
    }

    @Override
    public List<AidaModelConfig> robotsInfo(String applicationId) {
        Date endDate = new Date();
        Date startDate = DateUtil.getDateZero(DateUtil.addDays(endDate, -31));
        AutoInspectDetailConditionParam autoInspectDetailConditionParam = new AutoInspectDetailConditionParam();
        autoInspectDetailConditionParam.setStartTime(startDate);
        autoInspectDetailConditionParam.setEndTime(endDate);
        autoInspectDetailConditionParam.setPlatformApp(applicationId);
        List<AutoInspectDetailPo> autoInspectDetailPos = autoInspectDetailGeneratorService.getByCondition(autoInspectDetailConditionParam);
        List<Long> configIds = autoInspectDetailPos.stream().map(AutoInspectDetailPo::getAutoInspectConfigId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(configIds)) {
            return new ArrayList<>();
        }
        List<AutoInspectConfigPo> autoInspectConfigPos = autoInspectConfigGeneratorService.getByIdList(configIds);
        List<AidaModelConfig> aidaModelConfigs = new ArrayList<>();
        autoInspectConfigPos.forEach(autoInspect -> {
            AidaModelConfig aidaModelConfig = new AidaModelConfig();
            aidaModelConfig.setWorkspaceId(autoInspect.getWorkSpaceId());
            aidaModelConfig.setWorkspaceName(autoInspect.getWorkSpaceName());
            aidaModelConfig.setApplicationId(autoInspect.getPlatformApp());
            aidaModelConfig.setModelConfigVersionName(autoInspect.getModelConfigVersionName());
            aidaModelConfig.setModelConfigVersionId(autoInspect.getModelConfigVersionId());
            aidaModelConfigs.add(aidaModelConfig);
        });
        List<AidaModelConfig> distinctAidaModelConfigs = aidaModelConfigs.stream()
                .collect(Collectors.toMap(
                        AidaModelConfig::getModelConfigVersionId,
                        modelConfig -> modelConfig,
                        (existing, replacement) -> existing)) // 在键冲突时保留现有元素
                .values() // 获取Map的值集合
                .stream() // 将值集合转换为Stream
                .collect(Collectors.toList());
        return distinctAidaModelConfigs;
    }

    @Override
    public List<AutoInspectMetricDTO> metricsInfo(String applicationId) {
        // 先去拉31天的数据,根据appId
        Date endDate = new Date();
        Date startDate = DateUtil.getDateZero(DateUtil.addDays(endDate, -31));
        AutoInspectDetailConditionParam autoInspectDetailConditionParam = new AutoInspectDetailConditionParam();
        autoInspectDetailConditionParam.setStartTime(startDate);
        autoInspectDetailConditionParam.setEndTime(endDate);
        autoInspectDetailConditionParam.setPlatformApp(applicationId);
        List<AutoInspectDetailPo> autoInspectDetailPos = autoInspectDetailGeneratorService
                .getByCondition(autoInspectDetailConditionParam)
                .stream()
                .filter(detail -> detail.getAutoInspectConfigId() != null)
                .collect(Collectors.toList());
        // 然后将这些数据中的metricId提出来,对应查表即可
        List<Long> metricIds = autoInspectDetailPos.stream().map(AutoInspectDetailPo::getMetricId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(metricIds)){
            return new ArrayList<>();
        }
        List<Long> uniqueMetricIds = metricIds.stream()
                .distinct()
                .collect(Collectors.toList());
        List<MetricConfigPo> metricConfigPoList = metricConfigGeneratorService.getByIdList(uniqueMetricIds);
        List<AutoInspectMetricDTO> autoInspectMetricDTOS = new ArrayList<>();
        metricConfigPoList.forEach(metricConfigPo -> {
            AutoInspectMetricDTO autoInspectMetricDTO = new AutoInspectMetricDTO();
            autoInspectMetricDTO.setId(metricConfigPo.getId());
            autoInspectMetricDTO.setName(metricConfigPo.getName());
            autoInspectMetricDTOS.add(autoInspectMetricDTO);
        });
        return autoInspectMetricDTOS;
    }

    @Override
    public PageData<AutoInspectPanelPageDTO> pageAutoInspectPanel(String applicationId, String modelConfigVersionId, Integer metricId, Date startTime, Date endTime) {
        checkSpan(startTime, endTime);
        List<AutoInspectPanelPageDTO> autoInspectPanelPageDTOS = new ArrayList<>();
        // 设定参数
        AutoInspectDetailConditionParam autoInspectDetailConditionParam = new AutoInspectDetailConditionParam();
        autoInspectDetailConditionParam.setPlatformApp(applicationId);
        Optional.ofNullable(metricId).ifPresent(value -> autoInspectDetailConditionParam.setMetricId(Long.valueOf(metricId)));
        if (modelConfigVersionId != null && !modelConfigVersionId.trim().isEmpty()) {
            autoInspectDetailConditionParam.setModelConfigVersionId(modelConfigVersionId.trim());
        }
        // 计算时间
        LocalDate startDate = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDate = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        long daysBetween = Math.max(1, java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate));
        LocalDate actualStartDate = startDate.minusDays(daysBetween);
        Date actualStartTime = Date.from(actualStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        autoInspectDetailConditionParam.setStartTime(actualStartTime);
        autoInspectDetailConditionParam.setEndTime(endTime);
        autoInspectDetailConditionParam.setStatus(AutoInspectEvalStatusEnum.FINISHED.getCode());
        List<AutoInspectDetailPo> detailPos = autoInspectDetailGeneratorService.getByCondition(autoInspectDetailConditionParam);
        if (detailPos.isEmpty()) {
            return getEmptyPanelPageData(autoInspectPanelPageDTOS);
        }
        // 拆分成两份,一份时间startTime之前的,一份startTime之后的
        List<AutoInspectDetailPo> beforeStartTimeList = detailPos.stream()
                .filter(po -> po.getGmtCreated().before(startTime))
                .collect(Collectors.toList());
        // 使用startTime及之后的日期过滤列表
        List<AutoInspectDetailPo> onOrAfterStartTimeList = detailPos.stream()
                .filter(po -> po.getGmtCreated().after(startTime))
                .collect(Collectors.toList());
        AutoInspectPanelDimensionDTO beforeDto = getPanelDimensionSum(beforeStartTimeList);
        AutoInspectPanelDimensionDTO afterDto = getPanelDimensionSum(onOrAfterStartTimeList);
        // 总巡检会话数
        autoInspectPanelPageDTOS.add(getPanelDimensionResult(beforeDto.getTotalSessionCount(), afterDto.getTotalSessionCount(), AutoInspectPanelEnum.TOTAL_SESSION_COUNT.getType(), AutoInspectPanelEnum.TOTAL_SESSION_COUNT.getDescription()));
        // 成功巡检会话数
        autoInspectPanelPageDTOS.add(getPanelDimensionResult(beforeDto.getSuccessSessionCount(), afterDto.getSuccessSessionCount(), AutoInspectPanelEnum.COMPLETE_SESSION_COUNT.getType(), AutoInspectPanelEnum.COMPLETE_SESSION_COUNT.getDescription()));
        // 命中会话数
        autoInspectPanelPageDTOS.add(getPanelDimensionResult(beforeDto.getHitCount(), afterDto.getHitCount(), AutoInspectPanelEnum.HIT_SESSION_COUNT.getType(), AutoInspectPanelEnum.HIT_SESSION_COUNT.getDescription()));
        // 命中率单独构造
        autoInspectPanelPageDTOS.add(getHitRatio(beforeDto, afterDto));
        return PageData.create(autoInspectPanelPageDTOS.size(), 1, 10, autoInspectPanelPageDTOS);
    }

    private AutoInspectPanelPageDTO getHitRatio(AutoInspectPanelDimensionDTO beforeDto, AutoInspectPanelDimensionDTO afterDto) {
        AutoInspectPanelPageDTO hitRate = new AutoInspectPanelPageDTO();
        BigDecimal beforeHitRatio = BigDecimal.valueOf(0);
        if (beforeDto != null && beforeDto.getSuccessSessionCount() != null && beforeDto.getHitCount() != null) {
            beforeHitRatio = BigDecimal.valueOf(beforeDto.getSuccessSessionCount() == 0 ? 0 :
                    ((double) beforeDto.getHitCount() / (double) beforeDto.getSuccessSessionCount()));
        }
        double afterHitRatio = 0;
        if (afterDto != null && afterDto.getSuccessSessionCount() != null && afterDto.getHitCount() != null) {
            afterHitRatio = afterDto.getSuccessSessionCount() == 0 ? 0 :
                    ((double) afterDto.getHitCount() / (double) afterDto.getSuccessSessionCount());
        }
        double hitRateChange = 0;
        if (beforeHitRatio.equals(new BigDecimal(0))) {
            hitRateChange = afterHitRatio - beforeHitRatio.doubleValue();
        }
        // 使用BigDecimal进行精确的小数位数控制
        BigDecimal hitRateChangeBD = BigDecimal.valueOf(hitRateChange)
                .setScale(2, RoundingMode.HALF_UP);
        hitRate.setName(AutoInspectPanelEnum.HIT_RATIO.getType());
        hitRate.setDescription(AutoInspectPanelEnum.HIT_RATIO.getDescription());
        // 将afterHitRatio转换为长整型，如果需要保留小数点后两位，可以考虑改变数据类型或者使用其他方式展示
        hitRate.setCount((long) (afterHitRatio * 100)); // 注意：这里的处理方式取决于你希望如何展示“命中率”的值
        // 设置变化率，已调整为0到1之间的值，并保留两位小数
        hitRate.setRate(hitRateChangeBD.floatValue());
        return hitRate;
    }

    private void checkSpan(Date startTime, Date endTime) {
        Date endDate = DateUtil.getDateZero(endTime);
        Date startDate = DateUtil.getDateZero(startTime);
        Date startDateAfterMonth = DateUtil.addDays(startDate, 31);
        CommonUtils.checkEval(endDate.before(startDateAfterMonth), "时间跨度不能超过30天");
    }

    private PageData<AutoInspectPanelPageDTO> getEmptyPanelPageData(List<AutoInspectPanelPageDTO> autoInspectPanelPageDTOS) {
        autoInspectPanelPageDTOS.add(getPanelDimensionResult(0L, 0L, AutoInspectPanelEnum.TOTAL_SESSION_COUNT.getType(), AutoInspectPanelEnum.TOTAL_SESSION_COUNT.getDescription()));
        autoInspectPanelPageDTOS.add(getPanelDimensionResult(0L, 0L, AutoInspectPanelEnum.COMPLETE_SESSION_COUNT.getType(), AutoInspectPanelEnum.COMPLETE_SESSION_COUNT.getDescription()));
        autoInspectPanelPageDTOS.add(getPanelDimensionResult(0L, 0L, AutoInspectPanelEnum.HIT_SESSION_COUNT.getType(), AutoInspectPanelEnum.HIT_SESSION_COUNT.getDescription()));
        autoInspectPanelPageDTOS.add(getPanelDimensionResult(0L, 0L, AutoInspectPanelEnum.HIT_RATIO.getType(), AutoInspectPanelEnum.HIT_RATIO.getDescription()));
        return PageData.create(autoInspectPanelPageDTOS.size(), 1, 10, autoInspectPanelPageDTOS);
    }

    /**
     * 获取面板维度结果
     *
     * @param beforeCount 在startTime之前的count计数
     * @param afterCount  在startTime之前的count计数
     * @param type        当前要展示的指标的名称
     * @param description 当前要展示的指标的注释文案
     * @return
     */
    private AutoInspectPanelPageDTO getPanelDimensionResult(Long beforeCount, Long afterCount, String type, String description) {
        AutoInspectPanelPageDTO autoInspectPanelPageDTO = new AutoInspectPanelPageDTO();
        autoInspectPanelPageDTO.setName(type);
        autoInspectPanelPageDTO.setCount(afterCount);

        // 使用BigDecimal进行精确计算
        BigDecimal rate;
        if (beforeCount == 0) {
            rate = BigDecimal.ZERO;
        } else {
            BigDecimal beforeCountBD = BigDecimal.valueOf(beforeCount);
            BigDecimal afterCountBD = BigDecimal.valueOf(afterCount);
            rate = afterCountBD.subtract(beforeCountBD)
                    .divide(beforeCountBD, 2, BigDecimal.ROUND_HALF_UP);// 先计算出精确的比例，保留4位小数
        }

        // 将BigDecimal转换为float并设置到DTO中
        autoInspectPanelPageDTO.setRate(rate.floatValue());
        autoInspectPanelPageDTO.setDescription(description);
        return autoInspectPanelPageDTO;
    }

    private AutoInspectPanelDimensionDTO getPanelDimensionSum(List<AutoInspectDetailPo> detailList) {
        AutoInspectPanelDimensionDTO dto = new AutoInspectPanelDimensionDTO();
        // 确保detailList不为null
        if (detailList != null) {
            detailList.forEach(detail -> {
                // 使用Optional.ofNullable().orElse(0L)来确保不会因为null值而抛出NullPointerException
                long totalSessionCount = Optional.ofNullable(detail.getTotalSessionCount()).orElse(0L);
                long successSessionCount = Optional.ofNullable(detail.getSuccessSessionCount()).orElse(0L);
                long hitCount = Optional.ofNullable(detail.getHitCount()).orElse(0L);
                dto.setTotalSessionCount(dto.getTotalSessionCount() + totalSessionCount);
                dto.setSuccessSessionCount(dto.getSuccessSessionCount() + successSessionCount);
                dto.setHitCount(dto.getHitCount() + hitCount);
            });
        }
        return dto;
    }

    public Long saveAutoEvalResult(AutoInspectConfigPo autoInspectConfigPo, Long taskId, AutoInspectDetailPo autoInspectDetailPo) {
        // 根据taskId拿数据集详情
        List<EvalTaskQueryDetailPo> evalTaskQueryDetailPoList = evalTaskQueryDetailGeneratorService.getByTaskId(taskId);
        autoInspectDetailPo.setTotalSessionCount((long) evalTaskQueryDetailPoList.size());
        Long hitCount = calcHitCount(autoInspectConfigPo, evalTaskQueryDetailPoList);
        long completeCount = evalTaskQueryDetailPoList.stream()
                .filter(detail -> detail.getStatus() == AutoTaskEvalStatusEnum.EVALUATED.getCode())
                .count();
        autoInspectDetailPo.setSuccessSessionCount(completeCount);
        autoInspectDetailPo.setHitCount(hitCount);
        autoInspectDetailPo.setUpdaterMis(autoInspectConfigPo.getUpdaterMis());
        autoInspectDetailPo.setGmtModified(new Date());
        double percentage = (double) hitCount / autoInspectDetailPo.getTotalSessionCount() * 100;
        autoInspectDetailPo.setScore((long) percentage);
        autoInspectDetailGeneratorService.updateById(autoInspectDetailPo);
        return (long) Math.round(percentage);
    }

    private Long calcHitCount(AutoInspectConfigPo autoInspectConfigPo, List<EvalTaskQueryDetailPo> evalTaskQueryDetailPoList) {
        // 先根据结果类型进行判定,如果是字符串,代表是自动指标,其他均为普通数字
        // 先获取指标类型
        Integer metricId = autoInspectConfigPo.getMetricId();
        // 先判定自动生成指标
        if (metricId == EvalMetricTypeEnum.AUTO.getCode()) {
            return autoGenerateMetricHitCount(evalTaskQueryDetailPoList);
        }
        Integer metricType = metricConfigGeneratorService.getById(metricId).getMetricType();
        // 判定为分数型指标
        if (metricType == MetricTypeEnum.NUMBER.getCode()) {
            return numberMetricHitCount(evalTaskQueryDetailPoList, autoInspectConfigPo);
        }
        // 判定为枚举型指标
        if (metricType == MetricTypeEnum.ENUMERATION.getCode()) {
            return enumerationMetricHitCount(evalTaskQueryDetailPoList, autoInspectConfigPo);
        }
        return 0L;
    }

    private Long enumerationMetricHitCount(List<EvalTaskQueryDetailPo> evalTaskQueryDetailPoList, AutoInspectConfigPo autoInspectConfigPo) {
        AtomicLong hitCount = new AtomicLong(0);
        String scoreThreshold = autoInspectConfigPo.getScoreThresholdList();
        Map<Integer, List<String>> map = JSON.parseObject(scoreThreshold, new TypeReference<Map<Integer, List<String>>>() {
        });
        List<String> hitList = map.get(autoInspectConfigPo.getMetricId());
        evalTaskQueryDetailPoList.forEach(detail -> {
            String metricResult = detail.getMetricResult();
            if (hitList.contains(metricResult)) {
                hitCount.getAndIncrement();
            }
        });
        return hitCount.get();
    }

    private Long numberMetricHitCount(List<EvalTaskQueryDetailPo> evalTaskQueryDetailPoList, AutoInspectConfigPo autoInspectConfigPo) {
        AtomicLong hitCount = new AtomicLong(0);
        String scoreThreshold = autoInspectConfigPo.getScoreThresholdList();
        Map<Integer, Double> scoreThresholdList = JSON.parseObject(scoreThreshold, new TypeReference<Map<Integer, Double>>() {
        });
        // 转double可能丢精度,先这样吧。
        Double threshold = Optional.ofNullable(scoreThresholdList.get(autoInspectConfigPo.getMetricId()))
                .orElse(0.0);
        evalTaskQueryDetailPoList.forEach(detail -> {
            try {
                // 尝试将字符串转换为double类型
                double metricResult = Double.parseDouble(detail.getMetricResult());
                if (metricResult > threshold) {
                    hitCount.getAndIncrement();
                }
            } catch (NumberFormatException e) {
                // 如果转换失败（即字符串不是有效的double表示），可以选择记录日志或者其他处理方式
                log.error("无法将metricResult转换为数字: " + detail.getMetricResult());
            }
        });
        return hitCount.get();
    }

    private Long autoGenerateMetricHitCount(List<EvalTaskQueryDetailPo> evalTaskQueryDetailPoList) {
        AtomicLong hitCount = new AtomicLong(0);
        Pattern pattern = Pattern.compile("\\{.*\\}", Pattern.DOTALL);
        evalTaskQueryDetailPoList.forEach(detail -> {
            try {
                String metricResult = detail.getMetricResult();
                Matcher matcher = pattern.matcher(metricResult);
                if (matcher.matches()) {
                    // 处理 JSON 字符串
                    JSONObject jsonObject = JSONObject.parseObject(metricResult);
                    String result = jsonObject.getString("result");
                    if ("通过".equals(result)) {
                        hitCount.getAndIncrement();
                    }
                }
            } catch (Exception e) {
                // 在循环内部捕获异常，以便处理单个条目的解析错误，而不影响其他条目的处理
                log.error("自动指标解析结果字符串JSON出错，详情ID: " + detail.getId(), e);
            }
        });
        return hitCount.get();
    }

    private List<TemplateFieldBindDTO> datasetMapApp(List<ApplicationInfoDTO.Param> resultFieldList, JSONObject jsonObject, AutoInspectConfigPo autoInspectConfigPo) {
        List<TemplateFieldBindDTO> templateFieldBindDTOS = new ArrayList<>();
        resultFieldList.forEach(field -> {
            TemplateFieldBindDTO templateFieldBindDTO = new TemplateFieldBindDTO();
            templateFieldBindDTO.setFieldCode(field.getFieldCode());
            templateFieldBindDTO.setFieldKey(field.getFieldKey());
            templateFieldBindDTO.setFieldName(field.getFieldName());
            TemplateFieldEnum templateFieldEnum = TemplateFieldEnum.parse(field.getFieldCode());
            switch (templateFieldEnum) {
                // 巡检的时候没有预期结果,但是绑定字段时创建评测任务的时候必须有预期结果,因此这里我直接放模型回复？如果是自动生成指标，不需要预期回复，如果是其他指标，会通过metricContent替换预期回复的内容.
                case EXPECT:
                    if (autoInspectConfigPo.getMetricId() == EvalMetricTypeEnum.SIM.getCode() || autoInspectConfigPo.getMetricId() == EvalMetricTypeEnum.CONTAIN.getCode()) {
                        processField(templateFieldBindDTO, jsonObject, "预期结果");
                    } else {
                        processField(templateFieldBindDTO, jsonObject, "输出");
                    }
                    break; // 防止执行下一个case
                case REPLY:
                    processField(templateFieldBindDTO, jsonObject, "输出");
                    break; // 防止执行下一个case
                case INPUT:
                    processField(templateFieldBindDTO, jsonObject, "输入内容");
                    break; // 防止执行下一个case
                case SESSION_ID:
                    processField(templateFieldBindDTO, jsonObject, "会话ID");
                    break;
                case PARAMS:
                    processField(templateFieldBindDTO, jsonObject, templateFieldBindDTO.getFieldKey());
                    break;
            }
            templateFieldBindDTOS.add(templateFieldBindDTO);
        });
        return templateFieldBindDTOS;
    }

    @Override
    public List<MetricDTO> getAvailableMetricList(String applicationId) {
        Integer abilityType = transformAbilityType(aidaInvokeServiceProxy.getAidaRobotInfo(applicationId, null).getAppType());
        List<MetricDTO> metricDTOS = metricExecuteService.getAvailableMetricList(abilityType).getAutoMetricList();
        return metricDTOS.stream()
                .filter(metricDTO -> metricDTO.getMetricId() != EvalMetricTypeEnum.EQUAL.getCode()) // 过滤掉全等的
                .filter(metricDTO -> metricDTO.getMetricId() != EvalMetricTypeEnum.AUTO.getCode())
                .peek(metricDTO -> {
                    // 如果是包含匹配，则isThreshold为false，否则根据startScore或outputEnum是否为空来设定isThreshold
                    if (metricDTO.getMetricId() == EvalMetricTypeEnum.CONTAIN.getCode()) {
                        metricDTO.setIsThreshold(false);
                    } else {
                        metricDTO.setIsThreshold(metricDTO.getStartScore() != null || metricDTO.getOutputEnum() != null);
                    }
                })
                .collect(Collectors.toList());
    }

    private Integer transformAbilityType(Integer appType) {
        // 这里因为在AppType中 2 是单轮会话,但是在巡检系统中 2是 规则型应用
        return appType == AbilityEnum.RULE.getCode() ? AbilityEnum.SINGLE_ROUND.getCode() : MULTI_ROUND.getCode();
    }

    private String transformAbilityName(Integer appType) {
        // 这里因为在AppType中 2 是单轮会话,但是在巡检系统中 2是 规则型应用
        return appType == AbilityEnum.RULE.getCode() ? AbilityEnum.SINGLE_ROUND.getName() : MULTI_ROUND.getName();
    }

    public void processField(TemplateFieldBindDTO templateFieldBindDTO, JSONObject jsonObject, String key) {
        if (jsonObject.containsKey(key)) {
            templateFieldBindDTO.setColumnName(key);
        }
    }

    private void sendElephantMessage(Long alertThreshold, AutoInspectConfigPo autoInspectConfigPo) {
        try {
            String message = String.format("巡检告警：%s 应用命中率低于设定阈值:%s , 当前命中率:%s", autoInspectConfigPo.getModelConfigVersionName(), autoInspectConfigPo.getAlertThreshold(), alertThreshold);
            dxPushTextService.pushTextByMisName(message, autoInspectConfigPo.getCreatorMis());
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
            log.error("sendStatusMsg pushTextByMisName error, e:", e);
        }
    }

}
