package com.meituan.csc.aigc.eval.service.impl;

import com.meituan.csc.aigc.eval.dao.entity.WorkbenchMessageReviewRecordPo;
import com.meituan.csc.aigc.eval.dao.service.generator.WorkbenchMessageReviewRecordGeneratorService;
import com.meituan.csc.aigc.eval.dto.workbench.WorkbenchReviewDTO;
import com.meituan.csc.aigc.eval.exception.EvalException;
import com.meituan.csc.aigc.eval.param.workbench.WorkbenchReviewParam;
import com.meituan.csc.aigc.eval.service.InspectWorkbenchReviewService;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会话分析评价服务实现类
 *
 * <AUTHOR>
 * @date 2025/5/27
 */
@Service
@Slf4j
public class InspectWorkbenchReviewServiceImpl implements InspectWorkbenchReviewService {

    @Autowired
    private WorkbenchMessageReviewRecordGeneratorService workbenchMessageReviewRecordGeneratorService;


    @Override
    public Long addReview(WorkbenchReviewParam param) {
        // 参数校验
        if (StringUtils.isBlank(param.getSessionId()) || StringUtils.isBlank(param.getMessageId()) || StringUtils.isBlank(param.getReviewContent())) {
            throw new EvalException("缺少评价参数");
        }

        // 获取当前用户信息
        User user = UserUtils.getUser();
        String reviewMis = user.getLogin();
        String reviewName = user.getName();

        // 构建评价记录
        WorkbenchMessageReviewRecordPo reviewRecord = new WorkbenchMessageReviewRecordPo();
        reviewRecord.setSessionId(param.getSessionId());
        reviewRecord.setMessageId(param.getMessageId());
        reviewRecord.setReviewContent(param.getReviewContent());
        reviewRecord.setReviewMis(reviewMis);
        reviewRecord.setReviewName(reviewName);
        reviewRecord.setLikeNumber(0);
        reviewRecord.setAddTime(new Date());
        reviewRecord.setUpdateTime(new Date());
        reviewRecord.setIsDeleted(false);

        // 保存评价记录
        boolean res = workbenchMessageReviewRecordGeneratorService.save(reviewRecord);
        if (!res) {
            log.error("添加评价失败, sessionId: {}, messageId: {}, reviewMis: {}", param.getSessionId(), param.getMessageId(), reviewMis);
        }
        return reviewRecord.getId();

    }

    @Override
    public List<WorkbenchReviewDTO> listReviews(String sessionId) {
        // 参数校验
        if (StringUtils.isBlank(sessionId)) {
            throw new EvalException("会话ID不能为空");
        }

        // 查询评价记录
        List<WorkbenchMessageReviewRecordPo> reviewRecords = workbenchMessageReviewRecordGeneratorService.listBySessionId(sessionId);

        // 转换为DTO
        return reviewRecords.stream().map(record -> {
            WorkbenchReviewDTO dto = new WorkbenchReviewDTO();
            dto.setId(record.getId());
            dto.setSessionId(record.getSessionId());
            dto.setMessageId(record.getMessageId());
            dto.setReviewContent(record.getReviewContent());
            dto.setReviewMis(record.getReviewMis());
            dto.setReviewName(record.getReviewName());
            dto.setReviewDate(record.getAddTime());
            dto.setLikeNumber(record.getLikeNumber());
            return dto;
        }).collect(Collectors.toList());
    }
}
