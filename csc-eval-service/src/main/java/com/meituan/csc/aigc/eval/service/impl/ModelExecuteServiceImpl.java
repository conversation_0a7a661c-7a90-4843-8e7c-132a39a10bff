package com.meituan.csc.aigc.eval.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.dto.model.ModelDTO;
import com.meituan.csc.aigc.eval.param.model.ModelConfigParam;
import com.meituan.csc.aigc.eval.service.ModelExecuteService;
import com.meituan.csc.aigc.eval.utils.GptUtils;
import com.meituan.inf.xmdlog.ConfigUtil;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ModelExecuteServiceImpl implements ModelExecuteService {
    @Override
    public List<ModelDTO> listModel() {
        return Lion.getList(ConfigUtil.getAppkey(), LionConstants.MODEL_CONFIG_LIST, ModelDTO.class);
    }
}
