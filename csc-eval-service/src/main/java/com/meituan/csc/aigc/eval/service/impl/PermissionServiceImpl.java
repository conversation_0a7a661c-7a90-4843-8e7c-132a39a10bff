package com.meituan.csc.aigc.eval.service.impl;

import com.dianping.lion.client.Lion;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.exception.EvalException;
import com.meituan.csc.aigc.eval.service.PermissionService;
import com.meituan.csc.aigc.eval.utils.CommonUtils;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PermissionServiceImpl implements PermissionService {
    public void checkPermission(String createMis, String errorMessage) throws EvalException {
        checkPermission(null, createMis, errorMessage);
    }

    @Override
    public void checkPermission(List<String> adminGroup, String createMis, String errorMessage) throws EvalException {
        User user = UserUtils.getUser();
        // 管理员能够删除所有的数据集
        List<String> adminMisList = Lion.getList(ConfigUtil.getAppkey(), LionConstants.EVAL_ADMIN_KEY, String.class);
        if (CollectionUtils.isEmpty(adminMisList) || !adminMisList.contains(user.getLogin())) {
            List<String> misList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(adminGroup)) {
                misList.addAll(adminGroup);
            } else {
                misList.add(createMis);
            }
            CommonUtils.checkEval(misList.contains(user.getLogin()), errorMessage);
        }
    }
}
