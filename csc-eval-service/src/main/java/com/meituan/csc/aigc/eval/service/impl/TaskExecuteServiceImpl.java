package com.meituan.csc.aigc.eval.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.google.common.collect.Maps;
import com.meituan.csc.aigc.eval.aop.workspace.WorkspaceContextHolder;
import com.meituan.csc.aigc.eval.config.http.HttpConfig;
import com.meituan.csc.aigc.eval.constants.CommonConstants;
import com.meituan.csc.aigc.eval.constants.EvalConstants;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.constants.RedisConstants;
import com.meituan.csc.aigc.eval.dao.entity.*;
import com.meituan.csc.aigc.eval.dao.service.generator.*;
import com.meituan.csc.aigc.eval.dto.PageData;
import com.meituan.csc.aigc.eval.dto.application.ApplicationDTO;
import com.meituan.csc.aigc.eval.dto.application.ApplicationInfoDTO;
import com.meituan.csc.aigc.eval.dto.application.ApplicationOutputDTO;
import com.meituan.csc.aigc.eval.dto.common.TableDataDTO;
import com.meituan.csc.aigc.eval.dto.dataset.BaseTemplateFieldBindDTO;
import com.meituan.csc.aigc.eval.dto.dataset.DatasetDTO;
import com.meituan.csc.aigc.eval.dto.dataset.SingleTemplateFieldBindDTO;
import com.meituan.csc.aigc.eval.dto.dataset.TemplateFieldBindDTO;
import com.meituan.csc.aigc.eval.dto.mark.RobotMockInfoDTO;
import com.meituan.csc.aigc.eval.dto.metric.MetricReplyDTO;
import com.meituan.csc.aigc.eval.dto.metric.TaskMetricInfoDTO;
import com.meituan.csc.aigc.eval.dto.model.ModelDTO;
import com.meituan.csc.aigc.eval.dto.task.*;
import com.meituan.csc.aigc.eval.enums.*;
import com.meituan.csc.aigc.eval.enums.dataset.DataTypeEnum;
import com.meituan.csc.aigc.eval.enums.task.TaskHistorySourceEnum;
import com.meituan.csc.aigc.eval.exception.CheckException;
import com.meituan.csc.aigc.eval.exception.EvalException;
import com.meituan.csc.aigc.eval.param.ConversationInfo;
import com.meituan.csc.aigc.eval.param.PageParam;
import com.meituan.csc.aigc.eval.param.aida.AidaNodeConfigInfoDTO;
import com.meituan.csc.aigc.eval.param.application.ApplicationParam;
import com.meituan.csc.aigc.eval.param.application.ApplicationResultParam;
import com.meituan.csc.aigc.eval.param.application.DatasetApplicationParam;
import com.meituan.csc.aigc.eval.param.autoInspect.AutoInspectDetailConditionParam;
import com.meituan.csc.aigc.eval.param.dataset.DatasetExtraParam;
import com.meituan.csc.aigc.eval.param.dataset.SampleData;
import com.meituan.csc.aigc.eval.param.gpt.AidaRequest;
import com.meituan.csc.aigc.eval.param.gpt.ChatGptHttpRequest;
import com.meituan.csc.aigc.eval.param.gpt.GptContextParam;
import com.meituan.csc.aigc.eval.param.gpt.TokenParam;
import com.meituan.csc.aigc.eval.param.inspect.InspectInfo;
import com.meituan.csc.aigc.eval.param.log.TaskLogConditionParam;
import com.meituan.csc.aigc.eval.param.mark.MarkTaskConditionParam;
import com.meituan.csc.aigc.eval.param.mark.ScoreParam;
import com.meituan.csc.aigc.eval.param.metric.ChildMetricParam;
import com.meituan.csc.aigc.eval.param.metric.EnumMetricParam;
import com.meituan.csc.aigc.eval.param.metric.ManualAnnotationParam;
import com.meituan.csc.aigc.eval.param.model.ModelConfigRequestParam;
import com.meituan.csc.aigc.eval.param.session.SessionEvalResultParam;
import com.meituan.csc.aigc.eval.param.task.*;
import com.meituan.csc.aigc.eval.proxy.AidaInvokeServiceProxy;
import com.meituan.csc.aigc.eval.proxy.RedisClientProxy;
import com.meituan.csc.aigc.eval.service.*;
import com.meituan.csc.aigc.eval.service.strategy.eval.EvalStrategyService;
import com.meituan.csc.aigc.eval.service.strategy.eval.impl.ArenaEvalStrategyServiceImpl;
import com.meituan.csc.aigc.eval.service.strategy.eval.impl.CommonEvalStrategyService;
import com.meituan.csc.aigc.eval.service.strategy.metric.impl.CommonMetricStrategyService;
import com.meituan.csc.aigc.eval.utils.*;
import com.meituan.csc.aigc.runtime.api.AidaGptService;
import com.meituan.csc.aigc.runtime.dto.UserDTO;
import com.meituan.csc.aigc.runtime.dto.aida.AidaBaseResponse;
import com.meituan.csc.aigc.runtime.dto.aida.AidaFinalRes;
import com.meituan.csc.aigc.runtime.dto.aida.AppConfigVersionDTO;
import com.meituan.csc.aigc.runtime.dto.aida.SpaceResDTO;
import com.meituan.csc.aigc.runtime.inner.api.AidaUserInfoRemoteService;
import com.meituan.csc.aigc.runtime.inner.dto.InnerAppConfigDTO;
import com.meituan.csc.aigc.runtime.inner.dto.InnerMisInfoDTO;
import com.meituan.csc.aigc.runtime.inner.param.MisInfoParam;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.sankuai.csccratos.aida.config.client.dto.eval.AppDTO;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TaskExecuteServiceImpl implements TaskExecuteService {



    private static final int MAX_TEXT_LENGTH = 30000; // MySQL TEXT 最大字节数

    @Autowired
    private List<EvalStrategyService> evalStrategyServiceList;

    private Map<String, EvalStrategyService> evalServiceMap;

    @Autowired
    private EvalDatasetGeneratorService evalDatasetGeneratorService;

    @Autowired
    private EvalDatasetDetailGeneratorService evalDatasetDetailGeneratorService;
    @Autowired
    private EvalTrainDatasetVersionGeneratorService evalTrainDatasetVersionGeneratorService;
    @Autowired
    private EvalTaskGeneratorService evalTaskGeneratorService;

    @Autowired
    private AutoInspectDetailGeneratorService autoInspectDetailGeneratorService;

    @Autowired
    private EvalTaskSessionGeneratorService evalTaskSessionGeneratorService;

    @Autowired
    private EvalTaskQueryGeneratorService evalTaskQueryGeneratorService;

    @Autowired
    private EvalTaskQueryDetailGeneratorService evalTaskQueryDetailGeneratorService;

    @Autowired
    private ExcelService excelService;

    @Autowired
    private CommonEvalStrategyService commonEvalStrategyService;

    @Autowired
    private HttpConfig httpConfig;

    @Autowired
    private MetricConfigGeneratorService metricConfigGeneratorService;

    @Autowired
    private ApplicationConfigGeneratorService applicationConfigGeneratorService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private AidaExecuteService aidaExecuteService;

    @Autowired
    private CommonMetricStrategyService commonMetricStrategyService;

    @Autowired
    private ApplicationExecuteService applicationExecuteService;

    @Autowired
    private AidaInvokeServiceProxy aidaInvokeServiceProxy;

    @Autowired
    private DatasetExecuteService datasetExecuteService;

    @Autowired
    private EvalTaskLogGeneratorService evalTaskLogGeneratorService;
    @Autowired
    private AidaUserInfoRemoteService userRemoteService;

    @Autowired
    private ManualMarkTaskGeneratorService manualMarkTaskGeneratorService;

    @Autowired
    private ArenaEvalStrategyServiceImpl arenaEvalStrategyService;

    @Autowired
    private AidaGptService aidaGptPigeonService;

    @Autowired
    private MarkExecuteService markExecuteService;


    @Autowired
    private RedisClientProxy redisStoreClient;
    private static final String ARENA = "arena";
    private static final String EMPTY = "";

    private static final String NAME_SPLIT = "###";
    private static final List<String> BASE_EXCEL_HEADER = Arrays.asList("会话 id", "输入内容");

    private static final List<String> BASE_EXCEL_HEADER_RESULT = Arrays.asList("评测状态");

    private static final List<String> SESSION_EXCEL_HEAD = Arrays.asList("输入内容", "评测状态");

    private static final List<String> METRIC_RESULT = Arrays.asList("模型输出", "预期结果", "评测结果", "评测备注", "质检结果");

    @PostConstruct
    public void init() {
        if (MapUtils.isEmpty(evalServiceMap)) {
            evalServiceMap = evalStrategyServiceList.stream().collect(Collectors.toMap(EvalStrategyService::getName, Function.identity()));
        }
    }

    /**
     * 自动任务测试
     *
     * @param taskParam
     * @return
     */
    @Override
    public List<TaskTestDetailDTO.AidaModelConfigDTO> evalTaskTest(TaskParam taskParam) {
        // 校验
        check(taskParam);
        List<EvalDatasetDetailPo> datasetDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(taskParam.getDatasetIdList())) {
            datasetDetailList = checkDataset(taskParam);
            if (taskParam.getTaskType() != null && TaskTypeEnum.ARENA.getCode() == taskParam.getTaskType()) {
                // 竞技任务
            } else {
                // 字段映射绑定   测试时取一条数据测试
                if (CollectionUtils.isNotEmpty(datasetDetailList)) {
                    datasetDetailList = bindField(taskParam, Collections.singletonList(datasetDetailList.get(0)), UserUtils.getUser().getLogin());
                }
            }
        }

        EvalTaskRequest evalRequest = buildAndCheck(taskParam, datasetDetailList);
        //封装任务信息
        List<Long> metricIds = Optional.ofNullable(taskParam.getMetricList()).orElse(Collections.emptyList()).stream().map(TaskMetricParam::getMetricId).collect(Collectors.toList());
        SampleData sampleData = evalRequest.getSampleDataList().get(0);
        EvalTaskSessionPo evalTaskSessionPo = buildSession(evalRequest, taskParam, sampleData);
        //只取一条数据
        EvalDatasetDetailPo datasetDetailPo = datasetDetailList.get(0);
        EvalDatasetPo evalDatasetPo = evalDatasetGeneratorService.getById(datasetDetailPo.getDatasetId());
        Map<String, String> params = sampleData.getParams();


        List<AidaModelConfig> aidaModelConfigs = taskParam.getAidaModelConfig();
        List<MetricConfigPo> metricConfigPos = metricConfigGeneratorService.getByIdList(metricIds);
        Map<Long, MetricConfigPo> metricConfigPoMap = Optional.ofNullable(metricConfigPos).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));

        Map<String, String> keyNameMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(evalDatasetPo.getExtra())) {
            DatasetExtraParam datasetExtraParam = JSON.parseObject(evalDatasetPo.getExtra(), DatasetExtraParam.class);
            keyNameMap.putAll(Optional.ofNullable(datasetExtraParam.getHeadMapList()).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(SingleTemplateFieldBindDTO::getColumnKey, SingleTemplateFieldBindDTO::getColumnName, (k1, k2) -> k1)));
        }
        List<TaskTestDetailDTO.AidaModelConfigDTO> aidaModelConfigDTOS = Lists.newArrayList();
        //输入为空时，默认给个空格
        String inputContent = StringUtils.isNotBlank(sampleData.getInputContent()) ? sampleData.getInputContent() : " ";
        //选择多个应用时(也会有多个输出字段关联)，需要做每个应用和输出字段的映射
        List<Long> applicationids = Optional.ofNullable(evalRequest.getApplicationConfig()).orElse(Collections.emptyList()).stream().filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
        List<ApplicationConfigPo> applicationConfigPos = applicationConfigGeneratorService.getByIdList(applicationids);
        Map<Long, ApplicationConfigPo> applicationConfigPoMap = Optional.ofNullable(applicationConfigPos).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(ApplicationConfigPo::getId, Function.identity(),(k1, k2)->k1));
        for (AidaModelConfig modelConfig : aidaModelConfigs) {
            String answer = "";
            if (CallTypeEnum.ONLINE.getCode() == taskParam.getCallType()) {
                answer = getOnlineAnswer(modelConfig, evalRequest, sampleData, applicationConfigPoMap);
            } else {
                answer = getAidaAnswer(modelConfig, params, evalRequest, sampleData, inputContent);
            }

            TaskTestDetailDTO.AidaModelConfigDTO aidaModelConfigDTO = new TaskTestDetailDTO.AidaModelConfigDTO();
            BeanUtils.copyProperties(modelConfig, aidaModelConfigDTO);
            aidaModelConfigDTO.setNodeId(evalDatasetPo.getNodeId());
            //原始数据
            aidaModelConfigDTO.setDataset(getDatasetMap(datasetDetailPo, keyNameMap));

            List<EvalTaskQueryDetailPo> evalTaskQueryDetailPos = commonEvalStrategyService.buildTaskQueryDetail(evalRequest, sampleData, new CommonEvalStrategyService.EvalIdInfo(), evalTaskSessionPo);
            EvalTaskQueryDetailPo evalTaskQueryDetailPo = evalTaskQueryDetailPos.get(0);
            Map<String, String> evalResult = new HashMap<>();
            if (CollectionUtils.isNotEmpty(taskParam.getMetricList())) {

                for (TaskMetricParam taskMetricParam : taskParam.getMetricList()) {
                    MetricConfigPo metricConfig = metricConfigPoMap.get(taskMetricParam.getMetricId());

                    MetricReplyDTO metricReplyDTO = getMetricReply(inputContent, params, metricConfig, evalTaskQueryDetailPo, evalRequest, evalDatasetPo, answer);
                    evalResult.put(evalTaskQueryDetailPo.getOutputKey() + "-" + metricConfig.getName(), StringUtils.isNotBlank(metricReplyDTO.getResult()) ? metricReplyDTO.getResult() : metricReplyDTO.getMessage());
                }
            }
            aidaModelConfigDTO.setEvalResult(evalResult);
            aidaModelConfigDTO.setModelOutput(answer);
            aidaModelConfigDTOS.add(aidaModelConfigDTO);
        }

        return aidaModelConfigDTOS;
    }

    /**
     * 获取在线模型结果
     * @param modelConfig
     * @param evalRequest
     * @param sampleData
     * @param applicationConfigPoMap
     * @return
     */
    private String getOnlineAnswer(AidaModelConfig modelConfig, EvalTaskRequest evalRequest, SampleData sampleData, Map<Long, ApplicationConfigPo> applicationConfigPoMap) {
        List<String> applicationConfig = evalRequest.getApplicationConfig();
        if (CollectionUtils.isEmpty(applicationConfig)) {
            return null;
        }

        for (String applicationId : applicationConfig) {
            ApplicationConfigPo applicationConfigPo = applicationConfigPoMap.get(Long.parseLong(applicationId));
            if (isMatchingApplicationConfig(modelConfig, applicationConfigPo)) {
                String model = getModelFromRequest(evalRequest, applicationId);
                return getModelOutputOrDefault(sampleData, model, applicationId);
            }
        }
        return null;
    }

    /**
     * 判断关联的模型是不是当前应用
     * @param modelConfig
     * @param applicationConfigPo
     * @return
     */
    private boolean isMatchingApplicationConfig(AidaModelConfig modelConfig, ApplicationConfigPo applicationConfigPo) {
        return applicationConfigPo != null
                && modelConfig.getApplicationId().equals(applicationConfigPo.getPlatformApp())
                && modelConfig.getModelConfigVersionId().equals(applicationConfigPo.getRobotId());
    }

    private String getModelFromRequest(EvalTaskRequest evalRequest, String applicationId) {
        return Optional.ofNullable(evalRequest.getApplicationModelMap())
                .map(map -> map.get(applicationId))
                .orElse(null);
    }

    private String getModelOutputOrDefault(SampleData sampleData, String model, String applicationId) {
        return StringUtils.defaultIfBlank(commonEvalStrategyService.getModelOutput(sampleData, model, applicationId), "");
    }
    /**
     * 评测任务暂停
     *
     * @param taskId
     */
    @Override
    public void pauseTask(String taskId) {
        CommonUtils.checkEval(StringUtils.isNotBlank(taskId), "任务id不能为空");
        EvalTaskPo evalTaskPo = evalTaskGeneratorService.getById(Long.valueOf(taskId));
        CommonUtils.checkEval(Objects.nonNull(evalTaskPo.getType()) && TaskTypeEnum.AUTO.getCode() == evalTaskPo.getType(), "自动任务才允许暂停");
        //评测中才支持暂停
        CommonUtils.checkEval(AutoTaskStatusEnum.EVALUATING.getCode() == evalTaskPo.getStatus(), "任务状态不是评测中");
        evalTaskPo.setStatus(AutoTaskStatusEnum.PAUSE.getCode());
        evalTaskGeneratorService.updateById(evalTaskPo);
        //释放锁
        commonEvalStrategyService.releaseLock(evalTaskPo);
        setTaskPauseCache(taskId);
    }

    /**
     * 评测任务继续执行
     *
     * @param taskId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resumeTask(String taskId) {
        CommonUtils.checkEval(StringUtils.isNotBlank(taskId), "任务id不能为空");
        EvalTaskPo evalTaskPo = evalTaskGeneratorService.getById(Long.valueOf(taskId));
        //暂停任务才允许继续执行
        CommonUtils.checkEval(AutoTaskStatusEnum.PAUSE.getCode() == evalTaskPo.getStatus(), "暂停任务才允许继续执行");
        CommonUtils.checkEval(Objects.nonNull(evalTaskPo.getType()) && TaskTypeEnum.AUTO.getCode() == evalTaskPo.getType(), "自动任务才允许继续执行");

        //重新执行任务时先获取锁
        boolean lock = commonEvalStrategyService.getLock(evalTaskPo);
        clearTaskPauseCache(taskId);
        if (!lock) {
            evalTaskPo.setStatus(AutoTaskStatusEnum.QUEUING.getCode());
            evalTaskGeneratorService.updateById(evalTaskPo);
            return;
        }
        evalTaskPo.setStatus(AutoTaskStatusEnum.EVALUATING.getCode());
        evalTaskGeneratorService.updateById(evalTaskPo);
        //继续执行任务
        CompletableFuture.runAsync(() -> {
            try {
                excuteTask(evalTaskPo);
            } catch (Exception e) {
                log.error("异步执行excuteTask失败, evalTaskPo={}, msg={}", JSON.toJSONString(evalTaskPo), e.getMessage(), e);
                // 获取任务信息失败，更新任务状态为失败
                commonEvalStrategyService.updateTaskResultStatusAndScore(evalTaskPo.getId(), AutoTaskStatusEnum.FAILED, null);
            }
        });
    }

    /**
     * 清除缓存标识
     *
     * @param taskId
     */
    private void clearTaskPauseCache(String taskId) {
        try {
            redisStoreClient.delete(new StoreKey(RedisConstants.EVAL_TASK_PAUSE, taskId));
        } catch (Exception e) {
            Cat.logError(e);
            log.error("清除任务暂停缓存失败,taskId={},msg={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 设置缓存标识  不过期
     *
     * @param taskId
     */
    private void setTaskPauseCache(String taskId) {
        try {
            redisStoreClient.set(new StoreKey(RedisConstants.EVAL_TASK_PAUSE, taskId), true);
        } catch (Exception e) {
            Cat.logError(e);
            log.error("设置任务暂停缓存失败,taskId={},msg={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 任务继续执行
     *
     * @param evalTaskPo
     */
    private void excuteTask(EvalTaskPo evalTaskPo) {

        if (StringUtils.isBlank(evalTaskPo.getExtra())) {
            // 获取任务信息失败，更新任务状态为失败
            commonEvalStrategyService.updateTaskResultStatusAndScore(evalTaskPo.getId(), AutoTaskStatusEnum.FAILED, null);
        } else {
            try {
                Map<String, String> extMap = JSON.parseObject(evalTaskPo.getExtra(), new TypeReference<Map<String, String>>() {
                });
                if (!extMap.containsKey("evalRequest")) {
                    log.error("执行继续执行任务失败,上下文信息缺失，evalTask={}", JSON.toJSONString(evalTaskPo));
                    // 获取任务信息失败，更新任务状态为失败
                    commonEvalStrategyService.updateTaskResultStatusAndScore(evalTaskPo.getId(), AutoTaskStatusEnum.FAILED, null);
                } else {
                    EvalTaskRequest evalRequest = JSON.parseObject(extMap.get("evalRequest"), EvalTaskRequest.class);
                    evalRequest.setSampleDataList(filterQueryFinishData(commonEvalStrategyService.getSampleDataList(evalTaskPo.getId()), evalTaskPo));
                    evalRequest.setSessionList(filterSessionFinishData(commonEvalStrategyService.getSessionList(evalTaskPo.getId()), evalTaskPo));
                    CommonEvalStrategyService.EvalIdInfo evalIdInfo = JSON.parseObject(extMap.get("evalIdInfo"), CommonEvalStrategyService.EvalIdInfo.class);
                    evalIdInfo.setEvalTaskId(evalTaskPo.getId());
                    log.info("继续执行任务:evalRequest={}", JSON.toJSONString(evalTaskPo));
                    evalServiceMap.get(evalTaskPo.getAbility()).executeTask(evalRequest, evalIdInfo);
                }
            } catch (Exception e) {
                log.error("执行继续执行任务失败,evalTask={},msg={}", JSON.toJSONString(evalTaskPo), e.getMessage(), e);
                // 获取任务信息失败，更新任务状态为失败
                commonEvalStrategyService.updateTaskResultStatusAndScore(evalTaskPo.getId(), AutoTaskStatusEnum.FAILED, null);
            }
        }
        //取到缓存，说明是暂停了。这个时候不需要释放锁，因为暂停接口里已经释放了
        if (commonEvalStrategyService.isTaskPause(evalTaskPo.getId())) {
            return;
        }

        commonEvalStrategyService.releaseLock(evalTaskPo);
    }

    private List<EvalTaskSessionPo> filterSessionFinishData(List<EvalTaskSessionPo> sessionPoList, EvalTaskPo evalTaskPo) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(sessionPoList)) {
            return new ArrayList<>();
        }
        if (AutoTaskStatusEnum.EVALUATING.getCode() == evalTaskPo.getStatus()) {
            return sessionPoList.stream().filter(sessionPo -> TaskSessionStatusEnum.EVALUATING.getCode() == sessionPo.getStatus()).collect(Collectors.toList());
        }
        return sessionPoList;
    }

    private List<SampleData> filterQueryFinishData(List<SampleData> sampleDataList, EvalTaskPo evalTaskPo) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(sampleDataList)) {
            return new ArrayList<>();
        }
        // 多轮不过滤query，由session进行过滤
        if (AbilityEnum.MULTI_ROUND.getName().equals(evalTaskPo.getAbility())) {
            return sampleDataList;
        }
        if (AutoTaskStatusEnum.EVALUATING.getCode() == evalTaskPo.getStatus()) {
            return sampleDataList.stream().filter(sampleData -> sampleData.getStatus() != null
                    && TaskQueryStatusEnum.RESTART_SCAN_STATUS.contains(sampleData.getStatus())
            ).collect(Collectors.toList());
        }
        return sampleDataList;
    }

    private MetricReplyDTO getMetricReply(String inputContent, Map<String, String> params, MetricConfigPo metricConfig, EvalTaskQueryDetailPo evalTaskQueryDetailPo, EvalTaskRequest evalRequest, EvalDatasetPo evalDatasetPo, String answer) {
        MetricRequestParam metricRequestParam = new MetricRequestParam();
        metricRequestParam.setInputContent(inputContent);
        metricRequestParam.setParams(params);
        metricRequestParam.setMetric(metricConfig);
        metricRequestParam.setTestApplication(evalTaskQueryDetailPo.getTestApplication());
        metricRequestParam.setMessageList(null);
        metricRequestParam.setIsInner(evalRequest.getIsInner());
        CommonLogParam param = new CommonLogParam();
        param.setLogType(TaskLogTypeEnum.QUERY_METRIC.getCode());
        param.setMetricId(metricConfig.getId());
        param.setDatasetId(evalDatasetPo.getId());
        param.setTestApplication(evalTaskQueryDetailPo.getTestApplication());
        metricRequestParam.setLogParam(param);
        MetricReplyDTO metricReplyDTO = commonEvalStrategyService.executeMetric(evalTaskQueryDetailPo, answer, metricRequestParam);
        return metricReplyDTO;
    }

    public String getAidaAnswer(AidaModelConfig modelConfig, Map<String, String> params, EvalTaskRequest evalRequest, SampleData sampleData, String inputContent) {
        AidaRequest request = new AidaRequest();
        request.setParams(params);
        request.setUser(evalRequest.getCreatorMis());
        request.setApiSecretKey(aidaInvokeServiceProxy.getApiSecretKey(modelConfig.getApplicationId()));
        request.setModelConfigVersionId(modelConfig.getModelConfigVersionId());
        ConversationInfo conversationInfo = aidaInvokeServiceProxy.generateAidaConversationId(request);

        return requestAida(evalRequest, modelConfig, inputContent, conversationInfo, params, sampleData);
    }

    private static Map<String, String> parseParams(SampleData sampleData, Map<String, String> nameKeyMap) {
        Map<String, String> params = Maps.newHashMap();
        if (StringUtils.isNotBlank(sampleData.getContent())) {
            Map<String, String> contentMap = JSON.parseObject(sampleData.getContent(), new TypeReference<HashMap<String, String>>() {
            });
            contentMap.forEach((k, v) -> {
                if (MapUtils.isNotEmpty(nameKeyMap) && StringUtils.isNotBlank(nameKeyMap.get(k))) {
                    params.put(nameKeyMap.get(k), v);
                } else {
                    params.put(k, v);
                }
            });

        }
        return params;
    }


    private static EvalTaskSessionPo buildSession(EvalRequest evalRequest, TaskParam taskParam, SampleData sampleData) {

        EvalTaskSessionPo evalTaskSessionPo = new EvalTaskSessionPo();
        evalTaskSessionPo.setGmtModified(new Date());
        evalTaskSessionPo.setDatasetId(sampleData.getDatasetId());
        evalTaskSessionPo.setSessionId(sampleData.getSessionId());
        return evalTaskSessionPo;
    }

    private static Map<String, String> getDatasetMap(EvalDatasetDetailPo datasetDetail, Map<String, String> keyNameMap) {
        //原始数据
        Map<String, String> datasetMap = Maps.newHashMap();
        if (Objects.nonNull(datasetDetail) && StringUtils.isNotBlank(datasetDetail.getContent())) {
            Map<String, String> contentMap = JSON.parseObject(datasetDetail.getContent(), new TypeReference<Map<String, String>>() {
            });
            if (MapUtils.isNotEmpty(contentMap)) {
                contentMap.forEach((key, value) -> datasetMap.put(keyNameMap.getOrDefault(key, key), value));
            }
        }
        return datasetMap;
    }

    private void check(TaskParam taskParam) {
        CommonUtils.checkEval(taskParam != null, "参数为空");
        AbilityEnum abilityEnum = AbilityEnum.parse(taskParam.getAbilityType());
        CommonUtils.checkEval(abilityEnum != null, "能力类型错误");
        CommonUtils.checkEval(StringUtils.isNotBlank(taskParam.getName()), "任务名称不能为空");
        checkApplicationLimit(taskParam);
        CommonUtils.checkEval(CollectionUtils.isNotEmpty(taskParam.getDatasetIdList()), "数据集不能为空");
        checkDatasetSizeLimit(taskParam);
        CommonUtils.checkEval(CollectionUtils.isNotEmpty(taskParam.getMetricList()), "评测指标不能为空");
        CommonUtils.checkEval(CollectionUtils.isNotEmpty(taskParam.getAidaModelConfig()), "ai搭参数不能为空");

        List<Long> metricIds = Optional.ofNullable(taskParam.getMetricList()).orElse(Collections.emptyList()).stream().map(TaskMetricParam::getMetricId).collect(Collectors.toList());
        List<MetricConfigPo> metricConfigPos = metricConfigGeneratorService.getByIdList(metricIds);
        CommonUtils.checkEval(CollectionUtils.isNotEmpty(metricConfigPos), "指标为空");
        CommonUtils.checkEval(CallTypeEnum.ONLINE.getCode() != taskParam.getCallType() || MetricEvalTypeEnum.MANUAL.getCode() != metricConfigPos.get(0).getEvalType(), "在线任务和人工指标不允许测试");
    }

    private String requestAida(EvalTaskRequest evalRequest, AidaModelConfig modelConfig, String inputContent, ConversationInfo conversationInfo, Map<String, String> params, SampleData sampleData) {
        AidaRequest aidaRequest = new AidaRequest();
        aidaRequest.setUser(evalRequest.getCreatorMis());
        aidaRequest.setApiSecretKey(aidaInvokeServiceProxy.getApiSecretKey(modelConfig.getApplicationId()));
        aidaRequest.setAppId(modelConfig.getApplicationId());
        aidaRequest.setModelConfigVersionId(modelConfig.getModelConfigVersionId());
        aidaRequest.setInputContent(inputContent);
        aidaRequest.setConversationId(Objects.nonNull(conversationInfo) ? conversationInfo.getConversationId() : null);
        aidaRequest.setParams(params);
        aidaRequest.setBusinessParam(sampleData.getBusinessParam());
        aidaRequest.setMessageList(buildMessageList(evalRequest, Collections.singletonList(sampleData)));
        aidaRequest.setNodeId(evalRequest.getNodeId());
        aidaRequest.setNodeAppModelVersionId(evalRequest.getNodeAppModelVersionId());
        aidaRequest.setNodeApiToken(evalRequest.getNodeApiToken());
        aidaRequest.setFridayKey(aidaInvokeServiceProxy.getAidaFridayKey(evalRequest.getIsInner(), modelConfig.getApplicationId()));
        AidaFinalRes aidaGptReply = aidaInvokeServiceProxy.getAidaResult(aidaRequest);
        log.info("task-test-aidaGptReply" + aidaGptReply);
        if (aidaGptReply == null) {
            return "调用ai搭接口异常";
        }
        if (!"200".equals(aidaGptReply.getCode())) {
            return aidaGptReply.getMessage();
        }
        return aidaGptReply.getAnswer();
    }

    public List<ChatGptHttpRequest.GptMessage> buildMessageList(EvalTaskRequest evalRequest, List<SampleData> datasetDataList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(datasetDataList)) {
            return new ArrayList<>();
        }
        List<ChatGptHttpRequest.GptMessage> messages = new ArrayList<>();
        datasetDataList.forEach(datasetData -> {
            if (StringUtils.isNotBlank(datasetData.getInputContent())) {
                ChatGptHttpRequest.GptMessage message = new ChatGptHttpRequest.GptMessage();
                message.setRole(CommonConstants.GPT_PARAM_ASSISTANT);
                message.setContent(getInputParam(datasetData.getInputContent()));
                messages.add(message);
            }
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(datasetData.getTaskQueryDetailPoList())) {
                EvalTaskQueryDetailPo detail = datasetData.getTaskQueryDetailPoList().get(0);
                if (StringUtils.isNotBlank(detail.getModelOutput())) {
                    ChatGptHttpRequest.GptMessage message = new ChatGptHttpRequest.GptMessage();
                    message.setRole(CommonConstants.GPT_PARAM_USER);
                    message.setContent(getOutputMessage(Long.parseLong(detail.getTestApplication()), datasetData.getExpectedResult(), evalRequest, detail.getModelOutput()));
                    messages.add(message);
                }
            }
        });
        return messages;
    }

    private String getInputParam(String inputContent) {
        if (StringUtils.isBlank(inputContent)) {
            return inputContent;
        }
        JSONObject jsonObject = DataConvertUtil.tryConvertJson(inputContent);
        if (jsonObject != null && !jsonObject.containsKey("response")) {
            return jsonObject.getString("response");
        }
        return inputContent;
    }

    public String getOutputMessage(Long applicationId, String expect, EvalTaskRequest evalRequest, String reply) {
        if (evalRequest == null) {
            return reply;
        }
        String message = reply;
        if (evalRequest.getHistorySource() != null && evalRequest.getHistorySource() == TaskHistorySourceEnum.EXPECT.getCode()) {
            List<ModelExpectDTO> modelExpectList = DataConvertUtil.tryConvertObjectArray(expect, ModelExpectDTO.class);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(modelExpectList)) {
                message = modelExpectList.get(0).getExpect();
            } else {
                message = expect;
            }
        } else if (StringUtils.isNotBlank(reply)) {
            if (MapUtils.isNotEmpty(evalRequest.getApplicationOutputMap()) && evalRequest.getApplicationOutputMap().containsKey(applicationId)) {
                String outputParam = evalRequest.getApplicationOutputMap().get(applicationId);
                JSONObject jsonObject = DataConvertUtil.tryConvertJson(reply);
                if (jsonObject != null) {
                    message = jsonObject.getString(outputParam);
                }
            }
            if (StringUtils.isBlank(message)) {
                message = reply;
            }
        }
        return message;
    }

    private ConversationInfo getConversationInfo(Map<String, String> params, EvalTaskRequest evalRequest) {
        AidaRequest aidaRequest = new AidaRequest();
        aidaRequest.setParams(params);
        aidaRequest.setUser(evalRequest.getCreatorMis());
        Long applicationId = Lion.getMap(ConfigUtil.getAppkey(), LionConstants.EVAL_ROBOT_MOCK_LIST, ApplicationDTO.class).get(evalRequest.getRobotMockId().toString()).getId();
        ApplicationConfigPo applicationConfig = applicationConfigGeneratorService.getById(applicationId);
        aidaRequest.setApiSecretKey(aidaInvokeServiceProxy.getApiSecretKey(getAidaAppId(String.valueOf(applicationId))));
        aidaRequest.setModelConfigVersionId(applicationConfig.getRobotId());
        ConversationInfo conversationInfo = aidaInvokeServiceProxy.generateAidaConversationId(aidaRequest);
        return conversationInfo;
    }

    protected String getAidaAppId(String application) {
        Long applicationId = DataConvertUtil.tryConvertLongWithNull(application);
        if (applicationId != null) {
            ApplicationConfigPo applicationConfig = applicationConfigGeneratorService.getById(applicationId);
            if (applicationConfig != null) {
                return applicationConfig.getPlatformApp();
            }
        }
        return null;
    }

    /**
     * 校验数据集
     *
     * @param taskParam
     */
    private List<EvalDatasetDetailPo> checkDataset(TaskParam taskParam) {
        List<EvalDatasetDetailPo> datasetDetailList = null;
        List<Long> datasetIdList = taskParam.getDatasetIdList();
        List<EvalDatasetPo> datasetList = evalDatasetGeneratorService.getByIdList(datasetIdList);
        CommonUtils.checkEval(CollectionUtils.isNotEmpty(datasetList), "数据集不存在或已经被删除");
        datasetList.forEach(evalDataset -> CommonUtils.checkEval(evalDataset.getStatus() != DatasetStatusEnum.DELETED.getCode(), "数据集状态中存在已经被删除的数据集:" + evalDataset.getName()));
        datasetDetailList = evalDatasetDetailGeneratorService.getByDatasetIdList(datasetIdList);
        // 兼容历史数据，旧数据有一部分是有预期结果，这个值会影响新值的填入
        // datasetDetailList.forEach(datasetDetail -> datasetDetail.setExpect(null));
        CommonUtils.checkEval(CollectionUtils.isNotEmpty(datasetDetailList), "数据集下没有数据");
        return datasetDetailList;
    }

    @SuppressWarnings("all")
    @Override
    public void createTask(TaskParam taskParam) {
        checkParam(taskParam);
        List<AidaModelConfig> aidaModelConfig = taskParam.getAidaModelConfig();

        // TODO: 2025/3/10 临时逻辑，支持AI搭extra_info字段
        //setExtraInfo(taskParam);

        List<EvalDatasetDetailPo> datasetDetailList = new ArrayList<>();
        //构建任务时选取了评测集
        if (CollectionUtils.isNotEmpty(taskParam.getDatasetIdList())) {
            datasetDetailList = checkDataset(taskParam);
            List<Long> datasetIdList = taskParam.getDatasetIdList();
            if (taskParam.getTaskType() != null && TaskTypeEnum.ARENA.getCode() == taskParam.getTaskType()) {
                // 竞技任务
            } else {
                // 字段映射绑定
                datasetDetailList = bindField(taskParam, datasetDetailList, UserUtils.getUser().getLogin());
                //只选一个应用时，把应用和数据集关联，只更新一次，第二次后不更新
                if (1 == datasetIdList.size() && CollectionUtils.isNotEmpty(aidaModelConfig) && 1 == aidaModelConfig.size()) {
                    updateApplication(datasetIdList, aidaModelConfig);
                }
            }

        }
        //如果是竞技任务
        if (taskParam.getTaskType() != null && taskParam.getTaskType().equals(TaskTypeEnum.ARENA.getCode())) {
            AreaEvalRequest areaEvalRequest = buildAreaEvalRequest(taskParam, datasetDetailList);
            arenaEvalStrategyService.execute(areaEvalRequest);
        } else {
            EvalTaskRequest evalRequest = buildAndCheck(taskParam, datasetDetailList);
            evalServiceMap.get(AbilityEnum.parse(taskParam.getAbilityType()).getName()).execute(evalRequest);
        }
    }

    private EvalTaskRequest buildAndCheck(TaskParam taskParam, List<EvalDatasetDetailPo> datasetDetailList) {
        //预期结果长度校验前置
        checkLength(datasetDetailList);

        //数据集的businessParam字段做前置校验
        checkBusinessParam(datasetDetailList);
        EvalTaskRequest evalRequest = buildEvalTaskRequest(taskParam, datasetDetailList);
        //friday前置校验，减少aida校验报错
        try {
            checkFridayKey(evalRequest, taskParam);
        } catch (CheckException e) {
            log.error("fridayKey前置校验失败{}", e.getMessage());
            CommonUtils.checkEval(false, e.getMessage());
        } catch (Exception e) {
            log.error("fridayKey校验异常{}", e.getMessage());
        }

        // 校验指标所需变量是否完整
        checkMetricParam(taskParam, datasetDetailList);
        return evalRequest;
    }

    /**
     * 预期结果长度校验  以后可扩展其他字段校验
     * @param datasetDetailList
     */
    private void checkLength(List<EvalDatasetDetailPo> datasetDetailList) {
        if(CollectionUtils.isEmpty(datasetDetailList)){
            return;
        }
        for (EvalDatasetDetailPo datasetDetailPo : datasetDetailList) {
            if(Objects.nonNull(datasetDetailPo) && StringUtils.isNotBlank(datasetDetailPo.getExpect())){
                CommonUtils.checkEval(datasetDetailPo.getExpect().length() <= MAX_TEXT_LENGTH, "预期结果长度超过限制");
            }
        }
    }

    /**
     * 校验businessParam字段
     *
     * @param businessParamMap
     */
    private void validateBusinessParam(Map<String, String> businessParamMap) {
        // 定义json数组需要校验的字段及其对应的错误信息
        Map<String, String> fieldsArray = Collections.unmodifiableMap(
                new HashMap<String, String>() {{
                    put(TemplateFieldEnum.ATTACHMENTS.getCode(), TemplateFieldEnum.ATTACHMENTS.getInfo());
                    put(TemplateFieldEnum.HISTORIES.getCode(), TemplateFieldEnum.HISTORIES.getInfo());
                    put(TemplateFieldEnum.HISTORY.getCode(), TemplateFieldEnum.HISTORIES.getInfo());
                    put(TemplateFieldEnum.LLM_HISTORY.getCode(), TemplateFieldEnum.HISTORIES.getInfo());
                }}
        );
        // 定义json对象 需要校验的字段及其对应的错误信息
        Map<String, String> fieldsJson = Collections.unmodifiableMap(
                new HashMap<String, String>() {{
                    put(TemplateFieldEnum.EXTRAINFO.getCode(), TemplateFieldEnum.EXTRAINFO.getInfo());
                }}
        );

        // 遍历需要校验的字段
        // 遍历需要校验的字段
        businessParamMap.forEach((fieldCode, fieldValue) -> {
            if (fieldsArray.containsKey(fieldCode) && StringUtils.isNotBlank(fieldValue)) {
                CommonUtils.checkEval(DataConvertUtil.isValidJsonArray(fieldValue), fieldsArray.get(fieldCode) + "格式错误");
            }
            if (fieldsJson.containsKey(fieldCode) && StringUtils.isNotBlank(fieldValue)) {
                CommonUtils.checkEval(DataConvertUtil.isValidJson(fieldValue), fieldsJson.get(fieldCode) + "格式错误");
            }
        });
    }

    private void checkBusinessParam(List<EvalDatasetDetailPo> datasetDetailList) {
        for (EvalDatasetDetailPo datasetDetailPo : datasetDetailList) {
            if (StringUtils.isNotBlank(datasetDetailPo.getBusinessParam())) {
                Map<String, String> businessParamMap = JSON.parseObject(datasetDetailPo.getBusinessParam(), new TypeReference<Map<String, String>>() {
                });
                CommonUtils.checkEval(businessParamMap != null, "数据集businessParam字段格式错误");
                validateBusinessParam(businessParamMap);
            }
        }
    }

    private void setExtraInfo(TaskParam taskParam) {
        try {
            if (taskParam.getModelSource() != null && taskParam.getModelSource().equals(TaskModelSourceEnum.AI.getCode())) {
                Set<String> workspcaeIds = taskParam.getAidaModelConfig().stream().map(AidaModelConfig::getWorkspaceId).collect(Collectors.toSet());
                List<String> whiteListWorkspcaeList = Lion.getList(ConfigUtil.getAppkey(), "extra.info.white.list", String.class);
                if (CollectionUtils.isNotEmpty(whiteListWorkspcaeList)) {
                    for (String workspcae : whiteListWorkspcaeList) {
                        if (workspcaeIds.contains(workspcae)) {
                            // 临时支持extraInfo逻辑
                            Map<Long, List<TemplateFieldBindDTO>> bindFields = taskParam.getBindFields();
                            if (MapUtils.isNotEmpty(bindFields)) {
                                for (Map.Entry<Long, List<TemplateFieldBindDTO>> entry : bindFields.entrySet()) {
                                    List<TemplateFieldBindDTO> list = entry.getValue();
                                    TemplateFieldBindDTO templateFieldBindDTO = new TemplateFieldBindDTO();
                                    templateFieldBindDTO.setColumnName("__EXTRAINFO__");
                                    templateFieldBindDTO.setFieldCode(TemplateFieldEnum.PARAMS.getCode());
                                    templateFieldBindDTO.setFieldKey("__EXTRAINFO__");
                                    templateFieldBindDTO.setFieldName("__EXTRAINFO__");
                                    list.add(templateFieldBindDTO);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Cat.logError(e);
            log.error("临时支持extraInfo逻辑失败,taskParam={}", JSONObject.toJSONString(taskParam), e);
        }
    }

    /**
     * 选一个应用时，把应用和数据集关联，只更新一次，第二次后不更新
     *
     * @param datasetIdList
     * @param aidaModelConfig
     */
    private void updateApplication(List<Long> datasetIdList, List<AidaModelConfig> aidaModelConfig) {
        EvalDatasetPo evalDatasetPo = evalDatasetGeneratorService.getById(datasetIdList.get(0));
        if (Objects.nonNull(evalDatasetPo) && StringUtils.isBlank(evalDatasetPo.getPlatformApp())) {
            evalDatasetPo.setPlatformApp(aidaModelConfig.get(0).getApplicationId());
            evalDatasetPo.setPlatformWorkspace(aidaModelConfig.get(0).getWorkspaceId());
            evalDatasetGeneratorService.updateById(evalDatasetPo);
        }
    }

    private void checkFridayKey(EvalTaskRequest evalRequest, TaskParam taskParam) {
        // 内部人员无需校验，一定可以调用成功
        if (evalRequest.getIsInner()) {
            return;
        }
        //离线任务 校验应用 在线模式，不校验应用
        // 校验应用：app_id查ai搭找到apiToken
        //需要校验 fridaykey
        if (isOfflineMode(evalRequest)) {
            //校验应用
            checkFriday(evalRequest, getAidaApplicationIdList(taskParam));
        }
        //在线模式
        //人工指标。不校验指标
        // 自动指标   且 有全等、包含、相似之外的指标：校验指标
        //自动指标  且全部都是全等、包含、相似）：不校验指标
        //校验指标：指标表，找到应用表，找到app_id，查ai搭找到apiToken
        List<String> aidaApplicationIdList = getAidaAppIdsFromMetrics(getMetricConfigList(taskParam));
        if (CollectionUtils.isNotEmpty(aidaApplicationIdList)) {
            checkFriday(evalRequest, aidaApplicationIdList);
        }
    }

    private List<String> getAidaApplicationIdList(TaskParam taskParam) {
        if (TaskModelSourceEnum.APPLICATION.getCode() == taskParam.getModelSource()) {
            return Optional.ofNullable(applicationConfigGeneratorService.listByIds(taskParam.getApplicationConfig()))
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(applicationConfigPo -> {
                        if (applicationConfigPo.getPlatformApp() == null) {
                            return false;
                        }
                        return applicationConfigPo.getSource() != null && ApplicationSourceEnum.PB_SYSTEM.getCode() != applicationConfigPo.getSource();
                    })
                    .map(ApplicationConfigPo::getPlatformApp)
                    .map(String::valueOf)
                    .collect(Collectors.toList());
        } else {
            return Optional.ofNullable(taskParam.getAidaModelConfig())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(Objects::nonNull)
                    .map(AidaModelConfig::getApplicationId)
                    .collect(Collectors.toList());
        }
    }

    private boolean isOfflineMode(EvalTaskRequest evalRequest) {
        return CallTypeEnum.OFFLINE.getCode() == evalRequest.getCallType();
    }

    private List<String> getApplicationIds(TaskParam taskParam) {
        return Optional.ofNullable(taskParam.getAidaModelConfig())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(AidaModelConfig::getApplicationId)
                .collect(Collectors.toList());
    }

    private List<MetricConfigPo> getMetricConfigList(TaskParam taskParam) {
        //从请求参数取到指标id列表
        List<Long> metricIdList = Optional.ofNullable(taskParam.getMetricList())
                .orElse(Collections.emptyList())
                .stream()
                .map(TaskMetricParam::getMetricId)
                .distinct()
                .collect(Collectors.toList());
        //指标id查询指标表
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricIdList);
        //过滤出自动指标 和 除完全匹配、自动匹配、相似度匹配的外的指标
        return metricConfigList.stream()
                .filter(metricConfigPo -> Objects.nonNull(metricConfigPo)
                        && ((metricConfigPo.getEvalType() != null && metricConfigPo.getEvalType() == MetricEvalTypeEnum.AUTO.getCode())
                        && (Objects.nonNull(metricConfigPo.getId()) && !EvalMetricTypeEnum.NOT_AIDA_METRIC.contains(metricConfigPo.getId().intValue()))))
                .collect(Collectors.toList());
    }

    private boolean hasAutoMetrics(List<MetricConfigPo> metricConfigList) {
        return metricConfigList.stream()
                .filter(metricConfigPo -> metricConfigPo.getEvalType() != null
                        && metricConfigPo.getEvalType() == MetricEvalTypeEnum.AUTO.getCode())
                .count() > 0;
    }

    private boolean hasNonMatchingMetrics(List<MetricConfigPo> metricConfigList) {
        return metricConfigList.stream()
                .filter(metricConfigPo -> Objects.nonNull(metricConfigPo)
                        && Objects.nonNull(metricConfigPo.getId())
                        && !EvalMetricTypeEnum.NOT_AIDA_METRIC.contains(metricConfigPo.getId().intValue()))
                .count() > 0;
    }

    /**
     * 校验指标
     * 从指标数据找到应用的id，再找到机器人id，再查ai搭找到apiToken
     *
     * @param metricConfigList
     * @return
     */
    private List<String> getAidaAppIdsFromMetrics(List<MetricConfigPo> metricConfigList) {
        //从指标对象列表里面获取到应用表主键id  getApplicationId
        List<String> applicationIds = Optional.ofNullable(metricConfigList).orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(MetricConfigPo::getApplicationId)
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applicationIds)) {
            return null;
        }
        //查询应用表  获取到关联的机器人id
        return Optional.ofNullable(applicationConfigGeneratorService.listByIds(applicationIds))
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(ApplicationConfigPo::getPlatformApp)
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    private void checkFriday(EvalTaskRequest evalRequest, List<String> aidaApplicationIdList) {
        if (StringUtils.isNotBlank(evalRequest.getNodeId())) {
            return;
        }
        checkApplicationConfig(aidaApplicationIdList);
    }

    private void checkApplicationConfig(List<String> aidaApplicationIdList) {
        for (String aidaApplicationId : aidaApplicationIdList) {
            Boolean hasFridayKeyConfig = aidaInvokeServiceProxy.hasFridayKeyConfig(aidaApplicationId);
            CommonUtils.checkEval(hasFridayKeyConfig != null && hasFridayKeyConfig, "未配置fridayKey,请先去工作空间配置fridayKey");
        }
    }

    @Override
    public List<CustomStatusDTO> listTaskStatus() {
        //List<AutoTaskStatusEnum> taskStatusEnumList = Arrays.asList(AutoTaskStatusEnum.values());
        return AutoTaskStatusEnum.AUTO_TASK_STATUS.stream().map(autoTaskStatusEnum -> {
            CustomStatusDTO customStatusDTO = new CustomStatusDTO();
            customStatusDTO.setStatusCode(autoTaskStatusEnum.getCode());
            customStatusDTO.setRgb(autoTaskStatusEnum.getRgb());
            customStatusDTO.setStatusName(autoTaskStatusEnum.getInfo());
            return customStatusDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CustomStatusDTO> listTaskQueryStatus(Integer taskType) {
        List<TaskQueryStatusEnum> queryStatusEnumList = taskType != null && taskType == TaskTypeEnum.ARENA.getCode() ? TaskQueryStatusEnum.ARENA_STATUS : TaskQueryStatusEnum.AUTO_STATUS;
        return queryStatusEnumList.stream().map(queryStatusEnum -> {
            CustomStatusDTO customStatusDTO = new CustomStatusDTO();
            customStatusDTO.setStatusCode(queryStatusEnum.getCode());
            customStatusDTO.setStatusName(queryStatusEnum.getName());
            customStatusDTO.setRgb(queryStatusEnum.getRgb());
            return customStatusDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public PageData<TaskDetailDTO> pageTaskDetail(PageParam<TaskQueryConditionParam> pageParam) {
        EvalTaskPo evalTask = evalTaskGeneratorService.getById(pageParam.getCondition().getTaskId());
        CommonUtils.checkEval(evalTask != null, "任务不存在");
        List<EvalTaskQueryPo> taskQueryList = evalTaskQueryGeneratorService.getByCondition(pageParam.getCondition());
        TaskQueryDetailConditionParam condition = new TaskQueryDetailConditionParam();
        condition.setTaskId(pageParam.getCondition().getTaskId());
        condition.setMetricId(pageParam.getCondition().getMetricId());
        condition.setEvalResult(pageParam.getCondition().getEvalResult());
        condition.setEvalTarget(pageParam.getCondition().getEvalTarget());
        condition.setInspectionResult(pageParam.getCondition().getInspectionResult());
        List<EvalTaskQueryDetailPo> queryDetailList = evalTaskQueryDetailGeneratorService.getByCondition(condition);
        List<Long> queryIds = Optional.ofNullable(queryDetailList).orElse(Collections.emptyList()).stream().map(EvalTaskQueryDetailPo::getQueryId).distinct().collect(Collectors.toList());
        taskQueryList = Optional.ofNullable(taskQueryList).orElse(Collections.emptyList()).stream().filter(queryPo -> queryIds.contains(queryPo.getId())).collect(Collectors.toList());
        // 过滤质检人信息
        if (StringUtils.isNotBlank(pageParam.getCondition().getInspector())) {
            taskQueryList = filterInspector(taskQueryList, queryDetailList, pageParam.getCondition().getInspector());
        }
        if (CollectionUtils.isEmpty(taskQueryList)) {
            return PageData.emptyData(pageParam.getPageNum(), pageParam.getPageSize());
        }
        int totalSize = taskQueryList.size();
        List<EvalTaskQueryPo> pageDataList = PageData.page(pageParam.getPageNum(), pageParam.getPageSize(), taskQueryList);
        return convertTaskDetailPageInfo(totalSize, pageParam.getPageNum(), pageParam.getPageSize(), pageDataList, queryDetailList, evalTask);
    }

    @Override
    public PageData<TaskDTO> pageTask(PageParam<TaskConditionParam> pageParam) {
        if (pageParam.getCondition().getTaskTypes().contains(TaskTypeEnum.INSPECT.getCode())) {
            AutoInspectDetailConditionParam autoInspectDetailConditionParam = new AutoInspectDetailConditionParam();
            autoInspectDetailConditionParam.setPlatformApp(pageParam.getCondition().getAidaApplicationId());
            List<AutoInspectDetailPo> autoInspectDetailPos = autoInspectDetailGeneratorService.getByCondition(autoInspectDetailConditionParam);
            if (CollectionUtils.isEmpty(autoInspectDetailPos)) {
                return PageData.emptyData(pageParam.getPageNum(), pageParam.getPageSize());
            }
            int totalSize = autoInspectDetailPos.size();
            List<AutoInspectDetailPo> pageDataList = PageData.page(pageParam.getPageNum(), pageParam.getPageSize(), autoInspectDetailPos);
            return convertInspectTaskPageInfo(totalSize, pageParam.getPageNum(), pageParam.getPageSize(), pageDataList, pageParam.getCondition());
        }
        ZebraForceMasterHelper.forceMasterInLocalContext();
        List<EvalTaskPo> taskList = evalTaskGeneratorService.getByCondition(pageParam.getCondition());
        ZebraForceMasterHelper.clearLocalContext();
        if (CollectionUtils.isEmpty(taskList)) {
            return PageData.emptyData(pageParam.getPageNum(), pageParam.getPageSize());
        }
        // 先把自动任务里的人工任务显示出来
        //taskList = taskList.stream().filter(this::filterAutoTask).collect(Collectors.toList());
        int totalSize = taskList.size();
        List<EvalTaskPo> pageDataList = PageData.page(pageParam.getPageNum(), pageParam.getPageSize(), taskList);
        return convertTaskPageInfo(totalSize, pageParam.getPageNum(), pageParam.getPageSize(), pageDataList, pageParam.getCondition());
    }

    private List<TaskDTO> buildTaskDTOFromInspectDetail(List<AutoInspectDetailPo> pos) {
        List<TaskDTO> taskDTOs = new ArrayList<>();
        List<Long> datasetIds = pos.stream().map(AutoInspectDetailPo::getDatasetId).collect(Collectors.toList());
        List<EvalDatasetPo> datasetPos = evalDatasetGeneratorService.getByIdList(datasetIds);
        Map<Long, String> datasetIdToName = datasetPos.stream().collect(Collectors.toMap(EvalDatasetPo::getId, EvalDatasetPo::getName));
        pos.forEach(po -> {
            TaskDTO taskDTO = new TaskDTO();
            taskDTO.setId(0L);
            taskDTO.setName("--");
            taskDTO.setDataSetName(datasetIdToName.get(po.getDatasetId()));
            // 解析applicationName
            JSONObject jsonObject = JSON.parseObject(po.getExtra());
            JSONArray aidaModelConfigArray = jsonObject.getJSONArray("aidaModelConfig");
            if (aidaModelConfigArray != null && !aidaModelConfigArray.isEmpty()) {
                JSONObject firstConfig = aidaModelConfigArray.getJSONObject(0);
                String applicationName = firstConfig.getString("applicationName");
                taskDTO.setModelName(applicationName);
            } else {
                taskDTO.setModelName("--");
            }
            taskDTO.setStatus(transferStatusAutoInspectTask(po.getStatus()));
            taskDTO.setCreatorMis(po.getCreatorMis());
            taskDTO.setCreateTime(DateUtil.format(po.getGmtCreated(), null));
            taskDTO.setUpdateTime(DateUtil.format(po.getGmtModified(), null));
            taskDTO.setProgress("--");
            taskDTO.setScore(0);
            taskDTOs.add(taskDTO);
        });
        return taskDTOs;
    }

    private Integer transferStatusAutoInspectTask(int code) {
        if (code == AutoInspectEvalStatusEnum.FAILED.getCode()) {
            return AutoTaskStatusEnum.FAILED.getCode();
        }
        return AutoTaskStatusEnum.EVALUATING.getCode();
    }


    @Override
    public void deleteTask(Long taskId) {
        EvalTaskPo evalTaskPo = evalTaskGeneratorService.getById(taskId);
        CommonUtils.checkEval(evalTaskPo != null, "任务不存在");
        permissionService.checkPermission(evalTaskPo.getCreatorMis(), "没有删除任务权限");
        if (AutoTaskStatusEnum.EVALUATING.getCode() == evalTaskPo.getStatus()) {
            commonEvalStrategyService.setInterruptCache(taskId);
        }
        evalTaskPo.setIsDeleted(Boolean.TRUE);
        evalTaskPo.setGmtModified(new Date());
        evalTaskPo.setUpdaterMis(UserUtils.getUser().getLogin());
        evalTaskGeneratorService.updateById(evalTaskPo);
        // 删除关联的标注任务
        MarkTaskConditionParam condition = new MarkTaskConditionParam();
        condition.setDataSourceId(taskId);
        List<ManualMarkTaskPo> markTaskList = manualMarkTaskGeneratorService.getByCondition(condition);
        if (CollectionUtils.isNotEmpty(markTaskList)) {
            markTaskList.forEach(markTask -> markTask.setStatus(MarkTaskStatusEnum.DELETED.getCode()));
            manualMarkTaskGeneratorService.updateBatchById(markTaskList);
        }
    }

    @Override
    public TaskDTO overviewTask(Long taskId) {
        EvalTaskPo task = evalTaskGeneratorService.getById(taskId);
        CommonUtils.checkEval(task != null, "任务不存在");
        TaskDTO taskDTO = new TaskDTO();
        taskDTO.setId(taskId);
        taskDTO.setName(task.getName());
        taskDTO.setStatus(task.getStatus());
        taskDTO.setCreatorMis(task.getCreatorMis());
        taskDTO.setCreateTime(DateUtil.format(task.getGmtCreated(), null));
        if (EMPTY.equals(task.getDatasetIds())) {
            taskDTO.setDatasetList(new ArrayList<>());
            taskDTO.setDataSetName(EMPTY);
        } else {
            List<Long> datasetIdList = Arrays.stream(task.getDatasetIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            List<EvalDatasetPo> datasetList = evalDatasetGeneratorService.getByIdList(datasetIdList);
            taskDTO.setDatasetList(datasetList.stream().map(dataset -> {
                DatasetDTO dto = new DatasetDTO();
                dto.setId(dataset.getId());
                dto.setName(dataset.getName());
                return dto;
            }).collect(Collectors.toList()));
            taskDTO.setDataSetName(StringUtils.join(datasetList.stream().map(EvalDatasetPo::getName).toArray(), ","));
            List<ApplicationOutputDTO> outputKeyMap = Lists.newArrayList();
            for (EvalDatasetPo dataset : datasetList) {
                DatasetExtraParam datasetExtraParam = JSON.parseObject(dataset.getExtra(), DatasetExtraParam.class);
                if (null == datasetExtraParam || CollectionUtils.isEmpty(datasetExtraParam.getMapList())) {
                    continue;
                }
                ApplicationOutputDTO applicationOutputDTO = new ApplicationOutputDTO();
                applicationOutputDTO.setApplicationName(dataset.getName());
                applicationOutputDTO.setOutputKeyList(datasetExtraParam.getMapList()
                        .stream()
                        .filter(fieldBindDTO -> fieldBindDTO.getColumnType().equals(DataTypeEnum.OUTPUT.getCode()))
                        .map(SingleTemplateFieldBindDTO::getColumnName)
                        .collect(Collectors.toList()));
                outputKeyMap.add(applicationOutputDTO);

            }
            taskDTO.setOutputKeyMap(outputKeyMap);
        }
        // 设置模型名称和版本
        //application
        Map<Long, ApplicationConfigPo> applicationConfigMap = getApplicationMap(Collections.singletonList(task));
        setModelNameAndModelVersion(task, taskDTO, getAidaVersionMap(), applicationConfigMap);
        List<TaskDetailCountDTO> queryCountList = evalTaskQueryGeneratorService.getQueryCount(Collections.singletonList(taskId), TaskQueryStatusEnum.COMPLETE_STATUS);
        Map<Long, TaskDetailCountDTO> taskQueryMap = Optional.ofNullable(queryCountList).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(TaskDetailCountDTO::getTaskId,Function.identity(),(k1,k2)->k1));
        Map<Long, TaskDetailCountDTO> sessionMap = getSessionMap(Collections.singletonList(taskId));
        taskDTO.setProgress(buildProgress(task, taskQueryMap.get(taskId), sessionMap.get(taskId)));
        List<Long> metricList = Arrays.stream(task.getMetrics().split(",")).map(Long::parseLong).collect(Collectors.toList());
        StringBuilder standard = new StringBuilder();
        List<MetricConfigPo> metricCList = metricConfigGeneratorService.getByIdList(metricList);
        Map<Long, MetricConfigPo> metricConfigMap = metricCList.stream().collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));
        for (Long metric : metricList) {
            standard.append(metricConfigMap.get(metric).getName()).append(":\r\n").append(metricConfigMap.get(metric).getStandard()).append("\r\n");
        }
        taskDTO.setCallType(task.getCallType());
        taskDTO.setStandard(standard.toString());
        taskDTO.setAbilityType(AbilityEnum.parse(task.getAbility()).getCode());
        InspectInfo inspectInfo = TransferUtil.getInspectInfo(task.getExtra());
        taskDTO.setSamplingRatio(inspectInfo == null ? null : inspectInfo.getInspectRatio() + "%");
        taskDTO.setInspectProgress(inspectInfo == null || inspectInfo.getInspectCount() == null ? null : getInspectCount(taskId) + "/" + inspectInfo.getInspectCount());
        taskDTO.setCanInspect(getUserInspectPermission(task));
        taskDTO.setMetricList(buildMetricList(task));

        return taskDTO;
    }

    /**
     * 设置模型名称和版本
     *
     * @param task           任务对象
     * @param taskDTO        任务数据传输对象
     * @param aidaVersionMap 模型版本映射表
     */
    private void setModelNameAndModelVersion(EvalTaskPo task, TaskDTO taskDTO, Map<String, String> aidaVersionMap, Map<Long, ApplicationConfigPo> applicationConfigMap) {
        if (TaskTypeEnum.ARENA.getCode() == task.getType()) {
            return;
        }
        List<String> applicationInfoList = JSON.parseArray(task.getApplicationConfig(), String.class);

        List<String> nameList = new ArrayList<>();
        List<String> versionList = new ArrayList<>();
        for (String info : applicationInfoList) {
            Long id = DataConvertUtil.tryConvertLongWithNull(info);
            if (id == null) {
                nameList.add(info);
                versionList.add("-");
                continue;
            }

            // 填充模型名称和版本
            if (MapUtils.isNotEmpty(applicationConfigMap)) {
                ApplicationConfigPo applicationConfigPo = applicationConfigMap.get(id);
                nameList.add(applicationConfigPo.getName());
                versionList.add(aidaVersionMap == null ? "-" : aidaVersionMap.getOrDefault(applicationConfigPo.getRobotId(), "-"));
            }
        }
        taskDTO.setModelName(String.join(",", nameList));
        taskDTO.setVersion(String.join(",", versionList));
    }

    /**
     * 获取当前用户可用的模型版本映射表
     *
     * @return 模型版本映射表
     */
    private Map<String, String> getAidaVersionMap() {
        SpaceResDTO spacesAndApps = aidaInvokeServiceProxy.getSpacesAndApps(UserUtils.getUser().getLogin());
        return Optional.ofNullable(spacesAndApps)
                .map(SpaceResDTO::getData)
                .filter(CollectionUtils::isNotEmpty)
                .map(dataList -> dataList.stream()
                        .flatMap(data -> data.getApps().stream())
                        .flatMap(app -> app.getAppConfigVersions().stream())
                        .collect(Collectors.toMap(
                                AppConfigVersionDTO::getId,
                                AppConfigVersionDTO::getName,
                                (existingValue, newValue) -> existingValue // 处理重复key，保留现有值
                        ))
                )
                .orElse(Collections.emptyMap()); // 如果没有数据，则返回空的映射表
    }

    public List<EvalDatasetDetailPo> bindField(TaskParam param, List<EvalDatasetDetailPo> datasetDetailList, String createMis) {
        CommonUtils.checkEval(param.getDatasetIdList().size() == 1, "当前字段映射配置功能仅支持单数据集");
        Map<Long, List<TemplateFieldBindDTO>> bindFieldMap = param.getBindFields();
        List<Long> datasetIdList = param.getDatasetIdList();
        Map<Long, List<EvalDatasetDetailPo>> datasetDetailMap = datasetDetailList.stream().collect(Collectors.groupingBy(EvalDatasetDetailPo::getDatasetId));
        List<Long> applicationIdList = buildApplication(param.getModelSource(), param.getAidaModelConfig(), param.getApplicationConfig(), createMis);
        ApplicationInfoDTO applicationInfo = getApplicationInfoList(applicationIdList);
        AbilityEnum abilityEnum = AbilityEnum.parse(param.getAbilityType());
        for (Long datasetId : datasetIdList) {
            EvalDatasetPo evalDatasetPo = evalDatasetGeneratorService.getById(datasetId);
            Map<String, String> fieldKeyMap = new HashMap<>();
            if (Objects.nonNull(evalDatasetPo) && StringUtils.isNotBlank(evalDatasetPo.getExtra())) {
                DatasetExtraParam datasetExtraParam = JSON.parseObject(evalDatasetPo.getExtra(), DatasetExtraParam.class);

                if (Objects.nonNull(datasetExtraParam)) {
                    // 填充映射关系 ，在datasetExecuteService.parseDatasetDetail  里面映射字段用
                    fieldKeyMap.putAll(Optional.ofNullable(datasetExtraParam.getHeadMapList()).orElse(Collections.emptyList()).stream().filter(Objects::nonNull).collect(Collectors.toMap(BaseTemplateFieldBindDTO::getColumnName, BaseTemplateFieldBindDTO::getColumnKey, (k1, k2) -> k1)));
                    param.setFieldMap(fieldKeyMap);
                }
            }

            List<EvalDatasetDetailPo> detailList = datasetDetailMap.get(datasetId);
            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }
            List<TemplateFieldBindDTO> bindFieldList = bindFieldMap.get(datasetId);
            CommonUtils.checkEval(CollectionUtils.isNotEmpty(bindFieldList), "存在数据集未配置字段映射关系");
            // 校验必填项 如果是回归测评 无需校验
            if (!param.getWhetherRegressDataset()) {
                checkFields(abilityEnum, param.getInputSource(), param.getCallType(), applicationInfo, bindFieldList, applicationIdList);
            }
            // 解析映射关系配置的数据
            datasetExecuteService.parseDatasetDetail(abilityEnum, detailList, bindFieldList, param);
        }
        // 用户模拟器绑定字段
        if (MapUtils.isNotEmpty(param.getRobotMockBindFields())) {
            Long robotMockApplicationId = getRobotApplicationIdByMockId(param.getRobotMockId());
            ApplicationInfoDTO robotMockApplicationInfo = getApplicationInfoList(Collections.singletonList(robotMockApplicationId));
            for (Long datasetId : datasetIdList) {
                List<EvalDatasetDetailPo> detailList = datasetDetailMap.get(datasetId);
                if (CollectionUtils.isEmpty(detailList)) {
                    continue;
                }
                List<TemplateFieldBindDTO> bindFieldList = param.getRobotMockBindFields().get(datasetId);
                Map<String, TemplateFieldBindDTO> bindFieldConfigMap = bindFieldList.stream().collect(Collectors.toMap(templateFieldBindDTO -> {
                    if (TemplateFieldEnum.PARAMS.getCode().equals(templateFieldBindDTO.getFieldCode()) || TemplateFieldEnum.REPLY.getCode().equals(templateFieldBindDTO.getFieldCode())) {
                        return templateFieldBindDTO.getFieldCode() + templateFieldBindDTO.getFieldKey();
                    }
                    return templateFieldBindDTO.getFieldCode();
                }, Function.identity()));
                CommonUtils.checkEval(CollectionUtils.isNotEmpty(bindFieldList), "存在数据集未配置字段映射关系");
                // 校验必填项
                if (CollectionUtils.isNotEmpty(robotMockApplicationInfo.getApplicationParamList())) {
                    for (ApplicationInfoDTO.Param applicationParam : robotMockApplicationInfo.getApplicationParamList()) {
                        if (applicationParam.getIsRequire() != null && applicationParam.getIsRequire()) {
                            CommonUtils.checkEval(isExistField(TemplateFieldEnum.PARAMS, bindFieldConfigMap, applicationParam.getFieldCode()), "变量【" + applicationParam.getFieldName() + "】必填");
                        }
                    }
                }
                // 解析映射关系配置的数据
                datasetExecuteService.parseDatasetDetail(abilityEnum, detailList, bindFieldList, param);
            }
        }
        return datasetDetailList;
    }

    private Long getRobotApplicationIdByMockId(Long robotMockId) {
        return Lion.getMap(ConfigUtil.getAppkey(), LionConstants.EVAL_ROBOT_MOCK_LIST, ApplicationDTO.class).get(robotMockId.toString()).getId();
    }

    private List<Long> buildApplication(Integer applicationSource, List<AidaModelConfig> aidaModelConfigList, List<Long> applicationConfig, String createMis) {
        // 兼容前端旧逻辑，下次上线可去掉
        if (applicationSource == null) {
            return new ArrayList<>();
        }
        if (applicationSource == TaskModelSourceEnum.APPLICATION.getCode()) {
            return applicationConfig;
        }
        List<Long> applicationIdList = new ArrayList<>();
        for (AidaModelConfig aidaModelConfig : aidaModelConfigList) {
            ApplicationParam applicationParam = new ApplicationParam();
            applicationParam.setName("ai搭机器人:" + aidaModelConfig.getModelConfigVersionId());
            applicationParam.setSource(ApplicationSourceEnum.VIRTUAL.getCode());
            applicationParam.setCreateMis(createMis);
            applicationParam.setAidaModelConfig(aidaModelConfig);
            ApplicationConfigPo applicationConfigPo = applicationExecuteService.getOrCreateFromThirdSystem(applicationParam);
            applicationIdList.add(applicationConfigPo.getId());
        }
        return applicationIdList;
    }

    private void checkFields(AbilityEnum abilityEnum, Integer inputSource, Integer callType, ApplicationInfoDTO applicationInfo, List<TemplateFieldBindDTO> bindFieldsConfig, List<Long> applicationIdList) {
        CommonUtils.checkEval(org.apache.commons.collections4.CollectionUtils.isNotEmpty(bindFieldsConfig), "字段绑定配置不能为空");
        Map<String, TemplateFieldBindDTO> bindFieldConfigMap = bindFieldsConfig.stream().collect(Collectors.toMap(templateFieldBindDTO -> {
            if (TemplateFieldEnum.PARAMS.getCode().equals(templateFieldBindDTO.getFieldCode())
                    || TemplateFieldEnum.REPLY.getCode().equals(templateFieldBindDTO.getFieldCode())
                    || TemplateFieldEnum.EXPECT.getCode().equals(templateFieldBindDTO.getFieldCode())) {
                return templateFieldBindDTO.getFieldCode() + templateFieldBindDTO.getFieldKey();
            }
            return templateFieldBindDTO.getFieldCode();
        }, Function.identity()));
        // 多轮会话的会话ID必填
        if (abilityEnum == AbilityEnum.MULTI_ROUND) {
            CommonUtils.checkEval(isExistField(TemplateFieldEnum.SESSION_ID, bindFieldConfigMap), "多轮会话的会话ID必填");
            // 数据集方式，在多轮会话场景下输入内容必填
            if ((inputSource == null || inputSource == TaskInputSourceEnum.DATASET.getCode())) {
                CommonUtils.checkEval(isExistField(TemplateFieldEnum.INPUT, bindFieldConfigMap), "多轮会话的输入内容必填");
            }
        }

        // 数据集方式的预期输出必填
        if (inputSource == null || inputSource == TaskInputSourceEnum.DATASET.getCode()) {
            CommonUtils.checkEval(isExistField(TemplateFieldEnum.EXPECT, bindFieldConfigMap), "预期结果必填");
        }
        // 模拟场景的对话摘要必填
        if (inputSource != null && inputSource != TaskInputSourceEnum.DATASET.getCode()) {
            CommonUtils.checkEval(isExistField(TemplateFieldEnum.SUMMARY, bindFieldConfigMap), "模拟场景下对话摘要必填");
        }
        // 离线模式的输出内容必填且必须与应用数量一致
        if (callType == CallTypeEnum.ONLINE.getCode()) {
            for (Long applicationId : applicationIdList) {
                CommonUtils.checkEval(isExistField(TemplateFieldEnum.REPLY, bindFieldConfigMap, String.valueOf(applicationId)), "在线模式输出内容必填");
            }
            long outputSize = bindFieldsConfig.stream()
                    .filter(templateFieldBindDTO -> templateFieldBindDTO.getFieldCode().equals(TemplateFieldEnum.REPLY.getCode()) && StringUtils.isNotBlank(templateFieldBindDTO.getColumnName()))
                    .count();
            CommonUtils.checkEval(outputSize == applicationIdList.size(), "存在没有配置输出内容的应用");
        } else {
            // 在线模式的变量必须遵守应用的必填规则, 拆分后分为系统变量和应用变量，这里获取应用变量
            if (CollectionUtils.isNotEmpty(applicationInfo.getApplicationParamList())) {
                for (ApplicationInfoDTO.Param applicationParam : applicationInfo.getApplicationParamList()) {
                    if (applicationParam.getIsRequire() != null && applicationParam.getIsRequire()) {
                        CommonUtils.checkEval(isExistField(TemplateFieldEnum.PARAMS, bindFieldConfigMap, applicationParam.getFieldCode()), "变量【" + applicationParam.getFieldName() + "】必填");
                    }
                }
            }
        }
    }

    private ApplicationInfoDTO getApplicationInfoList(List<Long> applicationIdList) {
        return getApplicationInfoList(applicationIdList, null, null);
    }

    private ApplicationInfoDTO getApplicationInfoList(List<Long> applicationIdList, Integer uploadType, String createMis) {
        DatasetApplicationParam applicationInfoParam = new DatasetApplicationParam();
        applicationInfoParam.setApplicationSource(TaskModelSourceEnum.APPLICATION.getCode());
        applicationInfoParam.setApplicationIdList(applicationIdList);
        applicationInfoParam.setUploadType(uploadType);
        applicationInfoParam.setCreateMis(createMis);
        ZebraForceMasterHelper.forceMasterInLocalContext();
        ApplicationInfoDTO applicationInfo = applicationExecuteService.getDatasetApplicationInfo(applicationInfoParam);
        ZebraForceMasterHelper.clearLocalContext();
        return applicationInfo;
    }

    private boolean isExistField(TemplateFieldEnum templateFieldEnum, Map<String, TemplateFieldBindDTO> bindFieldConfigMap) {
        return isExistField(templateFieldEnum, bindFieldConfigMap, null);
    }

    private boolean isExistField(TemplateFieldEnum templateFieldEnum, Map<String, TemplateFieldBindDTO> bindFieldConfigMap, String paramCode) {
        String fieldCode = TemplateFieldEnum.PARAMS.equals(templateFieldEnum) || TemplateFieldEnum.REPLY.equals(templateFieldEnum) || TemplateFieldEnum.EXPECT.equals(templateFieldEnum) ? templateFieldEnum.getCode() + paramCode : templateFieldEnum.getCode();
        TemplateFieldBindDTO templateFieldBindDTO = bindFieldConfigMap.get(fieldCode);
        if (templateFieldBindDTO != null) {
            return StringUtils.isNotBlank(templateFieldBindDTO.getColumnName());
        }
        return false;
    }

    /**
     * 构建任务进度
     * @param task          任务
     * @param taskQueryList 任务query列表
     * @param sessionList   任务会话列表
     * @return
     */
    @Override
    public String buildProgress(EvalTaskPo task, TaskDetailCountDTO taskQueryList, TaskDetailCountDTO sessionList) {
        // 基础进度字符串构建
        String baseProgress = Objects.isNull(taskQueryList) || 0 == taskQueryList.getTotalCount() ? "0/0" :
                taskQueryList.getCompletedCount() + "/" + taskQueryList.getTotalCount();

        // 如果不是多轮对话能力，直接返回基础进度
        if (task.getAbility() == null || !AbilityEnum.MULTI_ROUND.getName().equals(task.getAbility())) {
            return baseProgress;
        }

        // 构建Session进度字符串
        String sessionProgress = Objects.isNull(sessionList) || 0 == sessionList.getTotalCount() ? "0/0" :
                sessionList.getCompletedCount() + "/" + sessionList.getTotalCount() + "（Session）";

        // 如果是手动模拟输入源，只返回Session进度
        if (task.getInputSource() != null && task.getInputSource() == TaskInputSourceEnum.MANUAL_MOCK.getCode()) {
            return sessionProgress;
        }

        // 返回Query和Session的完整进度
        return baseProgress + "（Query），" + sessionProgress;
    }
    private String getSessionProgress(List<EvalTaskSessionPo> sessionList) {
        if (CollectionUtils.isEmpty(sessionList)) {
            return "0/0";
        }
        int sessionTotalSize = sessionList.size();
        int sessionCompletionSize = (int) sessionList.stream().filter(evalTaskSessionPo -> evalTaskSessionPo.getStatus() == null || TaskSessionStatusEnum.COMPLETE_STATUS.contains(evalTaskSessionPo.getStatus())).count();
        return sessionCompletionSize + "/" + sessionTotalSize;
    }

    private String getQueryProgress(List<EvalTaskQueryPo> taskQueryList) {
        int totalSize = CollectionUtils.isNotEmpty(taskQueryList) ? taskQueryList.size() : 0;
        int completionSize = CollectionUtils.isNotEmpty(taskQueryList) ? (int) taskQueryList.stream().filter(evalTaskQueryPo -> evalTaskQueryPo.getStatus() == null || TaskQueryStatusEnum.COMPLETE_STATUS.contains(evalTaskQueryPo.getStatus())).count() : 0;
        return totalSize == 0 ? "0/0" : completionSize + "/" + totalSize;
    }

    private List<TaskMetricInfoDTO> buildMetricList(EvalTaskPo evalTaskPo) {
        if (evalTaskPo.getType() != null && TaskTypeEnum.ARENA.getCode() == evalTaskPo.getType()) {
            return new ArrayList<>();
        }
        List<Long> metricIdList = Arrays.stream(evalTaskPo.getMetrics().split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<MetricConfigPo> metricConfigPoList = metricConfigGeneratorService.getByIdList(metricIdList);
        List<ScoreThresholdParam> scoreThresholdList = parseScoreThresholdList(evalTaskPo);
        List<TaskMetricInfoDTO> metricInfoDTOList = new ArrayList<>();
        for (MetricConfigPo metricConfigPo : metricConfigPoList) {
            TaskMetricInfoDTO taskMetricInfoDTO = new TaskMetricInfoDTO();
            taskMetricInfoDTO.setMetricId(metricConfigPo.getId().intValue());
            taskMetricInfoDTO.setMetricName(metricConfigPo.getName());
            taskMetricInfoDTO.setMetricType(metricConfigPo.getMetricType());
            JSONObject jsonObject = JSON.parseObject(metricConfigPo.getRanges());
            Object range;
            if (metricConfigPo.getMetricType() == MetricTypeEnum.NUMBER.getCode()) {
                range = commonEvalStrategyService.getScoreThreshold(metricConfigPo.getId().intValue(), commonEvalStrategyService.getAvgEvalScore(scoreThresholdList, metricConfigPo.getId().intValue()), metricConfigPo);
            } else {
                ScoreThresholdParam scoreThresholdParam = commonEvalStrategyService.getScoreThresholdParam(scoreThresholdList, metricConfigPo.getId().intValue());
                if (scoreThresholdParam == null || CollectionUtils.isEmpty(scoreThresholdParam.getScoreThresholdList())) {
                    range = new ArrayList<>();
                } else {
                    range = scoreThresholdParam.getScoreThresholdList();
                }
            }
            taskMetricInfoDTO.setRange(range);
            if (jsonObject.containsKey("inspectReference")) {
                taskMetricInfoDTO.setInspectReference((Map<String, String>) jsonObject.get("inspectReference"));
            } else {
                taskMetricInfoDTO.setInspectReference(new HashMap<>());
            }
            metricInfoDTOList.add(taskMetricInfoDTO);
        }
        return metricInfoDTOList;
    }

    private boolean getUserInspectPermission(EvalTaskPo task) {
        if (!AutoTaskStatusEnum.CAN_INSPECT_STATUS.contains(task.getStatus())) {
            return false;
        }
        Set<String> inspectors = Arrays.stream(task.getInspectors().split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        //如果质检人列表为空，则允许进行抽样
        if (CollectionUtils.isEmpty(inspectors)) {
            return true;
        }
        return inspectors.contains(UserUtils.getUser().getLogin());
    }

    @Override
    public String downloadTaskDetail(TaskQueryConditionParam conditionParam) {
        EvalTaskPo task = evalTaskGeneratorService.getById(conditionParam.getTaskId());
        List<Long> metricIdList = Arrays.stream(task.getMetrics().split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricIdList);
        List<Long> queryMetricIdList = metricConfigList.stream().filter(metricConfig -> metricConfig.getDimension() == null || metricConfig.getDimension() == MetricDimensionEnum.QUERY.getCode()).map(MetricConfigPo::getId).collect(Collectors.toList());
        List<Long> sessionMetricIdList = metricConfigList.stream().filter(metricConfig -> metricConfig.getDimension() != null && metricConfig.getDimension() == MetricDimensionEnum.SESSION.getCode()).map(MetricConfigPo::getId).collect(Collectors.toList());
        List<TaskDetailDTO> taskDetailList = getTaskDetailInfo(conditionParam);
        if (TaskTypeEnum.TRAIN_MANUAL_ANNOTATION.getCode() == task.getType()) {
            return downManualAnnotation(conditionParam, taskDetailList, metricIdList, task);
        }
        return down(conditionParam, taskDetailList, queryMetricIdList, sessionMetricIdList, metricConfigList, task);


    }

    private String downManualAnnotation(TaskQueryConditionParam conditionParam, List<TaskDetailDTO> taskDetailList, List<Long> metricIdList, EvalTaskPo task) {
        List<ExcelService.ExcelData> excelDataList = new ArrayList<>();
        List<List<String>> queryData = new ArrayList<>();
        // 展开变量
        List<String> paramNameList = getParamNameList(task);

        // 获取数据表头, 获取不到表头的就不添加
        String datasetIds = task.getDatasetIds();
        List<String> datasetHeadList = new ArrayList<>();
        if (TaskTypeEnum.TRAIN_MANUAL_ANNOTATION.getCode() == task.getType()) {
            EvalTrainDatasetVersionPo trainDatasetVersionPo = evalTrainDatasetVersionGeneratorService.getEvalTrainDatasetByTaskId(task.getId());
            if (StringUtils.isNotBlank(trainDatasetVersionPo.getExtra())) {
                datasetHeadList.addAll(Optional.ofNullable(JSONArray.parseArray(JSONObject.parseObject(trainDatasetVersionPo.getExtra()).getString("headList"), String.class)).orElse(Collections.emptyList()));
                datasetHeadList.addAll(Optional.ofNullable(JSONArray.parseArray(JSONObject.parseObject(trainDatasetVersionPo.getExtra()).getString("customList"), String.class)).orElse(Collections.emptyList()));
            }
        } else {
            EvalDatasetPo dataset = evalDatasetGeneratorService.getById(datasetIds);
            if (StringUtils.isNotBlank(dataset.getExtra())) {
                datasetHeadList.addAll(Optional.ofNullable(JSONArray.parseArray(JSONObject.parseObject(dataset.getExtra()).getString("headList"), String.class)).orElse(Collections.emptyList()));
                datasetHeadList.addAll(Optional.ofNullable(JSONArray.parseArray(JSONObject.parseObject(dataset.getExtra()).getString("customList"), String.class)).orElse(Collections.emptyList()));
            }
        }
        List<List<String>> queryHeadList = buildQueryHeadList(paramNameList, datasetHeadList.stream().distinct().collect(Collectors.toList()), task);
        List<EvalTaskQueryPo> taskQueryPos = evalTaskQueryGeneratorService.getByTaskIdList(Collections.singletonList(conditionParam.getTaskId()));
        List<MetricConfigPo> metricConfigPos = metricConfigGeneratorService.getByIdList(metricIdList);

        List<String> metricHead = Lists.newArrayList();
        // 构建Query表头
        for (MetricConfigPo metricConfigPo : metricConfigPos) {
            parseMetricHead(queryHeadList, metricHead, metricConfigPo.getRanges());
        }
        List<String> misList = new ArrayList<>();
        Integer count = taskQueryPos.stream().map(taskQueryPo -> {
            Map<String, ScoreParam> scoreParamMap = JSONObject.parseObject(taskQueryPo.getMetricResult(), new TypeReference<Map<String, ScoreParam>>() {
            });
            if (MapUtils.isEmpty(scoreParamMap)) {
                return null;
            }
            scoreParamMap.forEach((k, v) -> misList.add(k));
            // 获取所有key的长度
            int maxSize = scoreParamMap.keySet().size();
            if (maxSize > 1) {
                return maxSize;
            } else {
                return 1;
            }

        }).filter(Objects::nonNull).max(Integer::compareTo).orElse(0);


        if (0 != count) {

            for (int i = 0; i < count; i++) {
                if (1 == count) {
                    queryHeadList.addAll(convertHeadList(Collections.singletonList("标注人")));
                } else {
                    queryHeadList.addAll(convertHeadList(Collections.singletonList("标注人-" + i)));
                }
                queryHeadList.addAll(convertHeadList(Collections.singletonList("标注时间")));
                queryHeadList.addAll(convertHeadList(metricHead));
            }
        }

        for (EvalTaskQueryPo evalTaskQueryPo : taskQueryPos) {
            queryData.add(buildQueryData(evalTaskQueryPo, queryHeadList, datasetHeadList, paramNameList, metricHead, misList));
        }
        if (CollectionUtils.isNotEmpty(queryData)) {
            excelDataList.add(new ExcelService.ExcelData(queryHeadList, queryData, "query维度指标结果"));
        }
        return excelService.manualAnnotationExcel(excelDataList, conditionParam.getTaskId() + "-" + System.currentTimeMillis() + ".xlsx", "任务【" + task.getName() + "】详情.xlsx", "xlsx", UserUtils.getUser().getLogin());
    }

    private void parseMetricHead(List<List<String>> queryHeadList, List<String> metricHead, String rangs) {
        if (StringUtils.isBlank(rangs)) {
            return;
        }
        JSONObject jsonObject = JSON.parseObject(rangs);
        ManualAnnotationParam manualAnnotationParam = JSONObject.parseObject(JSON.toJSONString(jsonObject.get("manualAnnotation")), new TypeReference<ManualAnnotationParam>() {
        });
        //ManualAnnotationParam manualAnnotationParam = (ManualAnnotationParam)content.get("manualAnnotation");
        //queryHeadList.addAll(convertHeadList(Collections.singletonList(manualAnnotationParam.getAnnotationName())));
        metricHead.add(manualAnnotationParam.getAnnotationName());
        getHead(queryHeadList, metricHead, manualAnnotationParam.getEnumValueList());
    }

    public void getHead(List<List<String>> rowHead, List<String> metricHead, List<EnumMetricParam> enumMetricParams) {
        if (CollectionUtils.isEmpty(enumMetricParams)) {
            return;
        }
        for (EnumMetricParam enumMetricParam : enumMetricParams) {
            if (Objects.nonNull(enumMetricParam) && Objects.nonNull(enumMetricParam.getMetrics())) {
                for (ChildMetricParam metric : enumMetricParam.getMetrics()) {
                    metricHead.add(metric.getChildManualAnnotation().getAnnotationName());
                    //  rowHead.addAll(convertHeadList(Collections.singletonList(metric.getChildManualAnnotation().getAnnotationName())));
                }
            }
            // 递归处理子节点
            List<ChildMetricParam> metrics = enumMetricParam.getMetrics();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(metrics)) {
                for (ChildMetricParam childMetricParam : metrics) {
                    // 递归处理子节点
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(childMetricParam.getChildManualAnnotation().getEnumValueList())) {
                        // rowHead.addAll(convertHeadList(Collections.singletonList(childMetricParam.getChildManualAnnotation().getAnnotationName())));
                        //  metricHead.add(childMetricParam.getChildManualAnnotation().getAnnotationName());
                        getHead(rowHead, metricHead, childMetricParam.getChildManualAnnotation().getEnumValueList());
                    }
                }
            }
        }
    }

    private List<String> buildQueryData(EvalTaskQueryPo evalTaskQueryPo, List<List<String>> queryHeadList, List<String> dataHeadList, List<String> paramNameList, List<String> metricHead, List<String> misList) {
        Map<String, ScoreParam> scoreParamMap = JSONObject.parseObject(evalTaskQueryPo.getMetricResult(), new TypeReference<Map<String, ScoreParam>>() {
        });
        List<String> rowData = new ArrayList<>();
        // 添加数据集列数据
        JSONObject parsed = JSONObject.parseObject(evalTaskQueryPo.getContent());
        dataHeadList.forEach(head -> rowData.add(parsed.getString(head)));
        // 添加Base列数据
        rowData.add(evalTaskQueryPo.getSessionId());
        rowData.add(evalTaskQueryPo.getInput());
        Map<String, String> params = Maps.newHashMap();
        if (StringUtils.isNotBlank(evalTaskQueryPo.getParams())) {
            Map<String, String> map = JSON.parseObject(evalTaskQueryPo.getParams(), new TypeReference<Map<String, String>>() {
            });
            params.putAll(map);
        }
        if (CollectionUtils.isNotEmpty(paramNameList)) {
            paramNameList.forEach(paramName -> rowData.add(MapUtils.isNotEmpty(params) && params.containsKey(paramName) ? params.get(paramName) : null));
        } else {
            rowData.add(MapUtils.isNotEmpty(params) ? JSON.toJSONString(evalTaskQueryPo.getParams()) : null);
        }
        rowData.add(evalTaskQueryPo.getStatus() == null ? null : TaskQueryStatusEnum.parse(evalTaskQueryPo.getStatus()).getName());
        if (MapUtils.isEmpty(scoreParamMap)) {
            return rowData;
        }
        List<String> miss = new ArrayList<>(scoreParamMap.keySet());

        for (int i = 0; i < miss.size(); i++) {
            ScoreParam scoreParam = scoreParamMap.get(miss.get(i));
            if (Objects.isNull(scoreParam)) {
                continue;
            }
            rowData.add(miss.get(i));
            rowData.add(scoreParam.getMarkTime());
            Integer index = 0;
            for (ChildMetricParam metric : scoreParam.getMetrics()) {
                index = parseMetric(rowData, metric, metricHead, index);
            }
        }
        return rowData;
    }

    public Integer parseMetric(List<String> rowData, ChildMetricParam metric, List<String> metricHead, int index) {
        if (Objects.isNull(metric)) {
            return index - 1;
        }
        // 根据 AnnotationType 添加值
        if (MetricAnnotationTypeEnum.ENUMERATION.getCode().equals(metric.getChildManualAnnotation().getAnnotationType())) {
            // 收集所有被选中的枚举值
            List<String> selectedValues = metric.getChildManualAnnotation().getEnumValueList().stream()
                    .filter(enumMetricParam -> Boolean.TRUE.equals(enumMetricParam.getSelected()))
                    .map(EnumMetricParam::getEnumValue)
                    .collect(Collectors.toList());
            // 仅当 selectedValues 不为空时添加到 rowData
            if (CollectionUtils.isNotEmpty(selectedValues)) {
                rowData.add(String.join(",", selectedValues));
            } else if (!metric.getSelected()) {
                rowData.add("");
            }
        } else {
            String input = metric.getChildManualAnnotation().getInput();
            // 仅当 input 不为空时添加到 rowData
            if (StringUtils.isNotBlank(input)) {
                rowData.add(input);
            } else {
                rowData.add("");
            }
        }

        // 递归处理子节点
        List<EnumMetricParam> enumValueList = metric.getChildManualAnnotation().getEnumValueList();
        if (CollectionUtils.isNotEmpty(enumValueList)) {
            for (EnumMetricParam enumMetricParam : enumValueList) {
                // 递归处理子节点，传递 currentIndex + 1
                for (ChildMetricParam enumMetricParamMetric : enumMetricParam.getMetrics()) {
                    index = parseMetric(rowData, enumMetricParamMetric, metricHead, index + 1);
                }
            }
        }
        return index;
    }

    private String down(TaskQueryConditionParam conditionParam, List<TaskDetailDTO> taskDetailList, List<Long> queryMetricIdList, List<Long> sessionMetricIdList, List<MetricConfigPo> metricConfigList, EvalTaskPo task) {

        List<ExcelService.ExcelData> excelDataList = new ArrayList<>();
        List<List<String>> queryData = new ArrayList<>();
        List<List<String>> sessionData = new ArrayList<>();
        // 展开变量
        List<String> paramNameList = getParamNameList(task);

        // 获取数据表头, 获取不到表头的就不添加
        String datasetIds = task.getDatasetIds();
        List<String> datasetHeadList = new ArrayList<>();
        Map<String, String> nameKeyMap = new HashMap<>();
        EvalDatasetPo dataset = evalDatasetGeneratorService.getById(datasetIds);
        if (TaskTypeEnum.TRAIN_MANUAL_ANNOTATION.getCode() == task.getType()) {
            EvalTrainDatasetVersionPo trainDatasetVersionPo = evalTrainDatasetVersionGeneratorService.getEvalTrainDatasetByTaskId(task.getId());
            if (StringUtils.isNotBlank(trainDatasetVersionPo.getExtra())) {
                datasetHeadList.addAll(Optional.ofNullable(JSONArray.parseArray(JSONObject.parseObject(trainDatasetVersionPo.getExtra()).getString("headList"), String.class)).orElse(Collections.emptyList()));
                datasetHeadList.addAll(Optional.ofNullable(JSONArray.parseArray(JSONObject.parseObject(trainDatasetVersionPo.getExtra()).getString("customList"), String.class)).orElse(Collections.emptyList()));
            }
        } else {
            if (StringUtils.isNotBlank(dataset.getExtra())) {
                DatasetExtraParam datasetExtraParam = JSON.parseObject(dataset.getExtra(), new TypeReference<DatasetExtraParam>() {
                });
                List<String> nameList = Optional.ofNullable(datasetExtraParam.getHeadMapList()).orElse(Collections.emptyList()).stream().map(SingleTemplateFieldBindDTO::getColumnName).collect(Collectors.toList());
                nameKeyMap.putAll(Optional.ofNullable(datasetExtraParam.getHeadMapList()).orElse(Collections.emptyList()).stream().filter(field -> StringUtils.isNotBlank(field.getColumnKey())).collect(Collectors.toMap(SingleTemplateFieldBindDTO::getColumnName, SingleTemplateFieldBindDTO::getColumnKey, (k1, k2) -> k1)));
                if (CollectionUtils.isNotEmpty(nameList)) {
                    datasetHeadList.addAll(nameList);
                    List<String> customList = Optional.ofNullable(JSONArray.parseArray(JSONObject.parseObject(dataset.getExtra()).getString("customList"), String.class)).orElse(Collections.emptyList());
                    datasetHeadList.addAll(customList);
                    customList.forEach(k -> nameKeyMap.put(k, k));
                } else {
                    datasetHeadList.addAll(Optional.ofNullable(JSONArray.parseArray(JSONObject.parseObject(dataset.getExtra()).getString("headList"), String.class)).orElse(Collections.emptyList()));
                    datasetHeadList.addAll(Optional.ofNullable(JSONArray.parseArray(JSONObject.parseObject(dataset.getExtra()).getString("customList"), String.class)).orElse(Collections.emptyList()));
                }
            }
        }
        // 构建Query表头 TODO 去重？
        List<List<String>> queryHeadList = buildQueryHeadList(paramNameList, datasetHeadList.stream().distinct().collect(Collectors.toList()), task);

        // 构建Session表头
        List<List<String>> sessionHeadList = convertHeadList(datasetHeadList);
        sessionHeadList.addAll(convertHeadList(SESSION_EXCEL_HEAD));

        if (CollectionUtils.isEmpty(taskDetailList)) {
            return generateEmptyExcel(conditionParam, excelDataList, queryData, sessionData, queryHeadList, sessionHeadList, task);
        }
        String[] applicationList = buildApplicationInfo(task).split(",");
        Map<Long, MetricConfigPo> metricConfigMap = metricConfigList.stream().collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));
        List<EvalTaskQueryDetailPo> queryDetailList = evalTaskQueryDetailGeneratorService.getByTaskId(conditionParam.getTaskId());
        Map<Integer, Set<String>> outputKeyMetricMap = queryDetailList.stream()
                .collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getMetricId, Collectors.mapping(EvalTaskQueryDetailPo::getOutputKey, Collectors.toSet())));
        List<String> uniqueAppMetricOutputKeyList = Lists.newArrayList();

        // 构建Query数据
        if (CollectionUtils.isNotEmpty(queryMetricIdList)) {
            // 设置评测详情表头
            addEvalHead(metricConfigMap, queryHeadList, applicationList, outputKeyMetricMap, uniqueAppMetricOutputKeyList, task, taskDetailList);
            // 多轮会话按照session进行排序
            if (!task.getAbility().equals(AbilityEnum.SINGLE_ROUND.getName())) {
                taskDetailList.sort(Comparator.comparing(o -> Long.parseLong(o.getConversationId())));
            }
            List<String> headList = datasetHeadList.stream().distinct().collect(Collectors.toList());
            // 填入数据
            taskDetailList.forEach(taskDetailDTO -> {

                queryData.add(buildQueryExcelData(taskDetailDTO, paramNameList, uniqueAppMetricOutputKeyList.stream().distinct().collect(Collectors.toList()), headList, nameKeyMap, dataset.getType()));
            });
        }

        // 构建Session数据
        if (CollectionUtils.isNotEmpty(sessionMetricIdList)) {
            // 设置评测详情表头
            addEvalHead(metricConfigMap, sessionHeadList, applicationList, outputKeyMetricMap, uniqueAppMetricOutputKeyList, task, taskDetailList);
            // 填入数据
            List<EvalTaskSessionPo> sessionList = evalTaskSessionGeneratorService.getByTaskId(conditionParam.getTaskId());
            Map<String, List<TaskDetailDTO>> taskDetailMap = taskDetailList.stream().collect(Collectors.groupingBy(TaskDetailDTO::getConversationId));
            if (CollectionUtils.isNotEmpty(sessionList)) {
                sessionList.forEach(sessionPo -> {
                    sessionData.addAll(buildSessionExcelData(sessionPo, taskDetailMap.get(String.valueOf(sessionPo.getId())), uniqueAppMetricOutputKeyList, datasetHeadList));
                });
            }
        }
        return generateExcel(conditionParam, excelDataList, queryData, sessionData, queryHeadList, sessionHeadList, task);
    }


    private void addEvalHead(Map<Long, MetricConfigPo> metricConfigMap, List<List<String>> headList, String[] applicationList, Map<Integer, Set<String>> metricOutputKeyMap, List<String> uniqueAppMetricOutputKeyList, EvalTaskPo task, List<TaskDetailDTO> taskDetailList) {
        for (String application : applicationList) {
            for (Map.Entry<Integer, Set<String>> outputKeyMetric : metricOutputKeyMap.entrySet()) {
                Integer metricId = outputKeyMetric.getKey();
                for (String outputKey : outputKeyMetric.getValue()) {
                    if (null == metricConfigMap.get(metricId.longValue())) {
                        continue;
                    }
                    String uniqueKey = application +
                            metricConfigMap.get(metricId.longValue()).getName() +
                            outputKey;
                    if (TaskTypeEnum.TRAIN_MANUAL_ANNOTATION.getCode() == task.getType()) {
                        List<String> manualMarkHeads = Optional.ofNullable(taskDetailList).orElse(Collections.emptyList())
                                .stream()
                                .map(TaskDetailDTO::getResult)
                                .filter(Objects::nonNull)
                                .flatMap(Collection::stream)
                                .map(TaskDetailDTO.EvalModelDTO::getData)
                                .filter(Objects::nonNull)
                                .flatMap(Collection::stream)
                                .map(TaskDetailDTO.EvalOutputDTO::getEval)
                                .flatMap(Collection::stream)
                                .map(TaskDetailDTO.EvalInfo::getHead)
                                .collect(Collectors.toList());
                        for (String manualMarkHead : manualMarkHeads) {
                            if (StringUtils.isBlank(manualMarkHead)) {
                                continue;
                            }
                            uniqueAppMetricOutputKeyList.add(application + manualMarkHead);
                        }
                    } else {
                        uniqueAppMetricOutputKeyList.add(uniqueKey);
                    }
                    if (TaskTypeEnum.TRAIN_MANUAL_ANNOTATION.getCode() != task.getType()) {
                        for (String column : METRIC_RESULT) {
                            String head = String.format("【%s】-【%s】-【%s】",
                                    application,
                                    StringUtils.isNotBlank(outputKey) ? outputKey + "-" + metricConfigMap.get(metricId.longValue()).getName() : metricConfigMap.get(metricId.longValue()).getName(),
                                    column);
                            headList.add(convertList(head));
                        }
                    }

                }
            }
        }

        if (TaskTypeEnum.TRAIN_MANUAL_ANNOTATION.getCode() == task.getType()) {
            headList.addAll(convertHeadList(Collections.singletonList("标注人")));
            headList.addAll(convertHeadList(Collections.singletonList("标注时间")));
            List<String> manualMarkHeads = Optional.ofNullable(taskDetailList).orElse(Collections.emptyList())
                    .stream()
                    .map(TaskDetailDTO::getResult)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .map(TaskDetailDTO.EvalModelDTO::getData)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .map(TaskDetailDTO.EvalOutputDTO::getEval)
                    .flatMap(Collection::stream)
                    .map(TaskDetailDTO.EvalInfo::getHead)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            Optional.ofNullable(convertHeadList(manualMarkHeads)).ifPresent(headList::addAll);
        }
    }

    private List<List<String>> buildSessionExcelData(EvalTaskSessionPo sessionPo, List<TaskDetailDTO> taskDetailList, List<String> uniqueAppMetricOutputKeyList, List<String> datasetHeadList) {
        List<List<String>> result = Lists.newArrayList();
        Map<String, List<TaskDetailDTO>> uniqueKeyDataMap = Maps.newHashMap();
        for (TaskDetailDTO taskDetailDTO : taskDetailList) {
            if (CollectionUtils.isNotEmpty(taskDetailDTO.getResult())) {
                for (TaskDetailDTO.EvalModelDTO evalModelDTO : taskDetailDTO.getResult()) {

                    String applicationName = evalModelDTO.getModelName();
                    if (CollectionUtils.isNotEmpty(evalModelDTO.getData())) {
                        List<TaskDetailDTO.EvalOutputDTO> evalOutputDTOS = evalModelDTO.getData();
                        for (TaskDetailDTO.EvalOutputDTO evalOutputDTO : evalOutputDTOS) {
                            if (CollectionUtils.isNotEmpty(evalOutputDTO.getEval())) {
                                for (TaskDetailDTO.EvalInfo evalInfo : evalOutputDTO.getEval()) {
                                    String metricName = evalInfo.getMetricName();
                                    String outputKey = evalInfo.getOutputKey();
                                    String appOutputMetricKey = applicationName + metricName + outputKey;
                                    uniqueKeyDataMap.computeIfAbsent(appOutputMetricKey, k -> Lists.newArrayList()).add(taskDetailDTO);
                                }
                            }
                        }
                    }
                }
            }
        }
        for (String uniqueKey : uniqueAppMetricOutputKeyList) {
            List<TaskDetailDTO> taskDetailDTOS = uniqueKeyDataMap.get(uniqueKey);
            for (TaskDetailDTO taskDetailDTO : taskDetailDTOS) {
                for (TaskDetailDTO.EvalModelDTO evalModelDTO : taskDetailDTO.getResult()) {
                    for (TaskDetailDTO.EvalOutputDTO datum : evalModelDTO.getData()) {
                        if (CollectionUtils.isNotEmpty(datum.getEval())) {
                            for (TaskDetailDTO.EvalInfo evalInfo : datum.getEval()) {
                                List<String> data = new ArrayList<>();
                                // 添加数据集数据
                                addDatasetData(taskDetailDTO, datasetHeadList, data, null);
                                data.add(taskDetailDTO.getInput());
                                String statusDescription = Optional.ofNullable(TaskSessionStatusEnum.getByCode(sessionPo.getStatus()))
                                        .map(TaskSessionStatusEnum::getDescription)
                                        .orElseGet(() -> String.format("未知状态(Code: %d)", sessionPo.getStatus()));
                                data.add(statusDescription);
                                data.add(evalInfo.getOutput());
                                data.add(evalInfo.getExpectOutput());
                                data.add(evalInfo.getResult());
                                data.add(evalInfo.getResultNote());
                                String inspectInfo = buildInspectInfo(evalInfo);
                                data.add(inspectInfo);
                                result.add(data);
                            }
                        }
                    }
                }
            }

        }

        return result;
    }

    private List<TaskDetailDTO> getTaskDetailInfo(TaskQueryConditionParam conditionParam) {
        List<EvalTaskQueryPo> taskQueryList = evalTaskQueryGeneratorService.getByCondition(conditionParam);
        List<EvalTaskQueryDetailPo> queryDetailList = evalTaskQueryDetailGeneratorService.getByTaskId(conditionParam.getTaskId());
        EvalTaskPo evalTask = evalTaskGeneratorService.getById(conditionParam.getTaskId());
        CommonUtils.checkEval(null != evalTask, "该评测任务不存在");
        return convertTaskDetailList(taskQueryList, queryDetailList, evalTask, null);
    }

    private String generateExcel(TaskQueryConditionParam conditionParam, List<ExcelService.ExcelData> excelDataList, List<List<String>> queryData, List<List<String>> sessionData, List<List<String>> queryHeadList, List<List<String>> sessionHeadList, EvalTaskPo taskPo) {
        if (CollectionUtils.isNotEmpty(queryData)) {
            excelDataList.add(new ExcelService.ExcelData(queryHeadList, queryData, "query维度指标结果"));
        }
        if (CollectionUtils.isNotEmpty(sessionData)) {
            excelDataList.add(new ExcelService.ExcelData(sessionHeadList, sessionData, "session维度指标结果"));
        }
        return excelService.generateMultiHeadExcelAndUpload(excelDataList, conditionParam.getTaskId() + "-" + System.currentTimeMillis() + ".xlsx", "任务【" + taskPo.getName() + "】详情.xlsx", "xlsx", UserUtils.getUser().getLogin());
    }

    private String generateEmptyExcel(TaskQueryConditionParam conditionParam, List<ExcelService.ExcelData> excelDataList, List<List<String>> queryData, List<List<String>> sessionData, List<List<String>> queryHeadList, List<List<String>> sessionHeadList, EvalTaskPo taskPo) {
        excelDataList.add(new ExcelService.ExcelData(queryHeadList, queryData, "query维度指标结果"));
        excelDataList.add(new ExcelService.ExcelData(sessionHeadList, sessionData, "session维度指标结果"));
        return excelService.generateMultiHeadExcelAndUpload(excelDataList, conditionParam.getTaskId() + "-" + System.currentTimeMillis() + ".xlsx", "任务【" + taskPo.getName() + "】详情.xlsx", "xlsx", UserUtils.getUser().getLogin());
    }

    private List<List<String>> buildQueryHeadList(List<String> paramNameList, List<String> datasetHeadList, EvalTaskPo task) {
        List<List<String>> queryHeadList = new ArrayList<>();
        // 添加数据集表头
        queryHeadList.addAll(convertHeadList(datasetHeadList));
        // 添加Base表头
        queryHeadList.addAll(convertHeadList(BASE_EXCEL_HEADER));

        if (CollectionUtils.isNotEmpty(paramNameList)) {
            queryHeadList.addAll(convertHeadList(paramNameList));
        } else {
            queryHeadList.add(convertList("变量"));
        }
        queryHeadList.addAll(convertHeadList(BASE_EXCEL_HEADER_RESULT));

        return queryHeadList;
    }

    @Override
    public TaskReportDTO taskReport(Long taskId) {
        EvalTaskPo taskPo = evalTaskGeneratorService.getById(taskId);
        CommonUtils.checkEval(taskPo != null && !taskPo.getIsDeleted(), "任务不存在或已经删除");
        List<EvalTaskQueryDetailPo> evalTaskQueryDetailList = evalTaskQueryDetailGeneratorService.getByTaskId(taskId);
        if (taskPo.getType().equals(TaskTypeEnum.ARENA.getCode())) {
            //过滤未评测的内容
            List<EvalTaskQueryDetailPo> filteredEvalTaskQueryDetailList = evalTaskQueryDetailList.stream().filter(po -> po.getStatus() == TaskQueryDetailStatusEnum.COMPLETED.getCode()).collect(Collectors.toList());
            return buildArenaTaskReport(taskPo, filteredEvalTaskQueryDetailList);
        }
        List<Long> datasetIdList = Arrays.stream(taskPo.getDatasetIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
        Map<Long, EvalDatasetPo> datasetMap = evalDatasetGeneratorService.getByIdList(datasetIdList).stream().collect(Collectors.toMap(EvalDatasetPo::getId, Function.identity()));
        return buildTaskReport(taskPo, evalTaskQueryDetailList, datasetMap);
    }

    @Override
    public PageData<TaskSessionResultDTO> pageSessionData(PageParam<TaskQuerySessionConditionParam> pageParam) {
        List<EvalTaskSessionPo> taskSessionList = evalTaskSessionGeneratorService.getByCondition(pageParam.getCondition());
        if (CollectionUtils.isEmpty(taskSessionList)) {
            return PageData.emptyData(pageParam.getPageNum(), pageParam.getPageSize());
        }
        TaskQueryDetailConditionParam detailConditionParam = new TaskQueryDetailConditionParam();
        detailConditionParam.setTaskId(pageParam.getCondition().getTaskId());
        detailConditionParam.setEvalTarget(pageParam.getCondition().getEvalTarget());
        detailConditionParam.setEvalResult(pageParam.getCondition().getEvalResult());
        detailConditionParam.setMetricId(pageParam.getCondition().getMetricId());
        detailConditionParam.setInspectionResult(pageParam.getCondition().getInspectionResult());
        List<EvalTaskQueryDetailPo> evalTaskQueryDetailList = evalTaskQueryDetailGeneratorService.getByCondition(detailConditionParam);
        Map<String, List<EvalTaskQueryDetailPo>> evalTaskQueryDetailMap = evalTaskQueryDetailList.stream().collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getConversationId));
        // 过滤质检人
        if (StringUtils.isNotBlank(pageParam.getCondition().getInspector())) {
            taskSessionList = taskSessionList.stream().filter(evalTaskSessionPo -> {
                List<EvalTaskQueryDetailPo> detailPoList = evalTaskQueryDetailMap.get(String.valueOf(evalTaskSessionPo.getId()));
                for (EvalTaskQueryDetailPo detailPo : detailPoList) {
                    if (pageParam.getCondition().getInspector().equals(detailPo.getInspectMis())) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
        }
        int totalSize = taskSessionList.size();
        taskSessionList = taskSessionList.stream().filter(taskSession -> filterSession(evalTaskQueryDetailMap, taskSession)).collect(Collectors.toList());
        List<EvalTaskSessionPo> resultSession = PageData.page(pageParam.getPageNum(), pageParam.getPageSize(), taskSessionList);
        return convertTaskSessionPageInfo(totalSize, pageParam, resultSession, evalTaskQueryDetailMap);
    }

    @Override
    public List<TaskDetailDTO> getSessionDetail(String conversationId, Long taskId, String sessionId, Long datasetId) {
        TaskQueryConditionParam condition = new TaskQueryConditionParam();
        if (StringUtils.isNotBlank(conversationId)) {
            condition.setConversationId(conversationId);
        } else {
            condition.setTaskId(taskId);
            condition.setDatasetId(datasetId);
            condition.setSessionId(sessionId);
        }
        List<EvalTaskQueryPo> evalTaskQueryList = evalTaskQueryGeneratorService.getByCondition(condition);
        List<EvalTaskQueryDetailPo> evalTaskQueryDetailList = new ArrayList<>();
        EvalTaskPo evalTask = null;
        if (CollectionUtils.isNotEmpty(evalTaskQueryList)) {
            List<Long> queryIdList = evalTaskQueryList.stream().map(EvalTaskQueryPo::getId).collect(Collectors.toList());
            evalTaskQueryDetailList = evalTaskQueryDetailGeneratorService.getByQueryIds(queryIdList);
            evalTask = evalTaskGeneratorService.getById(evalTaskQueryList.get(0).getTaskId());
            CommonUtils.checkEval(null != evalTask, "该评测任务不存在");
            // 填充outputKey中的null为完整输出
            evalTaskQueryDetailList.forEach(detail ->
                    detail.setOutputKey(Optional.ofNullable(detail.getOutputKey()).orElse(CommonConstants.COMPLETE_OUTPUT))
            );
        }
        TaskLogConditionParam conditionTaskLog = new TaskLogConditionParam();
        conditionTaskLog.setTaskId(taskId);
        List<EvalTaskLogPo> taskLogList = evalTaskLogGeneratorService.getByCondition(conditionTaskLog);
        Map<Long, List<EvalTaskLogPo>> taskLogPoMap = Optional.ofNullable(taskLogList).orElse(Collections.emptyList()).stream().collect(Collectors.groupingBy(EvalTaskLogPo::getQueryId));

        return convertTaskDetailList(evalTaskQueryList, evalTaskQueryDetailList, evalTask, taskLogPoMap);
    }

    @Override
    public TaskQueryDTO getByQueryId(Long queryId) {
        EvalTaskQueryPo query = evalTaskQueryGeneratorService.getById(queryId);
        CommonUtils.checkEval(query != null, "当前query不存在");
        EvalTaskPo task = evalTaskGeneratorService.getById(query.getTaskId());
        List<EvalTaskQueryDetailPo> evalTaskQueryDetailList = evalTaskQueryDetailGeneratorService.getByQueryIds(Collections.singletonList(queryId));
        TaskLogConditionParam condition = new TaskLogConditionParam();
        condition.setQueryId(queryId);
        List<EvalTaskLogPo> taskLogList = evalTaskLogGeneratorService.getByCondition(condition);
        Map<Long, List<EvalTaskLogPo>> taskLogPoMap = Optional.ofNullable(taskLogList).orElse(Collections.emptyList()).stream().collect(Collectors.groupingBy(EvalTaskLogPo::getQueryId));
        if (CollectionUtils.isEmpty(evalTaskQueryDetailList)) {
            return null;
        }
        Map<Long, ApplicationConfigPo> applicationConfigMap = getApplicationConfigPoMap(evalTaskQueryDetailList);
        Map<Long, MetricConfigPo> metricConfigMap = getMetricConfigMap(evalTaskQueryDetailList);
        TaskQueryDTO taskQueryDTO = new TaskQueryDTO();
        Map<String, String> aidaVersionMap = getAidaVersionMap();
        EvalDatasetPo evalDatasetPo = evalDatasetGeneratorService.getById(query.getDatasetId());
        taskQueryDTO.setResult(convertModelResult(query, evalTaskQueryDetailList, metricConfigMap, applicationConfigMap, aidaVersionMap, evalDatasetPo, true, taskLogPoMap, task));
        taskQueryDTO.setGsb(buildGsbList(evalTaskQueryDetailList, task));
        return taskQueryDTO;
    }

    @Override
    public List<CustomStatusDTO> getSessionStatusList(Integer taskType) {
        List<TaskSessionStatusEnum> queryStatusEnumList = taskType != null && taskType == TaskTypeEnum.ARENA.getCode() ? TaskSessionStatusEnum.ARENA_STATUS : TaskSessionStatusEnum.AUTO_STATUS;
        return queryStatusEnumList.stream().map(taskSessionStatusEnum -> {
            CustomStatusDTO customStatusDTO = new CustomStatusDTO();
            customStatusDTO.setStatusCode(taskSessionStatusEnum.getCode());
            customStatusDTO.setStatusName(taskSessionStatusEnum.getDescription());
            return customStatusDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public TaskLogDTO getTaskLog(Integer type, Long queryDetailId, Long queryId) {
        TaskLogConditionParam condition = new TaskLogConditionParam();
        condition.setType(type);
        condition.setQueryDetailId(queryDetailId);
        condition.setQueryId(queryId);
        List<EvalTaskLogPo> taskLogList = evalTaskLogGeneratorService.getByCondition(condition);
        TaskLogDTO taskLogDTO = new TaskLogDTO();
        if (CollectionUtils.isEmpty(taskLogList)) {
            return taskLogDTO;
        }
        EvalTaskLogPo taskLog = taskLogList.get(0);
        taskLogDTO.setBasicInfo(buildBasicInfo(taskLog));
        taskLogDTO.setPrompt(buildPrompt(taskLog));
        taskLogDTO.setModelConfig(buildModelConfig(taskLog));
        taskLogDTO.setInvokeInfo(buildInvokeInfo(taskLog));
        return taskLogDTO;
    }

    @Override
    public List<UserDTO> getMis(String mis, Integer pageSize, Integer pageNum) {
        MisInfoParam misInfoParam = new MisInfoParam();
        misInfoParam.setMis(mis);
        misInfoParam.setSize(pageSize);
        AidaBaseResponse<List<InnerMisInfoDTO>> misInfo = userRemoteService.getMisInfo(misInfoParam);
        return Optional.ofNullable(misInfo).orElse(new AidaBaseResponse<>()).getData().stream().map(misInfoDTO -> {
            UserDTO userDTO = new UserDTO();
            userDTO.setMis(misInfoDTO.getMis());
            userDTO.setName(misInfoDTO.getName());
            return userDTO;
        }).collect(Collectors.toList());

    }

    @Override
    public Map<Long, TaskColumnDTO> getFiledMap(TaskDatasetApplicationParam param) {
        List<Long> datasetIdList = param.getDatasetIdList();
        if (CollectionUtils.isEmpty(datasetIdList)) {
            return new HashMap<>();
        }
        List<Long> applicationIdList = buildApplication(param.getApplicationSource(), param.getAidaModelConfigList(), param.getApplicationIdList(), UserUtils.getUser().getLogin());
        List<EvalDatasetDetailPo> datasetDetailList = evalDatasetDetailGeneratorService.getByDatasetIdList(datasetIdList);
        Map<Long, List<EvalDatasetDetailPo>> datasetDetailMap = datasetDetailList.stream().collect(Collectors.groupingBy(EvalDatasetDetailPo::getDatasetId));
        Map<Long, TaskColumnDTO> resultMap = new HashMap<>();
        // TODO: 2024/6/18 先取headList
        List<EvalDatasetPo> datasetList = evalDatasetGeneratorService.getByIdList(datasetIdList);
        Map<Long, EvalDatasetPo> datasetIdMap = datasetList.stream().collect(Collectors.toMap(EvalDatasetPo::getId, Function.identity()));
        for (Long datasetId : datasetIdList) {
            List<EvalDatasetDetailPo> detailPoList = datasetDetailMap.get(datasetId);
            Set<String> headSet = new HashSet<>();
            EvalDatasetPo evalDatasetPo = datasetIdMap.get(datasetId);
            DatasetExtraParam datasetExtraParam = JSON.parseObject(evalDatasetPo.getExtra(), DatasetExtraParam.class);
            if (null != datasetExtraParam && CollectionUtils.isNotEmpty(datasetExtraParam.getHeadList())) {
                headSet.addAll(datasetExtraParam.getHeadList());
            } else {
                if (CollectionUtils.isNotEmpty(detailPoList)) {
                    int size = Lion.getInt(ConfigUtil.getAppkey(), LionConstants.CONTENT_CHECK_SIZE, 100);
                    for (EvalDatasetDetailPo datasetDetail : detailPoList) {
                        if (StringUtils.isNotBlank(datasetDetail.getContent())) {
                            Map<String, Object> dataList = JSON.parseObject(datasetDetail.getContent());
                            headSet.addAll(dataList.keySet());
                        }
                        if (size == 0) {
                            break;
                        }
                        size--;
                    }
                }

            }
            List<String> headList = new ArrayList<>(headSet);
            TaskColumnDTO taskColumn = new TaskColumnDTO();
            taskColumn.setColumnNameList(headList);
            taskColumn.setDefaultValueList(buildDefaultValueList(applicationIdList, datasetId, taskColumn.getColumnNameList(), param.getUploadType()));
            resultMap.put(datasetId, taskColumn);
        }
        return resultMap;
    }

    @Override
    public TaskInfoDTO getTaskInfo(Long taskId) {
        EvalTaskPo taskPo = evalTaskGeneratorService.getById(taskId);
        CommonUtils.checkEval(taskPo != null && !taskPo.getIsDeleted(), "任务不存在或已被删除");
        CommonUtils.checkEval(StringUtils.isNotBlank(taskPo.getExtra()), "当前任务信息已丢失，无法查看");
        TaskExtraParam param = JSON.parseObject(taskPo.getExtra(), TaskExtraParam.class);
        CommonUtils.checkEval(param != null && StringUtils.isNotBlank(param.getEvalRequest()), "当前任务信息已丢失，无法查看");
        EvalTaskRequest evalRequest = JSON.parseObject(param.getEvalRequest(), EvalTaskRequest.class);
        CommonUtils.checkEval(evalRequest != null && evalRequest.getApplicationSource() != null, "当前任务信息已丢失，无法查看");
        return convertTaskInfo(evalRequest);
    }

    @Override
    public List<RobotMockInfoDTO> getRobotMockInfoList() {
        // 获取用户模拟器列表
        Map<String, ApplicationDTO> robotMap = Lion.getMap(ConfigUtil.getAppkey(), LionConstants.EVAL_ROBOT_MOCK_LIST, ApplicationDTO.class);
        return robotMap.entrySet().stream().map(entry -> {
            RobotMockInfoDTO robotMockInfo = new RobotMockInfoDTO();
            robotMockInfo.setId(Long.parseLong(entry.getKey()));
            robotMockInfo.setName(entry.getValue().getName());
            robotMockInfo.setApplicationId(entry.getValue().getId());
            return robotMockInfo;
        }).collect(Collectors.toList());
    }

    @Override
    public TaskEvalDetailInfoDTO getEvalDetailInfo(Long taskId) {
        CommonUtils.checkEval(taskId != null, "任务id不能为空");
        EvalTaskPo evalTaskPo = evalTaskGeneratorService.getById(taskId);
        CommonUtils.checkEval(Objects.nonNull(evalTaskPo), "任务不存在");
        CommonUtils.checkEval(!evalTaskPo.getIsDeleted(), "任务已被删除");
        List<EvalTaskQueryDetailPo> evalTaskQueryDetailPos = evalTaskQueryDetailGeneratorService.getByTaskId(taskId);
        CommonUtils.checkEval(CollectionUtils.isNotEmpty(evalTaskQueryDetailPos), "任务评测数据被删除或不存在");
        //取queryDetail关联的指标
        List<Long> metricIds = evalTaskQueryDetailPos.stream().filter(Objects::nonNull).map(EvalTaskQueryDetailPo::getMetricId).map(Long::valueOf).collect(Collectors.toList());
        List<MetricConfigPo> metricConfigPos = metricConfigGeneratorService.getByIdList(metricIds);
        Map<Long, MetricConfigPo> metricConfigMap = Optional.ofNullable(metricConfigPos).orElse(Collections.emptyList()).stream().filter(Objects::nonNull).collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));
        //取queryDetail关联的数据集
        List<Long> datasetIds = evalTaskQueryDetailPos.stream().filter(Objects::nonNull).map(EvalTaskQueryDetailPo::getDatasetId).collect(Collectors.toList());
        List<EvalDatasetPo> datasetPos = evalDatasetGeneratorService.getByIdList(datasetIds);
        Map<String, String> evalMap = getEvalMap(datasetPos);
        List<EvalTaskSessionPo> evalTaskSessionPos = evalTaskSessionGeneratorService.getByTaskId(taskId);
        Map<String, EvalTaskSessionPo> evalTaskSessionPoMap = Optional.ofNullable(evalTaskSessionPos).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(EvalTaskSessionPo::getSessionId, Function.identity()));
        // 按 evalTarget 和 metricId 分组

        Map<String, Map<Integer, List<EvalTaskQueryDetailPo>>> queryDetailMap = evalTaskQueryDetailPos.stream().map(queryDetail -> {
                    if (StringUtils.isBlank(queryDetail.getOutputKey())) {
                        queryDetail.setOutputKey(CommonConstants.COMPLETE_OUTPUT);
                    }
                    return queryDetail;
                })
                .collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getOutputKey,
                        Collectors.groupingBy(EvalTaskQueryDetailPo::getMetricId)));

        // 构建 TaskEvalDetailInfoDTO
        TaskEvalDetailInfoDTO taskEvalDetailInfoDTO = new TaskEvalDetailInfoDTO();
        List<TaskEvalDetailInfoDTO.EvalTarget> evalTargets = new ArrayList<>();

        queryDetailMap.forEach((evalTarget, metricMap) -> parseEvalTarget(evalTarget, metricMap, metricConfigMap, evalTargets, evalMap, evalTaskPo, evalTaskSessionPoMap));

        taskEvalDetailInfoDTO.setEvalTargets(evalTargets);
        return taskEvalDetailInfoDTO;
    }


    private static Map<String, String> getEvalMap(List<EvalDatasetPo> datasetPos) {
        return Optional.ofNullable(
                        Optional.ofNullable(datasetPos).orElse(Collections.emptyList())
                                .stream()
                                .filter(Objects::nonNull)
                                .findFirst()
                                .map(EvalDatasetPo::getExtra)
                                .filter(StringUtils::isNotBlank)
                                .map(extra -> JSON.parseObject(extra, DatasetExtraParam.class))
                                .orElse(new DatasetExtraParam())
                                .getHeadMapList()
                ).orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(SingleTemplateFieldBindDTO::getColumnKey,
                        SingleTemplateFieldBindDTO::getColumnName, (k1, k2) -> k1));
    }

    /**
     * 解析封装评测对象
     *
     * @param evalTarget
     * @param metricMap
     * @param metricConfigMap
     * @param evalTargets
     * @param evalMap
     * @param evalTaskPo
     * @param evalTaskSessionPoMap
     */
    private void parseEvalTarget(String evalTarget, Map<Integer, List<EvalTaskQueryDetailPo>> metricMap, Map<Long, MetricConfigPo> metricConfigMap, List<TaskEvalDetailInfoDTO.EvalTarget> evalTargets, Map<String, String> evalMap, EvalTaskPo evalTaskPo, Map<String, EvalTaskSessionPo> evalTaskSessionPoMap) {
        TaskEvalDetailInfoDTO.EvalTarget target = new TaskEvalDetailInfoDTO.EvalTarget();
        List<TaskEvalDetailInfoDTO.EvalMetricDTO> evalMetrics = new ArrayList<>();
        target.setKey(evalTarget);
        target.setName(MapUtils.isNotEmpty(evalMap) && StringUtils.isNotBlank(evalMap.get(evalTarget)) ? evalMap.get(evalTarget) : evalTarget);
        metricMap.forEach((metricId, detailList) -> parseMetric(metricId, detailList, metricConfigMap, evalMetrics, evalTaskPo, evalTaskSessionPoMap));
        target.setEvalMetrics(evalMetrics);
        evalTargets.add(target);
    }

    /**
     * 解析封装指标对象
     *
     * @param metricId
     * @param detailList
     * @param metricConfigMap
     * @param evalMetrics
     * @param evalTaskPo
     * @param evalTaskSessionPoMap
     */
    private void parseMetric(Integer metricId, List<EvalTaskQueryDetailPo> detailList, Map<Long, MetricConfigPo> metricConfigMap, List<TaskEvalDetailInfoDTO.EvalMetricDTO> evalMetrics, EvalTaskPo evalTaskPo, Map<String, EvalTaskSessionPo> evalTaskSessionPoMap) {
        TaskEvalDetailInfoDTO.EvalMetricDTO evalMetricDTO = new TaskEvalDetailInfoDTO.EvalMetricDTO();
        MetricConfigPo metricConfigPo = metricConfigMap.get(metricId.longValue());
        if (Objects.nonNull(metricConfigPo)) {
            evalMetricDTO.setMetricId(String.valueOf(metricConfigPo.getId()));
            evalMetricDTO.setMetricName(metricConfigPo.getName());
            evalMetricDTO.setMetricType(Objects.nonNull(metricConfigPo.getType()) ? metricConfigPo.getType().toString() : null);

            evalMetricDTO.setEvalResults(getEvalResults(detailList));

            evalMetricDTO.setInspectionResult(getInspectionResult(detailList, evalTaskPo, evalTaskSessionPoMap));
            evalMetrics.add(evalMetricDTO);
        }
    }

    /**
     * 解析评测结果
     *
     * @param detailList
     * @return
     */
    private List<String> getEvalResults(List<EvalTaskQueryDetailPo> detailList) {
        return Optional.ofNullable(detailList).orElse(Collections.emptyList()).stream()
                .filter(Objects::nonNull)
                .map(EvalTaskQueryDetailPo::getMetricResult)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 解析质检结果
     *
     * @param detailList
     * @param evalTaskPo
     * @param evalTaskSessionPoMap
     * @return
     */
    private List<String> getInspectionResult(List<EvalTaskQueryDetailPo> detailList, EvalTaskPo evalTaskPo, Map<String, EvalTaskSessionPo> evalTaskSessionPoMap) {
        return detailList.stream()
                .filter(Objects::nonNull)
                .map(EvalTaskQueryDetailPo::getAnswer)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }


    private TaskInfoDTO convertTaskInfo(EvalTaskRequest evalRequest) {
        TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
        taskInfoDTO.setName(evalRequest.getName());
        taskInfoDTO.setCallType(evalRequest.getCallType());
        taskInfoDTO.setInputSource(evalRequest.getInputSource());
        taskInfoDTO.setMockTimes(evalRequest.getMockTimes());
        taskInfoDTO.setDatasetNameList(buildDatasetName(evalRequest.getDataSetIdList()));
        taskInfoDTO.setMetricList(buildMetricInfoList(evalRequest));
        taskInfoDTO.setInspectors(evalRequest.getInspectors());
        taskInfoDTO.setApplicationSource(evalRequest.getApplicationSource());
        taskInfoDTO.setHistorySource(evalRequest.getHistorySource());
        // 设置应用信息
        setApplicationInfo(taskInfoDTO, evalRequest);
        taskInfoDTO.setBindFields(evalRequest.getBindFields());
        return taskInfoDTO;
    }

    private void setApplicationInfo(TaskInfoDTO taskInfoDTO, EvalTaskRequest evalRequest) {
        List<ApplicationResultParam> modelResultParamList = new ArrayList<>();
        List<ApplicationResultParam> resultParamList = new ArrayList<>();
        List<Long> applicationIdList = evalRequest.getApplicationConfig().stream().map(DataConvertUtil::tryConvertLongWithNull).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applicationIdList)) {
            return;
        }
        if (evalRequest.getApplicationSource() == TaskModelSourceEnum.APPLICATION.getCode()) {
            taskInfoDTO.setApplicationConfig(applicationIdList);
            if (MapUtils.isNotEmpty(evalRequest.getApplicationModelMap())) {
                modelResultParamList = evalRequest.getApplicationModelMap().entrySet().stream()
                        .map(entry -> {
                            ApplicationResultParam applicationResultParam = new ApplicationResultParam();
                            applicationResultParam.setApplicationSource(evalRequest.getApplicationSource());
                            applicationResultParam.setApplicationId(Long.parseLong(entry.getKey()));
                            applicationResultParam.setModelResultParam(entry.getValue());
                            return applicationResultParam;
                        })
                        .collect(Collectors.toList());
            }
            if (MapUtils.isNotEmpty(evalRequest.getApplicationParamMap())) {
                resultParamList = evalRequest.getApplicationParamMap().entrySet().stream()
                        .map(entry -> {
                            ApplicationResultParam applicationResultParam = new ApplicationResultParam();
                            applicationResultParam.setApplicationSource(evalRequest.getApplicationSource());
                            applicationResultParam.setApplicationId(entry.getKey());
                            applicationResultParam.setModelResultParam(entry.getValue());
                            return applicationResultParam;
                        })
                        .collect(Collectors.toList());
            }
        } else {
            List<ApplicationConfigPo> applicationConfigList = applicationConfigGeneratorService.getByIdList(applicationIdList);
            Map<Long, ApplicationConfigPo> applicationConfigMap = applicationConfigList.stream().collect(Collectors.toMap(ApplicationConfigPo::getId, Function.identity()));
            List<AidaModelConfig> aidaModelConfigList = applicationConfigList.stream().map(TaskExecuteServiceImpl::getAidaModelConfig).collect(Collectors.toList());
            taskInfoDTO.setAidaModelConfigList(aidaModelConfigList);
            if (MapUtils.isNotEmpty(evalRequest.getApplicationModelMap())) {
                modelResultParamList = evalRequest.getApplicationModelMap().entrySet().stream()
                        .map(entry -> {
                            ApplicationResultParam applicationResultParam = new ApplicationResultParam();
                            applicationResultParam.setApplicationSource(evalRequest.getApplicationSource());
                            applicationResultParam.setAidaModelConfig(getAidaModelConfig(applicationConfigMap.get(Long.parseLong(entry.getKey()))));
                            applicationResultParam.setModelResultParam(entry.getValue());
                            return applicationResultParam;
                        })
                        .collect(Collectors.toList());
            }
            if (MapUtils.isNotEmpty(evalRequest.getApplicationParamMap())) {
                resultParamList = evalRequest.getApplicationParamMap().entrySet().stream()
                        .map(entry -> {
                            ApplicationResultParam applicationResultParam = new ApplicationResultParam();
                            applicationResultParam.setApplicationSource(evalRequest.getApplicationSource());
                            applicationResultParam.setAidaModelConfig(getAidaModelConfig(applicationConfigMap.get(entry.getKey())));
                            applicationResultParam.setModelResultParam(entry.getValue());
                            return applicationResultParam;
                        })
                        .collect(Collectors.toList());
            }
        }
        taskInfoDTO.setModelResultParamList(modelResultParamList);
        taskInfoDTO.setResultParamList(resultParamList);
    }

    @NotNull
    private static AidaModelConfig getAidaModelConfig(ApplicationConfigPo applicationConfigPo) {
        AidaModelConfig aidaModelConfig = new AidaModelConfig();
        aidaModelConfig.setWorkspaceId(applicationConfigPo.getPlatformWorkspace());
        aidaModelConfig.setApplicationId(applicationConfigPo.getPlatformApp());
        aidaModelConfig.setModelConfigVersionId(applicationConfigPo.getRobotId());
        return aidaModelConfig;
    }

    private List<TaskInfoDTO.MetricInfo> buildMetricInfoList(EvalTaskRequest evalRequest) {
        List<Long> mertricIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(evalRequest.getQueryMetric())) {
            mertricIdList.addAll(evalRequest.getQueryMetric().stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(evalRequest.getSessionMetric())) {
            mertricIdList.addAll(evalRequest.getSessionMetric().stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(mertricIdList)) {
            return new ArrayList<>();
        }
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(mertricIdList);
        if (CollectionUtils.isEmpty(metricConfigList)) {
            return new ArrayList<>();
        }
        List<ScoreThresholdParam> scoreThresholdList = evalRequest.getScoreThreshold();
        Map<Long, ScoreThresholdParam> scoreThresholdMap = scoreThresholdList.stream().collect(Collectors.toMap(scoreThresholdParam -> Long.valueOf(scoreThresholdParam.getMetric()), Function.identity()));
        return metricConfigList.stream().map(metricConfig -> {
            TaskInfoDTO.MetricInfo metricInfo = new TaskInfoDTO.MetricInfo();
            metricInfo.setName(metricConfig.getName());
            metricInfo.setType(metricConfig.getMetricType());
            if (scoreThresholdMap.containsKey(metricConfig.getId())) {
                ScoreThresholdParam scoreThresholdParam = scoreThresholdMap.get(metricConfig.getId());
                if (MetricTypeEnum.NUMBER.getCode() == metricConfig.getMetricType()) {
                    metricInfo.setScoreThreshold(scoreThresholdParam.getScoreThreshold());
                } else {
                    metricInfo.setScoreThreshold(scoreThresholdParam.getScoreThresholdList());
                }
            }
            return metricInfo;
        }).collect(Collectors.toList());
    }

    private List<String> buildDatasetName(List<Long> datasetIdList) {
        if (CollectionUtils.isEmpty(datasetIdList)) {
            return new ArrayList<>();
        }
        List<EvalDatasetPo> evalDatasetList = evalDatasetGeneratorService.getByIdList(datasetIdList);
        if (CollectionUtils.isEmpty(evalDatasetList)) {
            return new ArrayList<>();
        }
        return evalDatasetList.stream().map(EvalDatasetPo::getName).collect(Collectors.toList());
    }

    private List<TemplateFieldBindDTO> buildDefaultValueList(List<Long> applicationIdList, Long datasetId, List<String> columnNameList, Integer uploadType) {
        List<TemplateFieldBindDTO> templateFieldBindList = new ArrayList<>();
        // 策略一：查找历史的映射记录
        for (Long applicationId : applicationIdList) {
            TaskConditionParam condition = new TaskConditionParam();
            condition.setDatasetId(datasetId);
            condition.setApplicationId(applicationId);
            List<EvalTaskPo> taskList = evalTaskGeneratorService.getByCondition(condition);
            // 合并多个应用的映射
            templateFieldBindList = mergeFiled(templateFieldBindList, datasetId, taskList);
        }
        // 策略二：同名映射
        if (CollectionUtils.isEmpty(templateFieldBindList)) {
            Set<String> columnNameSet = new HashSet<>(columnNameList);
            ApplicationInfoDTO applicationInfoDTO = getApplicationInfoList(applicationIdList, uploadType, UserUtils.getUser().getLogin());
            // applicationParamList
            if (CollectionUtils.isNotEmpty(applicationInfoDTO.getApplicationParamList())) {
                for (ApplicationInfoDTO.Param param : applicationInfoDTO.getApplicationParamList()) {
                    if (columnNameSet.contains(param.getFieldName())) {
                        TemplateFieldBindDTO templateFieldBindDTO = new TemplateFieldBindDTO();
                        templateFieldBindDTO.setFieldCode(param.getFieldCode());
                        templateFieldBindDTO.setFieldKey(param.getFieldKey());
                        templateFieldBindDTO.setFieldName(param.getFieldName());
                        templateFieldBindDTO.setColumnName(param.getFieldName());
                        templateFieldBindList.add(templateFieldBindDTO);
                    }
                }
            }
            // systemParamList
            if (CollectionUtils.isNotEmpty(applicationInfoDTO.getSystemParamList())) {
                for (ApplicationInfoDTO.Param param : applicationInfoDTO.getSystemParamList()) {
                    if (columnNameSet.contains(param.getFieldName())) {
                        TemplateFieldBindDTO templateFieldBindDTO = new TemplateFieldBindDTO();
                        templateFieldBindDTO.setFieldCode(param.getFieldCode());
                        templateFieldBindDTO.setFieldKey(param.getFieldKey());
                        templateFieldBindDTO.setFieldName(param.getFieldName());
                        templateFieldBindDTO.setColumnName(param.getFieldName());
                        templateFieldBindList.add(templateFieldBindDTO);
                    }
                }
            }
        }
        // 策略三：利用大模型实现映射
        return templateFieldBindList;
    }

    private List<TemplateFieldBindDTO> mergeFiled(List<TemplateFieldBindDTO> templateFieldBindList, Long datasetId, List<EvalTaskPo> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            return templateFieldBindList;
        }
        for (EvalTaskPo task : taskList) {
            String extra = task.getExtra();
            if (StringUtils.isNotBlank(extra)) {
                TaskExtraParam extraParam = JSON.parseObject(extra, TaskExtraParam.class);
                if (MapUtils.isNotEmpty(extraParam.getBindFields()) && extraParam.getBindFields().containsKey(datasetId)) {
                    List<TemplateFieldBindDTO> curTemplateFieldBindList = extraParam.getBindFields().get(datasetId);
                    templateFieldBindList.addAll(curTemplateFieldBindList);
                    break;
                }
            }
        }
        // 去重
        if (CollectionUtils.isNotEmpty(templateFieldBindList)) {
            Map<String, TemplateFieldBindDTO> templateFieldBindMap = templateFieldBindList.stream().collect(Collectors.toMap(templateFieldBind -> templateFieldBind.getFieldCode() + (StringUtils.isNotBlank(templateFieldBind.getFieldKey()) ? templateFieldBind.getFieldKey() : ""), Function.identity(), (a, b) -> a));
            templateFieldBindList = new ArrayList<>(templateFieldBindMap.values());
        }
        return templateFieldBindList;
    }

    private TaskLogDTO.InvokeInfo buildInvokeInfo(EvalTaskLogPo taskLog) {
        TaskLogDTO.InvokeInfo invokeInfo = new TaskLogDTO.InvokeInfo();
        if (StringUtils.isNotBlank(taskLog.getContent())) {
            GptContextParam param = JSON.parseObject(taskLog.getContent(), GptContextParam.class);
            invokeInfo.setOutput(param.getOutput());
            invokeInfo.setHistory(param.getContext());
        }
        return invokeInfo;
    }

    private List<List<String>> convertHeadList(List<String> head) {
        return head.stream()
                .map(this::convertList)
                .collect(Collectors.toList());
    }

    private List<String> convertList(String... head) {
        return new ArrayList<>(Arrays.asList(head));
    }

    private List<String> getParamNameList(EvalTaskPo task) {
        List<Long> datasetIdList = Arrays.stream(task.getDatasetIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<EvalDatasetPo> datasetList = evalDatasetGeneratorService.getByIdList(datasetIdList);
        if (CollectionUtils.isEmpty(datasetList)) {
            return new ArrayList<>();
        }
        List<String> paramNameList = new ArrayList<>();
        datasetList.forEach(dataset -> {
            if (StringUtils.isNotBlank(dataset.getExtra())) {
                JSONObject jsonObject = JSON.parseObject(dataset.getExtra());
                if (jsonObject != null && jsonObject.containsKey(EvalConstants.PARAM_NAME_LIST)) {
                    paramNameList.addAll(jsonObject.getJSONArray(EvalConstants.PARAM_NAME_LIST).toJavaList(String.class));
                }
            }
        });
        return paramNameList;
    }

    private ModelConfigRequestParam buildModelConfig(EvalTaskLogPo taskLog) {
        if (StringUtils.isNotBlank(taskLog.getContent())) {
            GptContextParam param = JSON.parseObject(taskLog.getContent(), GptContextParam.class);
            return param.getModelConfig();
        }
        return null;
    }

    private TaskLogDTO.Prompt buildPrompt(EvalTaskLogPo taskLog) {
        TaskLogDTO.Prompt prompt = new TaskLogDTO.Prompt();
        if (StringUtils.isNotBlank(taskLog.getContent())) {
            GptContextParam param = JSON.parseObject(taskLog.getContent(), GptContextParam.class);
            prompt.setContent(param.getPrompt());
        }
        return prompt;
    }

    private TaskLogDTO.BasicInfo buildBasicInfo(EvalTaskLogPo taskLog) {
        TaskLogDTO.BasicInfo basicInfo = new TaskLogDTO.BasicInfo();
        basicInfo.setCostTime(taskLog.getCostTime());
        if (StringUtils.isNotBlank(taskLog.getCostToken())) {
            TokenParam tokenParam = JSON.parseObject(taskLog.getCostToken(), TokenParam.class);
            basicInfo.setCostToken(tokenParam.getTotalTokenNum());
        }
        return basicInfo;
    }

    private List<String> buildQueryExcelData(TaskDetailDTO taskDetailDTO, List<String> paramNameList, List<String> uniqueAppMetricOutputKeyList, List<String> datasetHeadList, Map<String, String> nameKeyMap, Integer datasetType) {
        List<String> data = new ArrayList<>();
        // 添加数据集列数据
        addDatasetData(taskDetailDTO, datasetHeadList, data, nameKeyMap);
        // 添加Base列数据
        data.add(taskDetailDTO.getSessionId());
        data.add(taskDetailDTO.getInput());

        if (CollectionUtils.isNotEmpty(paramNameList)) {
            paramNameList.forEach(paramName -> data.add(MapUtils.isNotEmpty(taskDetailDTO.getParams()) && taskDetailDTO.getParams().containsKey(paramName) ? taskDetailDTO.getParams().get(paramName) : null));
        } else {
            data.add(MapUtils.isNotEmpty(taskDetailDTO.getParams()) ? JSON.toJSONString(taskDetailDTO.getParams()) : null);
        }
        data.add(taskDetailDTO.getStatus() == null ? null : TaskQueryStatusEnum.parse(taskDetailDTO.getStatus()).getName());
        Map<String, List<TaskDetailDTO.EvalInfo>> uniqueKeyDataMap = Maps.newHashMap();

        if (CollectionUtils.isNotEmpty(taskDetailDTO.getResult())) {
            for (TaskDetailDTO.EvalModelDTO evalModelDTO : taskDetailDTO.getResult()) {
                String applicationName = evalModelDTO.getModelName();
                if (CollectionUtils.isNotEmpty(evalModelDTO.getData())) {
                    List<TaskDetailDTO.EvalOutputDTO> evalOutputDTOS = evalModelDTO.getData();
                    for (TaskDetailDTO.EvalOutputDTO evalOutputDTO : evalOutputDTOS) {
                        if (CollectionUtils.isNotEmpty(evalOutputDTO.getEval())) {
                            String appOutputMetricKey = "";
                            for (TaskDetailDTO.EvalInfo evalInfo : evalOutputDTO.getEval()) {
                                String metricName = evalInfo.getMetricName();
                                String outputKey = evalInfo.getOutputKey();
                                if (TaskTypeEnum.TRAIN_MANUAL_ANNOTATION.getCode() == taskDetailDTO.getType()) {
                                    appOutputMetricKey = applicationName + metricName;
                                } else {
                                    //nameKeyMap 为空是评测集任务，不为空是错题集任务，兼容
                                    if (2 == datasetType && MapUtils.isNotEmpty(nameKeyMap)) {
                                        appOutputMetricKey = applicationName + metricName + nameKeyMap.get(outputKey);
                                    } else {
                                        appOutputMetricKey = applicationName + metricName + outputKey;
                                    }

                                }
                                uniqueKeyDataMap.computeIfAbsent(appOutputMetricKey, k -> Lists.newArrayList()).add(evalInfo);
                            }
                        }
                    }
                }
            }
            Set<String> rowData = new HashSet<>(); // 使用Set避免重复

            for (String uniqueKey : uniqueAppMetricOutputKeyList) {
                List<TaskDetailDTO.EvalInfo> evalInfos = uniqueKeyDataMap.get(uniqueKey);
                if (CollectionUtils.isNotEmpty(evalInfos)) {

                    for (TaskDetailDTO.EvalInfo evalInfo : evalInfos) {

                        rowData.add(evalInfo.getMarkMis());
                        rowData.add(evalInfo.getMarkTime());
                        if (TaskTypeEnum.TRAIN_MANUAL_ANNOTATION.getCode() == taskDetailDTO.getType()) {
                            rowData.add(evalInfo.getResult());
                            data.addAll(rowData.stream().distinct().collect(Collectors.toList()));
                        } else {
                            data.add(evalInfo.getOutput());
                            data.add(evalInfo.getExpectOutput());
                            data.add(evalInfo.getResult());
                            data.add(evalInfo.getResultNote());
                            String inspectInfo = buildInspectInfo(evalInfo);
                            data.add(inspectInfo);
                        }

                    }
                } else {
                    data.add("");
                }
            }

        }
        return data;
    }

    private void addDatasetData(TaskDetailDTO taskDetailDTO, List<String> datasetHeadList, List<String> data, Map<String, String> nameKeyMap) {
        if (CollectionUtils.isEmpty(datasetHeadList)) {
            return;
        }
        JSONObject parsed = JSONObject.parseObject(taskDetailDTO.getContent());
        datasetHeadList.forEach(head -> data.add(parsed.getString(head)));
    }

    private int getInspectCount(Long taskId) {
        List<EvalTaskQueryPo> queryPoList = evalTaskQueryGeneratorService.getByTaskId(taskId);
        if (CollectionUtils.isEmpty(queryPoList)) {
            return 0;
        }
        return (int) queryPoList.stream().filter(evalTaskQueryPo -> evalTaskQueryPo.getStatus() == TaskQueryStatusEnum.INSPECT_FAILED.getCode() || evalTaskQueryPo.getStatus() == TaskQueryStatusEnum.INSPECT_PASS.getCode()).count();
    }

    private List<EvalTaskQueryPo> filterInspector(List<EvalTaskQueryPo> taskQueryList, List<EvalTaskQueryDetailPo> queryDetailList, String inspector) {
        Map<Long, List<EvalTaskQueryDetailPo>> queryDetailMap = queryDetailList.stream().collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getQueryId));
        return taskQueryList.stream().filter(evalTaskQueryPo -> {
            List<EvalTaskQueryDetailPo> detailPoList = queryDetailMap.get(evalTaskQueryPo.getId());
            if (CollectionUtils.isEmpty(detailPoList)) {
                return false;
            }
            for (EvalTaskQueryDetailPo queryDetail : detailPoList) {
                if (inspector.equals(queryDetail.getInspectMis())) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());
    }

    private PageData<TaskSessionResultDTO> convertTaskSessionPageInfo(int totalSize, PageParam<TaskQuerySessionConditionParam> pageParam, List<EvalTaskSessionPo> pageList, Map<String, List<EvalTaskQueryDetailPo>> evalTaskQueryDetailMap) {

        EvalTaskPo evalTask = evalTaskGeneratorService.getById(pageParam.getCondition().getTaskId());
        List<Long> datasetIdList = pageList.stream().map(EvalTaskSessionPo::getDatasetId).collect(Collectors.toList());
        List<EvalDatasetPo> datasetList = evalDatasetGeneratorService.getByIdList(datasetIdList);
        Map<Long, EvalDatasetPo> datasetMap = datasetList.stream().collect(Collectors.toMap(EvalDatasetPo::getId, Function.identity()));
        List<ScoreThresholdParam> scoreThresholdList = parseScoreThresholdList(evalTask);

        List<TaskSessionResultDTO> sessionResultList = pageList.stream().map(evalTaskSessionPo -> {
            TaskSessionResultDTO sessionResult = new TaskSessionResultDTO();
            sessionResult.setConversationId(String.valueOf(evalTaskSessionPo.getId()));
            sessionResult.setStatus(evalTaskSessionPo.getStatus());
            sessionResult.setCanInspect(evalTaskSessionPo.getStatus() == TaskSessionStatusEnum.INSPECTING.getCode());
            if (evalTaskSessionPo.getDatasetId() != null && 0 != evalTaskSessionPo.getDatasetId()) {
                sessionResult.setDataSetId(evalTaskSessionPo.getDatasetId());
                sessionResult.setDataSetName(datasetMap.get(evalTaskSessionPo.getDatasetId()).getName());
                sessionResult.setSessionId(evalTaskSessionPo.getDatasetId() + "_" + evalTaskSessionPo.getSessionId() + "_" + evalTaskSessionPo.getId());
            } else {
                sessionResult.setSessionId(evalTaskSessionPo.getSessionId());
            }
            if (TaskTypeEnum.ARENA.equals(TaskTypeEnum.parse(evalTask.getType()))) {
                sessionResult.setResult(buildArenaSessionResult(evalTask, evalTaskSessionPo));
            } else {
                List<EvalTaskQueryDetailPo> queryDetailList = evalTaskQueryDetailMap.get(String.valueOf(evalTaskSessionPo.getId()));
                sessionResult.setResult(buildSessionResult(scoreThresholdList, evalTaskSessionPo, queryDetailList));
            }
            return sessionResult;
        }).collect(Collectors.toList());
        return PageData.create(totalSize, pageParam.getPageNum(), pageParam.getPageSize(), sessionResultList);
    }

    private static boolean filterSession(Map<String, List<EvalTaskQueryDetailPo>> evalTaskQueryDetailMap, EvalTaskSessionPo taskSession) {
        return MapUtils.isNotEmpty(evalTaskQueryDetailMap) && CollectionUtils.isNotEmpty(evalTaskQueryDetailMap.get(String.valueOf(taskSession.getId())));
    }

    private List<TaskSessionResultDTO.SessionResult> buildArenaSessionResult(EvalTaskPo evalTask, EvalTaskSessionPo evalTaskSessionPo) {
        List<TaskSessionResultDTO.SessionResult> result = new ArrayList<>();
        TaskSessionResultDTO.SessionResult evalTaskDetailDTO = new TaskSessionResultDTO.SessionResult();
        evalTaskDetailDTO.setModel("【" + evalTask.getTestModel() + "】");
        Integer metric = Integer.parseInt(evalTask.getMetrics());
        evalTaskDetailDTO.setMetric("【" + EvalMetricTypeEnum.getByCode(metric).getName() + "】");
        evalTaskDetailDTO.setEvalResult(calculateArenaVoteResult(isValidNumber(evalTaskSessionPo.getSessionResult()) ? Integer.parseInt(evalTaskSessionPo.getSessionResult()) : ArenaVoteResultEnum.TBD.getCode()));
        result.add(evalTaskDetailDTO);
        return result;
    }

    private String buildInspectInfo(TaskDetailDTO.EvalInfo evalInfo) {
        if (evalInfo.getInspectResult() == null) {
            return "";
        }
        StringBuilder inspectInfo = new StringBuilder();
        inspectInfo.append("质检人：").append(evalInfo.getInspector());
        if (evalInfo.getInspectResult() == InspectResultEnum.PAAS.getCode()) {
            inspectInfo.append("\n质检结果：").append(InspectResultEnum.PAAS.getDescription());
        } else {
            inspectInfo.append("\n质检结果：").append(InspectResultEnum.FAIL.getDescription());
            inspectInfo.append("\n驳回原因：").append(evalInfo.getReason());
            inspectInfo.append("\n正确答案：").append(evalInfo.getCorrectAnswer());
        }
        return inspectInfo.toString();
    }

    private void checkMetricParam(TaskParam taskParam, List<EvalDatasetDetailPo> datasetDetailList) {
        if (taskParam.getInputSource() != null && TaskInputSourceEnum.DATASET.getCode() != taskParam.getInputSource()) {
            CommonUtils.checkEval(taskParam.getMockTimes() != null, "模拟次数不能为空");
            if (taskParam.getModelSource() == TaskModelSourceEnum.APPLICATION.getCode()) {
                CommonUtils.checkEval(taskParam.getApplicationConfig().size() == 1, "模拟场景不支持多应用");
            } else {
                CommonUtils.checkEval(taskParam.getAidaModelConfig().size() == 1, "模拟场景不支持多应用");
            }
        }
        // 单轮应用暂不支持人工模拟
        if (taskParam.getAbilityType() == AbilityEnum.SINGLE_ROUND.getCode()) {
            CommonUtils.checkEval(taskParam.getInputSource() == null || taskParam.getInputSource() != TaskInputSourceEnum.MANUAL_MOCK.getCode(), "单轮应用暂不支持人工模拟");
        }
        if (CollectionUtils.isNotEmpty(taskParam.getMetricList())) {
            List<Long> metricIdList = taskParam.getMetricList().stream().map(TaskMetricParam::getMetricId).collect(Collectors.toList());
            List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricIdList);
            // 单轮应用暂不支持人工指标和人工模拟
            if (taskParam.getAbilityType() == AbilityEnum.SINGLE_ROUND.getCode()) {
                long manualCount = metricConfigList.stream().filter(metricConfigPo -> metricConfigPo.getEvalType() != null && metricConfigPo.getEvalType() == MetricEvalTypeEnum.MANUAL.getCode()).count();
                CommonUtils.checkEval(manualCount == 0, "单轮应用暂不支持人工指标");
            }
            // session维度人工指标暂不支持多个应用
            long sessionCount = metricConfigList.stream().filter(metricConfigPo -> metricConfigPo.getDimension() != null && metricConfigPo.getDimension() == MetricDimensionEnum.SESSION.getCode() && metricConfigPo.getEvalType() != null && metricConfigPo.getEvalType() == MetricEvalTypeEnum.MANUAL.getCode()).count();
            if (sessionCount > 0) {
                int applicationCount;
                if (taskParam.getModelSource() == TaskModelSourceEnum.APPLICATION.getCode()) {
                    applicationCount = taskParam.getApplicationConfig().size();
                } else {
                    applicationCount = taskParam.getAidaModelConfig().size();
                }
                CommonUtils.checkEval(applicationCount == 1, "session维度人工指标暂不支持多个应用");
            }
            List<Long> applicationIdList = metricConfigList.stream().map(MetricConfigPo::getApplicationId).collect(Collectors.toList());
            List<ApplicationConfigPo> applicationConfigList = applicationConfigGeneratorService.getByIdList(applicationIdList);
            Set<String> paramList = new HashSet<>();
            for (ApplicationConfigPo applicationConfigPo : applicationConfigList) {
                InnerAppConfigDTO appConfig = aidaInvokeServiceProxy.getAidaRobotInfo(applicationConfigPo.getPlatformApp(), applicationConfigPo.getRobotId());
                if (appConfig != null) {
                    setParamList(paramList, appConfig);
                }
            }
            if (CollectionUtils.isNotEmpty(paramList)) {
                // 过滤掉系统变量
                paramList.removeIf(EvalConstants.SYSTEM_PARAM::contains);
                // 离线模式校验数据集的变量
                if (CallTypeEnum.ONLINE.getCode() == taskParam.getCallType()) {
                    if (CollectionUtils.isNotEmpty(datasetDetailList)) {
                        EvalDatasetDetailPo datasetDetail = datasetDetailList.get(0);
                        CommonUtils.checkEval(StringUtils.isNotBlank(datasetDetail.getParams()), "当前数据集缺少参数无法计算选定指标");
                        JSONObject jsonObject = DataConvertUtil.tryConvertJson(datasetDetail.getParams());
                        CommonUtils.checkEval(jsonObject != null, "当前数据集缺少参数无法计算选定指标");
                        for (String param : paramList) {
                            CommonUtils.checkEval(jsonObject.containsKey(param), "当前数据集无法计算选定指标，缺少参数：" + param);
                        }
                    }
                } else {
                    // 在线模式校验应用的变量
                    Set<String> applicationParamList = datasetExecuteService.getDatasetParamList(taskParam.getModelSource(), taskParam.getAidaModelConfig(), taskParam.getApplicationConfig());
                    for (String param : paramList) {
                        CommonUtils.checkEval(applicationParamList.contains(param), "当前数据集无法计算选定指标，缺少参数：" + param);
                    }
                }
            }
        }
    }

    private void setParamList(Set<String> paramList, InnerAppConfigDTO appConfig) {
        List<InnerAppConfigDTO.UserInputForm> userInputFormList = appConfig.getUserInputFormList();
        if (CollectionUtils.isNotEmpty(userInputFormList)) {
            for (InnerAppConfigDTO.UserInputForm userInputForm : userInputFormList) {
                if (userInputForm.getText() != null && userInputForm.getText().getRequired() != null && userInputForm.getText().getRequired()) {
                    paramList.add(userInputForm.getText().getVariable());
                }
                if (userInputForm.getSelect() != null && userInputForm.getSelect().getRequired() != null && userInputForm.getSelect().getRequired()) {
                    paramList.add(userInputForm.getSelect().getVariable());
                }
                if (userInputForm.getPhoto() != null && userInputForm.getPhoto().getRequired() != null && userInputForm.getPhoto().getRequired()) {
                    paramList.add(userInputForm.getPhoto().getVariable());
                }
                if (userInputForm.getParagraph() != null && userInputForm.getParagraph().getRequired() != null && userInputForm.getParagraph().getRequired()) {
                    paramList.add(userInputForm.getParagraph().getVariable());
                }
            }
        }
    }

    private double getAidaScoreThreshold(Integer evalType, EvalTaskPo evalTask) {
        Double scoreThreshold = null;
        String extMap = evalTask.getExtra();
        if (StringUtils.isNotBlank(extMap)) {
            JSONObject jsonObject = JSONObject.parseObject(extMap);
            if (jsonObject.containsKey("scoreThreshold")) {
                scoreThreshold = jsonObject.getDouble("scoreThreshold");
            }
        }
        if (EvalMetricTypeEnum.EQUAL.getCode() == evalType || EvalMetricTypeEnum.CONTAIN.getCode() == evalType) {
            return 0.0;
        }
        if (scoreThreshold == null) {
            return 0.5;
        }
        return scoreThreshold;
    }

    private List<TaskSessionResultDTO.SessionResult> buildSessionResult(List<ScoreThresholdParam> scoreThresholdList, EvalTaskSessionPo evalTaskSessionPo, List<EvalTaskQueryDetailPo> evalTaskQueryDetailList) {
        List<TaskSessionResultDTO.SessionResult> sessionResultList = new ArrayList<>();
        // session维度指标
        if (StringUtils.isNotBlank(evalTaskSessionPo.getSessionResult())) {
            List<SessionEvalResultParam> sessionResultParamList = JSON.parseArray(evalTaskSessionPo.getSessionResult(), SessionEvalResultParam.class);
            if (CollectionUtils.isNotEmpty(sessionResultParamList)) {
                sessionResultList = sessionResultParamList.stream().map(sessionEvalResultParam -> {
                    TaskSessionResultDTO.SessionResult sessionResult = new TaskSessionResultDTO.SessionResult();
                    sessionResult.setModel(getName(sessionEvalResultParam.getApplication()));
                    sessionResult.setMetric(sessionEvalResultParam.getMetricName());
                    sessionResult.setEvalResult(sessionEvalResultParam.getResult());
                    sessionResult.setResultNote(sessionEvalResultParam.getNote());
                    return sessionResult;
                }).collect(Collectors.toList());
            }
        }
        // query维度指标
        // TODO: 2024/3/1 临时逻辑，后续移动到指标计算完后执行
        if (CollectionUtils.isNotEmpty(evalTaskQueryDetailList)) {
            List<Integer> metricIdList = evalTaskQueryDetailList.stream().map(EvalTaskQueryDetailPo::getMetricId).distinct().collect(Collectors.toList());
            List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricIdList.stream().map(Integer::longValue).collect(Collectors.toList()));
            Map<Long, MetricConfigPo> metricConfigMap = metricConfigList.stream().collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));
            List<String> applicationList = evalTaskQueryDetailList.stream().map(EvalTaskQueryDetailPo::getTestApplication).distinct().collect(Collectors.toList());
            for (String application : applicationList) {
                for (Integer metricId : metricIdList) {
                    MetricConfigPo metricConfigPo = metricConfigMap.get(metricId.longValue());
                    // 二级指标不计算结果
                    if (Objects.isNull(metricConfigPo) || MetricClassifyTypeEnum.SUB_METRIC_LIST.contains(metricConfigPo.getType()) || (metricConfigPo.getDimension() != null && metricConfigPo.getDimension() == MetricDimensionEnum.SESSION.getCode())) {
                        continue;
                    }
                    TaskSessionResultDTO.SessionResult sessionResult = new TaskSessionResultDTO.SessionResult();
                    sessionResult.setModel("【" + getName(application) + "】");
                    sessionResult.setMetric("【" + metricConfigPo.getName() + "】");
                    List<EvalTaskQueryDetailPo> detailList = evalTaskQueryDetailList.stream().filter(po -> po.getTestApplication().equals(application) && po.getMetricId().equals(metricId) && po.getSessionId().equals(evalTaskSessionPo.getSessionId())).collect(Collectors.toList());
                    int totalSize = detailList.size();
                    int paasSize = 0;
                    if (MetricTypeEnum.NUMBER.getCode() != metricConfigPo.getMetricType()) {
                        paasSize = getTextMetricPassSize(detailList, scoreThresholdList, metricConfigPo);
                    } else {
                        paasSize = getNumberMetricPassSize(detailList, scoreThresholdList, metricConfigPo);
                    }
                    double score = totalSize == 0 ? 0.0 : (double) paasSize / (double) totalSize;
                    sessionResult.setEvalResult(":合格query占比" + (int) (score * 100) + "%");
                    sessionResultList.add(sessionResult);
                }
            }
        }
        return sessionResultList;
    }

    private String getName(String applicationName) {
        Long applicationId = DataConvertUtil.tryConvertLongWithNull(applicationName);
        if (applicationId == null) {
            return applicationName;
        }
        ApplicationConfigPo applicationConfigPo = applicationConfigGeneratorService.getById(applicationId);
        if (applicationConfigPo == null) {
            return applicationName;
        }
        return applicationConfigPo.getName();
    }

    private int getTextMetricPassSize(List<EvalTaskQueryDetailPo> detailList, List<ScoreThresholdParam> scoreThresholdList, MetricConfigPo metricConfigPo) {
        int paasSize = 0;
        ScoreThresholdParam scoreThresholdParam = commonEvalStrategyService.getScoreThresholdParam(scoreThresholdList, metricConfigPo.getId().intValue());
        if (scoreThresholdParam == null || CollectionUtils.isEmpty(scoreThresholdParam.getScoreThresholdList())) {
            return paasSize;
        }
        List<String> ranges = scoreThresholdParam.getScoreThresholdList();
        for (EvalTaskQueryDetailPo detail : detailList) {
            if (ranges.contains(detail.getMetricResult())) {
                paasSize++;
            }
        }
        return paasSize;
    }

    private int getNumberMetricPassSize(List<EvalTaskQueryDetailPo> detailList, List<ScoreThresholdParam> scoreThresholdList, MetricConfigPo metricConfigPo) {
        int paasSize = 0;
        int metricId = metricConfigPo.getId().intValue();
        double scoreThreshold = commonEvalStrategyService.getScoreThreshold(metricId, commonEvalStrategyService.getAvgEvalScore(scoreThresholdList, metricId), metricConfigPo);
        for (EvalTaskQueryDetailPo detail : detailList) {
            double score;
            if (EvalMetricTypeEnum.CORRECT.getCode() == metricId) {
                Double scoreResult = parseScore(detail.getMetricResult());
                if (scoreResult == null) {
                    continue;
                }
                score = scoreResult;
            } else {
                if (StringUtils.isBlank(detail.getMetricResult())) {
                    continue;
                }
                score = DataConvertUtil.tryConvertDouble(detail.getMetricResult());
            }
            if (score > scoreThreshold) {
                paasSize++;
            }
        }
        return paasSize;
    }

    private Double parseScore(String result) {
        ScoreResult scoreResult = DataConvertUtil.tryConvertJsonObject(result, ScoreResult.class);
        if (scoreResult == null || StringUtils.isBlank(scoreResult.getScore())) {
            return DataConvertUtil.tryConvertDouble(result);
        }
        try {
            return commonMetricStrategyService.parseDoubleGptReply(scoreResult.getScore());
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
            log.error("解析话术正确性结果失败,result={},msg={}", result, e.getMessage(), e);
            return null;
        }
    }

    private List<TaskQueryDTO.Gsb> buildGsbList(List<EvalTaskQueryDetailPo> evalTaskQueryDetailList, EvalTaskPo task) {
        if (task.getType() != null && task.getType() == TaskTypeEnum.ARENA.getCode()) {
            return new ArrayList<>();
        }
        Map<Long, ApplicationConfigPo> applicationConfigPoMap = getApplicationConfigPoMap(evalTaskQueryDetailList);
        List<String> modelList = evalTaskQueryDetailList.stream().map(evalTaskQueryDetailPo -> {
            if (StringUtils.isBlank(evalTaskQueryDetailPo.getTestApplication())) {
                return evalTaskQueryDetailPo.getTestModel();
            }
            Long applicationId = DataConvertUtil.tryConvertLongWithNull(evalTaskQueryDetailPo.getTestApplication());
            if (applicationId == null || !applicationConfigPoMap.containsKey(applicationId)) {
                return evalTaskQueryDetailPo.getTestModel();
            }
            return applicationConfigPoMap.get(applicationId).getName();
        }).distinct().collect(Collectors.toList());
        if (modelList.size() < 2) {
            return new ArrayList<>();
        }
        List<Long> metricIdList = evalTaskQueryDetailList.stream().map(EvalTaskQueryDetailPo::getMetricId).map(Integer::longValue).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(metricIdList)) {
            return new ArrayList<>();
        }
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricIdList);
        Map<Long, MetricConfigPo> metricConfigMap = metricConfigList.stream().collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));
        List<TaskQueryDTO.Gsb> gsbList = new ArrayList<>();
        List<ScoreThresholdParam> scoreThresholdList = parseScoreThresholdList(task);
        for (int i = 0; i < modelList.size() - 1; i++) {
            for (int j = i + 1; j < modelList.size(); j++) {
                for (Long metricId : metricIdList) {
                    MetricConfigPo metricConfig = metricConfigMap.get(metricId);
                    if (MetricTypeEnum.NUMBER.getCode() != metricConfig.getMetricType()) {
                        continue;
                    }
                    String model1 = modelList.get(i);
                    String model2 = modelList.get(j);
                    TaskQueryDTO.Gsb gsb = new TaskQueryDTO.Gsb();
                    gsb.setModel1(model1);
                    gsb.setModel2(model2);
                    gsb.setMetricName(metricConfig.getName());
                    List<EvalTaskQueryDetailPo> modelResult1 = filterByModelAndMetric(evalTaskQueryDetailList, model1, metricId.intValue(), applicationConfigPoMap);
                    List<EvalTaskQueryDetailPo> modelResult2 = filterByModelAndMetric(evalTaskQueryDetailList, model2, metricId.intValue(), applicationConfigPoMap);
                    if (CollectionUtils.isEmpty(modelResult1) || CollectionUtils.isEmpty(modelResult2)) {
                        continue;
                    }
                    double score1 = DataConvertUtil.tryConvertDouble(modelResult1.get(0).getMetricResult());
                    double score2 = DataConvertUtil.tryConvertDouble(modelResult2.get(0).getMetricResult());
                    if (score1 > score2) {
                        gsb.setResult(model1 + "更好");
                    } else if (score1 < score2) {
                        gsb.setResult(model2 + "更好");
                    } else if (score1 < commonEvalStrategyService.getScoreThreshold(metricId.intValue(), commonEvalStrategyService.getAvgEvalScore(scoreThresholdList, metricId.intValue()), metricConfig)) {
                        gsb.setResult("一样差");
                    } else {
                        gsb.setResult("一样好");
                    }
                    gsbList.add(gsb);
                }
            }
        }
        return gsbList;
    }

    private List<ScoreThresholdParam> parseScoreThresholdList(EvalTaskPo task) {
        if (StringUtils.isNotBlank(task.getExtra())) {
            try {
                Map<String, String> extMap = JSON.parseObject(task.getExtra(), new TypeReference<Map<String, String>>() {
                });
                if (extMap.containsKey("scoreThresholdMap")) {
                    return JSON.parseArray(extMap.get("scoreThresholdMap"), ScoreThresholdParam.class);
                }
            } catch (Exception e) {
                Cat.logError(new EvalException(e));
                log.error("解析阈值失败,task={},msg={}", JSON.toJSONString(task), e.getMessage(), e);
            }
        }
        return new ArrayList<>();
    }

    private List<EvalTaskQueryDetailPo> filterByModelAndMetric(List<EvalTaskQueryDetailPo> evalTaskQueryDetailList, String model, int metricId, Map<Long, ApplicationConfigPo> applicationConfigPoMap) {
        return evalTaskQueryDetailList.stream().filter(po -> {
            String testModel;
            if (StringUtils.isBlank(po.getTestApplication())) {
                testModel = po.getTestModel();
            } else {
                Long applicationId = DataConvertUtil.tryConvertLongWithNull(po.getTestApplication());
                if (applicationId == null || !applicationConfigPoMap.containsKey(applicationId)) {
                    testModel = po.getTestModel();
                } else {
                    testModel = applicationConfigPoMap.get(applicationId).getName();
                }
            }
            return testModel.equals(model) && po.getMetricId() == metricId;
        }).collect(Collectors.toList());
    }

    /**
     * 生成竞技场任务报告
     *
     * @param taskPo                  任务
     * @param evalTaskQueryDetailList 按模型分类结果
     */
    private TaskReportDTO buildArenaTaskReport(EvalTaskPo taskPo, List<EvalTaskQueryDetailPo> evalTaskQueryDetailList) {
        Map<String, List<EvalTaskQueryDetailPo>> taskModelResultMap = groupByModel(evalTaskQueryDetailList);
        TaskReportDTO taskReportDTO = new TaskReportDTO();
        taskReportDTO.setBasicInfo(buildArenaTaskBasicInfo(taskPo));
        taskReportDTO.setGsbList(buildArenaGSBList(taskPo, taskModelResultMap));
        return taskReportDTO;
    }

    private Map<String, List<EvalTaskQueryDetailPo>> groupByModel(List<EvalTaskQueryDetailPo> evalTaskQueryDetailList) {
        return evalTaskQueryDetailList.stream().peek(evalTaskQueryDetailPo -> {
            if (StringUtils.isBlank(evalTaskQueryDetailPo.getTestModel())) {
                evalTaskQueryDetailPo.setTestModel("ai搭机器人");
            }
        }).collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getTestModel));
    }

    private TaskReportDTO.BasicInfo buildArenaTaskBasicInfo(EvalTaskPo taskPo) {
        TaskReportDTO.BasicInfo basicInfo = new TaskReportDTO.BasicInfo();
        AbilityEnum abilityEnum = AbilityEnum.parse(taskPo.getAbility());
        basicInfo.setAbilityType(abilityEnum == null ? "" : abilityEnum.getDescription());
        List<Long> metricIdList = Arrays.stream(taskPo.getMetrics().split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricIdList);
        List<String> metricNameList = metricConfigList.stream().map(MetricConfigPo::getName).collect(Collectors.toList());
        basicInfo.setMetric(StringUtils.join(metricNameList, ","));
        basicInfo.setDataset(taskPo.getDatasetIds());
        return basicInfo;
    }


    /**
     * 竞技任务报告中GSB排行榜（都好：两个模型good++；都坏：两个模型bad++）
     */
    private List<TaskReportDTO.Gsb> buildArenaGSBList(EvalTaskPo taskPo, Map<String, List<EvalTaskQueryDetailPo>> taskModelResultMap) {
        List<TaskReportDTO.Gsb> gsbList = new ArrayList<>();
        CommonUtils.checkEval(taskPo != null && !taskPo.getIsDeleted(), "任务不存在或已经删除");
        List<String> selectedModelList = Arrays.stream(taskPo.getTestModel().split(",")).collect(Collectors.toList());
        for (Map.Entry<String, List<EvalTaskQueryDetailPo>> entry : taskModelResultMap.entrySet()) {
            TaskReportDTO.Gsb gsb = new TaskReportDTO.Gsb();
            gsb.setModelName(entry.getKey());
            //竞技任务模型先后顺序
            int modelIndex = selectedModelList.indexOf(entry.getKey());
            int good = 0;
            int bad = 0;
            //按照dataset分组
            Map<Long, List<EvalTaskQueryDetailPo>> datasetTaskQueryDetailMap = entry.getValue().stream().collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getDatasetId));
            for (Map.Entry<Long, List<EvalTaskQueryDetailPo>> datasetEntry : datasetTaskQueryDetailMap.entrySet()) {
                //按照session进行分组
                Map<String, List<EvalTaskQueryDetailPo>> sessionTaskQueryDetailMap = datasetEntry.getValue().stream().collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getSessionId));
                for (Map.Entry<String, List<EvalTaskQueryDetailPo>> sessionEntry : sessionTaskQueryDetailMap.entrySet()) {
                    // 按照gmtModified属性从现在到从前的顺序排序
                    List<EvalTaskQueryDetailPo> orderedTaskQueryDetailList = sessionEntry.getValue().stream().sorted(Comparator.comparing(EvalTaskQueryDetailPo::getGmtModified).reversed()).collect(Collectors.toList());
                    //选取最新的投票结果作为最终投票结果
                    EvalTaskQueryDetailPo evalTaskQueryDetailPo = orderedTaskQueryDetailList.get(0);
                    if (modelIndex == 0) {
                        boolean isAGood = Integer.valueOf(evalTaskQueryDetailPo.getMetricResult()).equals(ArenaVoteResultEnum.PREFER_A.getCode()) || Integer.valueOf(evalTaskQueryDetailPo.getMetricResult()).equals(ArenaVoteResultEnum.BOTH_GOOD.getCode());
                        if (isAGood) {
                            good++;
                        } else {
                            bad++;
                        }
                    } else {
                        boolean isBGood = Integer.valueOf(evalTaskQueryDetailPo.getMetricResult()).equals(ArenaVoteResultEnum.PREFER_B.getCode()) || Integer.valueOf(evalTaskQueryDetailPo.getMetricResult()).equals(ArenaVoteResultEnum.BOTH_GOOD.getCode());
                        if (isBGood) {
                            good++;
                        } else {
                            bad++;
                        }
                    }
                }
            }
            gsb.setGood(good);
            gsb.setBad(bad);
            int total = good + bad;
            gsb.setTotal(total);
            double winRate = total == 0 ? 0.0 : (double) good / total;
            String winRateStr = String.format("%.2f%%", winRate * 100);
            gsb.setWinRate(winRateStr);
            gsbList.add(gsb);

        }
        return gsbList;
    }

    private TaskReportDTO buildTaskReport(EvalTaskPo taskPo, List<EvalTaskQueryDetailPo> evalTaskQueryDetailList, Map<Long, EvalDatasetPo> datasetMap) {
        // 按照应用分组
        List<EvalTaskQueryDetailPo> finishedQueryDetailList = evalTaskQueryDetailList.stream().filter(queryDetailPo -> queryDetailPo.getStatus() != null && queryDetailPo.getStatus() != AutoTaskEvalStatusEnum.EVALUATING.getCode()).collect(Collectors.toList());
        Map<Long, ApplicationConfigPo> applicationConfigPoMap = getApplicationConfigPoMap(finishedQueryDetailList);
        Map<String, List<EvalTaskQueryDetailPo>> taskApplicationResultMap = getApplicationDetailMap(finishedQueryDetailList, applicationConfigPoMap);
        TaskReportDTO taskReportDTO = new TaskReportDTO();
        taskReportDTO.setBasicInfo(buildBasicInfo(taskPo, datasetMap));
        List<EvalTaskSessionPo> sessionList = evalTaskSessionGeneratorService.getByTaskId(taskPo.getId());
        List<Long> metricIdList = finishedQueryDetailList.stream().map(EvalTaskQueryDetailPo::getMetricId).map(Integer::longValue).distinct().collect(Collectors.toList());
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricIdList);
        Map<Long, MetricConfigPo> metricConfigMap = metricConfigList.stream().collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));
        List<Long> applicationId = metricConfigList.stream().map(MetricConfigPo::getApplicationId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<ApplicationConfigPo> applicationConfigList = applicationConfigGeneratorService.getByIdList(applicationId);
        Map<Long, ApplicationConfigPo> applicationConfigMap = applicationConfigList.stream().collect(Collectors.toMap(ApplicationConfigPo::getId, Function.identity()));
        TableDataDTO<TaskReportDTO.Result> resultHeadData = TableDataDTO.create(buildResultList(sessionList, taskApplicationResultMap, datasetMap, metricConfigMap), TaskReportDTO.Result.class);
        taskReportDTO.setCompareList(buildCompareList(taskPo, sessionList, evalTaskQueryDetailList, applicationConfigPoMap));
        List<TableDataDTO<?>> reportList = new ArrayList<>();
        List<TaskReportDTO.Cost> costList = buildCostList(taskPo, metricConfigMap, applicationConfigMap, taskApplicationResultMap);
        List<TaskReportDTO.Performance> performanceList = buildPerformanceList(taskPo, metricConfigMap, applicationConfigMap, taskApplicationResultMap);
        reportList.add(resultHeadData);
        reportList.add(getCostList(costList, TaskReportTypeEnum.ALL, TaskReportDTO.TotalCost.class));
        reportList.add(getCostList(costList, TaskReportTypeEnum.TEST_APPLICATION, TaskReportDTO.ApplicationCost.class));
        reportList.add(getCostList(costList, TaskReportTypeEnum.JUDGE_APPLICATION, TaskReportDTO.JudgeCost.class));
        reportList.add(getPerformanceList(performanceList, TaskReportTypeEnum.TEST_APPLICATION, TaskReportDTO.ApplicationPerformance.class));
        reportList.add(getPerformanceList(performanceList, TaskReportTypeEnum.JUDGE_APPLICATION, TaskReportDTO.JudgePerformance.class));
        taskReportDTO.setReportList(reportList);
        return taskReportDTO;
    }

    private <T> TableDataDTO<T> getCostList(List<TaskReportDTO.Cost> costList, TaskReportTypeEnum taskReportTypeEnum, Class<T> clazz) {
        List<T> tList = costList.stream().filter(cost -> taskReportTypeEnum.getDescription().equals(cost.getType()))
                .map(cost -> {
                    try {
                        T t = clazz.newInstance();
                        BeanUtils.copyProperties(cost, t);
                        return t;
                    } catch (Exception e) {
                        throw new EvalException(e);
                    }
                })
                .collect(Collectors.toList());
        return TableDataDTO.create(tList, clazz);
    }

    private <T> TableDataDTO<T> getPerformanceList(List<TaskReportDTO.Performance> performanceList, TaskReportTypeEnum taskReportTypeEnum, Class<T> clazz) {
        List<T> tList = performanceList.stream().filter(performance -> taskReportTypeEnum.getDescription().equals(performance.getType()))
                .map(performance -> {
                    try {
                        T t = clazz.newInstance();
                        BeanUtils.copyProperties(performance, t);
                        return t;
                    } catch (Exception e) {
                        throw new EvalException(e);
                    }
                })
                .collect(Collectors.toList());
        return TableDataDTO.create(tList, clazz);
    }

    private List<TaskReportDTO.Cost> buildCostList(EvalTaskPo taskPo, Map<Long, MetricConfigPo> metricConfigMap, Map<Long, ApplicationConfigPo> applicationConfigMap, Map<String, List<EvalTaskQueryDetailPo>> taskModelResultMap) {
        List<TaskReportDTO.Cost> costList = new ArrayList<>();
        for (Map.Entry<String, List<EvalTaskQueryDetailPo>> entry : taskModelResultMap.entrySet()) {
            List<Long> queryDetailIdList = entry.getValue().stream().map(EvalTaskQueryDetailPo::getId).distinct().collect(Collectors.toList());
            List<EvalTaskLogPo> taskLogList = evalTaskLogGeneratorService.getByQueryDetailIdList(queryDetailIdList);
            List<EvalTaskLogPo> testApplicationLogList = taskLogList.stream().filter(log -> log.getLogType() == TaskLogTypeEnum.TEST_APPLICATION.getCode()).collect(Collectors.toList());
            List<TaskReportDTO.Cost> applicationCostList = new ArrayList<>();
            // 被测应用
            int totalSize = testApplicationLogList.stream().map(EvalTaskLogPo::getQueryId).collect(Collectors.toSet()).size();
            String applicationName = getApplicationName(entry.getKey());
            String applicationModelName = getModelName(entry.getKey());
            TaskReportDTO.Cost testCost = buildCost(totalSize, testApplicationLogList, applicationName, applicationModelName, null, null, TaskReportTypeEnum.TEST_APPLICATION);
            applicationCostList.add(testCost);
            // 评测应用
            Map<Integer, List<EvalTaskQueryDetailPo>> metricResultMap = entry.getValue().stream().collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getMetricId));
            for (Map.Entry<Integer, List<EvalTaskQueryDetailPo>> metricResultEntry : metricResultMap.entrySet()) {
                List<Long> metricQueryDetailIdList = metricResultEntry.getValue().stream().map(EvalTaskQueryDetailPo::getId).distinct().collect(Collectors.toList());
                MetricConfigPo metricConfigPo = metricConfigMap.get(metricResultEntry.getKey().longValue());
                List<EvalTaskLogPo> judgeApplicationLogList;
                if (metricConfigPo.getDimension() == null || metricConfigPo.getDimension() == MetricDimensionEnum.QUERY.getCode()) {
                    List<EvalTaskLogPo> metricTaskLogList = evalTaskLogGeneratorService.getByQueryDetailIdList(metricQueryDetailIdList);
                    judgeApplicationLogList = metricTaskLogList.stream().filter(log -> log.getLogType() == TaskLogTypeEnum.QUERY_METRIC.getCode()).collect(Collectors.toList());
                } else {
                    TaskLogConditionParam condition = new TaskLogConditionParam();
                    condition.setTaskId(taskPo.getId());
                    condition.setType(TaskLogTypeEnum.SESSION_METRIC.getCode());
                    condition.setApplication(entry.getValue().get(0).getTestApplication());
                    condition.setMetricId(metricConfigPo.getId());
                    judgeApplicationLogList = evalTaskLogGeneratorService.getByCondition(condition);
                }
                int metricTotalSize = judgeApplicationLogList.size();
                String metricModelName = metricConfigPo.getApplicationId() != null ? commonEvalStrategyService.getModelName(applicationConfigMap.get(metricConfigPo.getApplicationId())) : null;
                TaskReportDTO.Cost judgeCost = buildCost(metricTotalSize, judgeApplicationLogList, applicationName, applicationModelName, StringUtils.isNotBlank(metricModelName) ? metricModelName : "-", metricConfigPo, TaskReportTypeEnum.JUDGE_APPLICATION);
                applicationCostList.add(judgeCost);
            }
            // 总调用统计
            TaskReportDTO.Cost totalCost = buildTotalCost(applicationCostList, entry.getKey());
            costList.add(totalCost);
            costList.addAll(applicationCostList);
        }
        return costList;
    }

    private TaskReportDTO.Cost buildTotalCost(List<TaskReportDTO.Cost> applicationCostList, String application) {
        TaskReportDTO.Cost totalCost = new TaskReportDTO.Cost();
        int invokeNum = 0;
        int totalInputTokenNum = 0;
        int totalOutputTokenNum = 0;
        double totalInputTokenCost = 0;
        double totalOutputTokenCost = 0;
        for (TaskReportDTO.Cost cost : applicationCostList) {
            if (cost.getToken() != 0) {
                invokeNum += cost.getInvokeNum();
            }
            totalInputTokenNum += cost.getInputToken();
            totalOutputTokenNum += cost.getOutputToken();
            totalInputTokenCost += cost.getInputTotalCost();
            totalOutputTokenCost += cost.getOutputTotalCost();
        }
        totalCost.setApplicationName(getApplicationName(application));
        totalCost.setApplicationModelName(getModelName(application));
        totalCost.setType(TaskReportTypeEnum.ALL.getDescription());
        totalCost.setInvokeNum(invokeNum);
        totalCost.setInputPrice("-");
        totalCost.setOutputPrice("-");
        totalCost.setToken(totalInputTokenNum + totalOutputTokenNum);
        totalCost.setInputToken(totalInputTokenNum);
        totalCost.setOutputToken(totalOutputTokenNum);
        totalCost.setTokenCost(invokeNum == 0 ? 0 : (double) (int) ((double) (totalInputTokenNum + totalOutputTokenNum) / (double) invokeNum));
        totalCost.setInputTotalCost(MathUtils.save5Double(totalInputTokenCost));
        totalCost.setOutputTotalCost(MathUtils.save5Double(totalOutputTokenCost));
        totalCost.setTotalCost(MathUtils.save5Double(totalInputTokenCost + totalOutputTokenCost));
        totalCost.setPerTimeCost(invokeNum == 0 ? 0 : MathUtils.save5Double((totalInputTokenCost + totalOutputTokenCost) / (double) invokeNum));
        return totalCost;
    }

    private TaskReportDTO.Cost buildCost(int totalSize, List<EvalTaskLogPo> taskLogList, String applicationName, String modelName, String metricModelName, MetricConfigPo metricConfig, TaskReportTypeEnum taskCostType) {
        List<ModelDTO> modelDTOList = Lion.getList(ConfigUtil.getAppkey(), LionConstants.MODEL_CONFIG_LIST, ModelDTO.class);
        Map<String, ModelDTO> modelDTOMap = modelDTOList.stream().collect(Collectors.toMap(ModelDTO::getModelName, Function.identity()));
        TaskReportDTO.Cost cost = new TaskReportDTO.Cost();
        cost.setApplicationName(applicationName);
        cost.setApplicationModelName(modelName);
        cost.setType(taskCostType.getDescription());
        String invokeModelName = modelName;
        if (TaskReportTypeEnum.JUDGE_APPLICATION.equals(taskCostType)) {
            cost.setMetricName(metricConfig.getName());
            cost.setMetricModelName(metricModelName);
            invokeModelName = metricModelName;
        }
        cost.setInvokeNum(totalSize);
        double inputCost = 0.0;
        double outputCost = 0.0;
        if (!"-".equals(invokeModelName) && modelDTOMap.containsKey(invokeModelName)) {
            inputCost = DataConvertUtil.tryConvertDouble(modelDTOMap.get(invokeModelName).getInputPrice());
            outputCost = DataConvertUtil.tryConvertDouble(modelDTOMap.get(invokeModelName).getOutputPrice());
        }
        cost.setInputPrice(MathUtils.save2DoubleWithEmpty(inputCost));
        cost.setOutputPrice(MathUtils.save2DoubleWithEmpty(outputCost));
        int inputTokenNum = 0;
        int outputTokenNum = 0;
        double inputTokenCost = 0.0;
        double outputTokenCost = 0.0;
        for (EvalTaskLogPo taskLogPo : taskLogList) {
            String costToken = taskLogPo.getCostToken();
            if (StringUtils.isNotBlank(costToken)) {
                TokenParam tokenParam = JSON.parseObject(costToken, TokenParam.class);
                inputTokenNum += (null == tokenParam.getInputTokenNum() ? 0 : tokenParam.getInputTokenNum());
                outputTokenNum += (null == tokenParam.getOutputTokenNum() ? 0 : tokenParam.getOutputTokenNum());
                inputTokenCost += (null == tokenParam.getInputTokenNum() ? 0 : tokenParam.getInputTokenNum() * inputCost / 1000000);
                outputTokenCost += (null == tokenParam.getOutputTokenNum() ? 0 : tokenParam.getOutputTokenNum() * outputCost / 1000000);
            }
        }

        cost.setToken(inputTokenNum + outputTokenNum);
        cost.setTokenCost(totalSize == 0 ? 0 : (double) (int) ((double) (inputTokenNum + outputTokenNum) / (double) totalSize));
        cost.setInputToken(inputTokenNum);
        cost.setOutputToken(outputTokenNum);
        cost.setInputTotalCost(MathUtils.save5Double(inputTokenCost));
        cost.setOutputTotalCost(MathUtils.save5Double(outputTokenCost));
        cost.setTotalCost(MathUtils.save5Double(inputTokenCost + outputTokenCost));
        cost.setPerTimeCost(totalSize == 0 ? 0 : MathUtils.save5Double((inputTokenCost + outputTokenCost) / (double) totalSize));
        return cost;
    }

    private String getModel(List<EvalTaskLogPo> taskLogList, String model) {
        if (CollectionUtils.isNotEmpty(taskLogList)) {
            EvalTaskLogPo taskLog = taskLogList.get(0);
            if (taskLog != null && StringUtils.isNotBlank(taskLog.getContent())) {
                GptContextParam param = JSON.parseObject(taskLog.getContent(), GptContextParam.class);
                if (param != null && param.getModelConfig() != null) {
                    return param.getModelConfig().getModelName();
                }
            }
        }
        return model;
    }

    private List<TaskReportDTO.Performance> buildPerformanceList(EvalTaskPo taskPo, Map<Long, MetricConfigPo> metricConfigMap, Map<Long, ApplicationConfigPo> applicationConfigMap, Map<String, List<EvalTaskQueryDetailPo>> taskModelResultMap) {
        List<TaskReportDTO.Performance> performanceList = new ArrayList<>();
        for (Map.Entry<String, List<EvalTaskQueryDetailPo>> entry : taskModelResultMap.entrySet()) {
            List<Long> queryDetailIdList = entry.getValue().stream().map(EvalTaskQueryDetailPo::getId).distinct().collect(Collectors.toList());
            List<EvalTaskLogPo> taskLogList = evalTaskLogGeneratorService.getByQueryDetailIdList(queryDetailIdList);
            List<EvalTaskLogPo> testApplicationLogList = taskLogList.stream().filter(log -> log.getLogType() == TaskLogTypeEnum.TEST_APPLICATION.getCode()).collect(Collectors.toList());
            List<TaskReportDTO.Performance> applicationPerformanceList = new ArrayList<>();
            // 被测应用
            int totalSize = testApplicationLogList.stream().map(EvalTaskLogPo::getQueryId).collect(Collectors.toSet()).size();
            int successSize = (int) testApplicationLogList.stream().filter(log -> log.getIsSuccess() == ApplicationResultEnum.SUCCESS.getCode()).map(EvalTaskLogPo::getQueryId).distinct().count();
            TaskReportDTO.Performance testPerformance = buildPerformance(totalSize, successSize, testApplicationLogList, entry.getKey(), null, null, TaskReportTypeEnum.TEST_APPLICATION);
            applicationPerformanceList.add(testPerformance);
            // 评测应用
            Map<Integer, List<EvalTaskQueryDetailPo>> metricResultMap = entry.getValue().stream().collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getMetricId));
            for (Map.Entry<Integer, List<EvalTaskQueryDetailPo>> metricResultEntry : metricResultMap.entrySet()) {
                MetricConfigPo metricConfig = metricConfigMap.get(metricResultEntry.getKey().longValue());
                List<Long> metricQueryDetailIdList = metricResultEntry.getValue().stream().map(EvalTaskQueryDetailPo::getId).distinct().collect(Collectors.toList());
                List<EvalTaskLogPo> judgeApplicationLogList;
                if (metricConfig.getDimension() == null || metricConfig.getDimension() == MetricDimensionEnum.QUERY.getCode()) {
                    List<EvalTaskLogPo> metricTaskLogList = evalTaskLogGeneratorService.getByQueryDetailIdList(metricQueryDetailIdList);
                    judgeApplicationLogList = metricTaskLogList.stream().filter(log -> log.getLogType() == TaskLogTypeEnum.QUERY_METRIC.getCode()).collect(Collectors.toList());
                } else {
                    TaskLogConditionParam condition = new TaskLogConditionParam();
                    condition.setTaskId(taskPo.getId());
                    condition.setType(TaskLogTypeEnum.SESSION_METRIC.getCode());
                    condition.setApplication(entry.getValue().get(0).getTestApplication());
                    condition.setMetricId(metricConfig.getId());
                    judgeApplicationLogList = evalTaskLogGeneratorService.getByCondition(condition);
                }
                int metricTotalSize = judgeApplicationLogList.size();
                int judgeSuccessSize = (int) judgeApplicationLogList.stream().filter(log -> log.getIsSuccess() == ApplicationResultEnum.SUCCESS.getCode()).count();
                String metricModelName = metricConfig.getApplicationId() != null ? commonEvalStrategyService.getModelName(applicationConfigMap.get(metricConfig.getApplicationId())) : null;
                TaskReportDTO.Performance judgePerformance = buildPerformance(metricTotalSize, judgeSuccessSize, judgeApplicationLogList, entry.getKey(), StringUtils.isNotBlank(metricModelName) ? metricModelName : "-", metricConfig, TaskReportTypeEnum.JUDGE_APPLICATION);
                applicationPerformanceList.add(judgePerformance);
            }
            performanceList.addAll(applicationPerformanceList);
        }
        return performanceList;
    }

    private TaskReportDTO.Performance buildPerformance(int totalSize, int successSize, List<EvalTaskLogPo> taskLogList, String application, String metricModelName, MetricConfigPo metricConfig, TaskReportTypeEnum taskCostType) {
        TaskReportDTO.Performance performance = new TaskReportDTO.Performance();
        performance.setApplicationName(getApplicationName(application));
        performance.setApplicationModelName(getModelName(application));
        if (TaskReportTypeEnum.JUDGE_APPLICATION.equals(taskCostType)) {
            performance.setMetricName(metricConfig.getName());
            performance.setMetricModelName(metricModelName);
        }
        performance.setType(taskCostType.getDescription());
        performance.setInvokeNum(totalSize);
        performance.setSuccessRatio(totalSize == 0 ? 0 : MathUtils.save2Double((double) successSize / (double) totalSize * 100));
        performance.setSuccessSize(successSize);
        AtomicInteger totalTime = new AtomicInteger();
        List<EvalTaskLogPo> executeTimeList = taskLogList.stream().filter(evalTaskLogPo -> evalTaskLogPo.getCostTime() != null).peek(evalTaskLogPo -> totalTime.addAndGet(evalTaskLogPo.getCostTime())).sorted(Comparator.comparing(EvalTaskLogPo::getCostTime)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(executeTimeList)) {
            int size = executeTimeList.size();
            performance.setAvgTime(totalTime.get() / size);
            performance.setMinTime(executeTimeList.get(0).getCostTime());
            performance.setMaxTime(executeTimeList.get(size - 1).getCostTime());
            performance.setTp95(executeTimeList.get((int) (size * 0.95)).getCostTime());
            performance.setTp99(executeTimeList.get((int) (size * 0.99)).getCostTime());
            performance.setTp999(executeTimeList.get((int) (size * 0.999)).getCostTime());
        }
        return performance;
    }

    private List<List<TaskReportDTO.CompareResult>> buildCompareList(EvalTaskPo task, List<EvalTaskSessionPo> sessionList, List<EvalTaskQueryDetailPo> evalTaskQueryDetailList, Map<Long, ApplicationConfigPo> applicationConfigPoMap) {
        if (!AutoTaskStatusEnum.FINISHED_SUCCESS_STATUS.contains(task.getStatus())) {
            return new ArrayList<>();
        }
        List<String> modelList = evalTaskQueryDetailList.stream().map(evalTaskQueryDetailPo -> {
            if (StringUtils.isBlank(evalTaskQueryDetailPo.getTestApplication())) {
                return evalTaskQueryDetailPo.getTestModel();
            }
            Long applicationId = DataConvertUtil.tryConvertLongWithNull(evalTaskQueryDetailPo.getTestApplication());
            if (applicationId == null || !applicationConfigPoMap.containsKey(applicationId)) {
                return evalTaskQueryDetailPo.getTestModel();
            }
            return applicationConfigPoMap.get(applicationId).getName();
        }).distinct().collect(Collectors.toList());
        if (modelList.size() < 2) {
            return new ArrayList<>();
        }
        List<Long> metricIdList = evalTaskQueryDetailList.stream().map(EvalTaskQueryDetailPo::getMetricId).map(Integer::longValue).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(metricIdList)) {
            return new ArrayList<>();
        }
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricIdList);
        Map<Long, MetricConfigPo> metricConfigMap = metricConfigList.stream().collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));
        List<List<TaskReportDTO.CompareResult>> compareResultList = new ArrayList<>();
        Map<String, Map<Long, String>> sessionResultMap = sessionList.stream().flatMap(session -> {
            if (StringUtils.isNotBlank(session.getSessionResult())) {
                List<SessionEvalResultParam> sessionResultList = JSON.parseArray(session.getSessionResult(), SessionEvalResultParam.class);
                if (CollectionUtils.isNotEmpty(sessionResultList)) {
                    return sessionResultList.stream().map(sessionEvalResultParam -> new AbstractMap.SimpleEntry<>(applicationConfigPoMap.get(Long.parseLong(sessionEvalResultParam.getApplication())).getName() + sessionEvalResultParam.getMetricId(), new AbstractMap.SimpleEntry<>(session.getId(), sessionEvalResultParam.getResult())));
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.groupingBy(Map.Entry::getKey, Collectors.toMap(entry -> entry.getValue().getKey(), entry -> entry.getValue().getValue())));
        for (int i = 0; i < modelList.size() - 1; i++) {
            for (int j = i + 1; j < modelList.size(); j++) {
                for (Long metricId : metricIdList) {
                    MetricConfigPo metricConfig = metricConfigMap.get(metricId);
                    if (MetricTypeEnum.NUMBER.getCode() != metricConfig.getMetricType()) {
                        continue;
                    }
                    String model1 = modelList.get(i);
                    String model2 = modelList.get(j);
                    List<TaskReportDTO.CompareResult> compareResult;
                    if (metricConfig.getDimension() != null && metricConfig.getDimension() == MetricDimensionEnum.SESSION.getCode()) {
                        compareResult = getSessionCompareResult(sessionResultMap, applicationConfigPoMap, model1, model2, metricConfig);
                    } else {
                        compareResult = getQueryCompareResult(evalTaskQueryDetailList, applicationConfigPoMap, model1, model2, metricConfig);
                    }
                    if (compareResult != null) {
                        compareResultList.add(compareResult);
                    }
                }
            }
        }
        return compareResultList;
    }

    private List<TaskReportDTO.CompareResult> getSessionCompareResult(Map<String, Map<Long, String>> sessionResultMap, Map<Long, ApplicationConfigPo> applicationConfigPoMap, String model1, String model2, MetricConfigPo metricConfig) {
        Map<Long, String> resultList1 = sessionResultMap.get(model1 + metricConfig.getId());
        Map<Long, String> resultList2 = sessionResultMap.get(model2 + metricConfig.getId());
        if (MapUtils.isEmpty(resultList1) || MapUtils.isEmpty(resultList2)) {
            return null;
        }
        List<TaskReportDTO.CompareResult> compareResult = new ArrayList<>();
        int totalSize = 0;
        TaskReportDTO.CompareResult result1 = createGsb(model1, metricConfig.getName());
        compareResult.add(result1);
        TaskReportDTO.CompareResult result2 = createGsb(model2, metricConfig.getName());
        compareResult.add(result2);
        for (Map.Entry<Long, String> entry : resultList1.entrySet()) {
            String result = resultList2.get(entry.getKey());
            if (StringUtils.isBlank(result)) {
                continue;
            }
            double score1 = StringUtils.isBlank(entry.getValue()) ? 0.0 : DataConvertUtil.tryConvertDouble(entry.getValue());
            double score2 = StringUtils.isBlank(result) ? 0.0 : DataConvertUtil.tryConvertDouble(result);
            if (score1 > score2) {
                result1.setGood(result1.getGood() + 1);
                result2.setBad(result2.getBad() + 1);
            } else if (score1 < score2) {
                result2.setGood(result2.getGood() + 1);
                result1.setBad(result1.getBad() + 1);
            } else {
                result1.setSame(result1.getSame() + 1);
                result2.setSame(result2.getSame() + 1);
            }
            totalSize++;
        }
        result1.setGood(totalSize == 0 ? 0 : (int) (MathUtils.save2Double((result1.getGood()) / (double) totalSize) * 100));
        result1.setSame(totalSize == 0 ? 0 : (int) (MathUtils.save2Double((result1.getSame()) / (double) totalSize) * 100));
        result1.setBad(100 - result1.getGood() - result1.getSame());
        result2.setBad(totalSize == 0 ? 0 : (int) (MathUtils.save2Double((result2.getBad()) / (double) totalSize) * 100));
        result2.setSame(totalSize == 0 ? 0 : (int) (MathUtils.save2Double((result2.getSame()) / (double) totalSize) * 100));
        result2.setGood(100 - result2.getBad() - result2.getSame());
        return compareResult;
    }

    private List<TaskReportDTO.CompareResult> getQueryCompareResult(List<EvalTaskQueryDetailPo> evalTaskQueryDetailList, Map<Long, ApplicationConfigPo> applicationConfigPoMap, String model1, String model2, MetricConfigPo metricConfig) {
        List<EvalTaskQueryDetailPo> modelResult1 = filterByModelAndMetric(evalTaskQueryDetailList, model1, metricConfig.getId().intValue(), applicationConfigPoMap);
        List<EvalTaskQueryDetailPo> modelResult2 = filterByModelAndMetric(evalTaskQueryDetailList, model2, metricConfig.getId().intValue(), applicationConfigPoMap);
        if (CollectionUtils.isEmpty(modelResult1) || CollectionUtils.isEmpty(modelResult2)) {
            return null;
        }
        List<TaskReportDTO.CompareResult> compareResult = new ArrayList<>();
        Map<Long, List<EvalTaskQueryDetailPo>> modelResultMap = modelResult2.stream().collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getQueryId));
        TaskReportDTO.CompareResult result1 = createGsb(model1, metricConfig.getName());
        compareResult.add(result1);
        TaskReportDTO.CompareResult result2 = createGsb(model2, metricConfig.getName());
        compareResult.add(result2);
        int totalSize = 0;
        for (EvalTaskQueryDetailPo modelQuery1 : modelResult1) {
            EvalTaskQueryDetailPo modelQuery2 = modelResultMap.get(modelQuery1.getQueryId()).get(0);
            if (modelQuery2 == null) {
                continue;
            }
            double score1 = StringUtils.isBlank(modelQuery1.getMetricResult()) ? 0.0 : DataConvertUtil.tryConvertDouble(modelQuery1.getMetricResult());
            double score2 = StringUtils.isBlank(modelQuery2.getMetricResult()) ? 0.0 : DataConvertUtil.tryConvertDouble(modelQuery2.getMetricResult());
            if (score1 > score2) {
                result1.setGood(result1.getGood() + 1);
                result2.setBad(result2.getBad() + 1);
            } else if (score1 < score2) {
                result2.setGood(result2.getGood() + 1);
                result1.setBad(result1.getBad() + 1);
            } else {
                result1.setSame(result1.getSame() + 1);
                result2.setSame(result2.getSame() + 1);
            }
            totalSize++;
        }
        result1.setGood(totalSize == 0 ? 0 : (int) (MathUtils.save2Double((result1.getGood()) / (double) totalSize) * 100));
        result1.setSame(totalSize == 0 ? 0 : (int) (MathUtils.save2Double((result1.getSame()) / (double) totalSize) * 100));
        result1.setBad(100 - result1.getGood() - result1.getSame());
        result2.setBad(totalSize == 0 ? 0 : (int) (MathUtils.save2Double((result2.getBad()) / (double) totalSize) * 100));
        result2.setSame(totalSize == 0 ? 0 : (int) (MathUtils.save2Double((result2.getSame()) / (double) totalSize) * 100));
        result2.setGood(100 - result2.getBad() - result2.getSame());
        return compareResult;
    }

    private List<TaskReportDTO.Result> buildResultList(List<EvalTaskSessionPo> sessionList, Map<String, List<EvalTaskQueryDetailPo>> taskModelResultMap, Map<Long, EvalDatasetPo> datasetMap, Map<Long, MetricConfigPo> metricConfigMap) {
        List<TaskReportDTO.Result> resultList = new ArrayList<>();
        Map<Long, List<EvalTaskSessionPo>> sessionMap = sessionList.stream().collect(Collectors.groupingBy(EvalTaskSessionPo::getDatasetId));
        for (Map.Entry<String, List<EvalTaskQueryDetailPo>> modelEntry : taskModelResultMap.entrySet()) {
            String application = modelEntry.getValue().get(0).getTestApplication();
            // 按照数据集分组
            Map<Long, List<EvalTaskQueryDetailPo>> datasetResultMap = modelEntry.getValue().stream().collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getDatasetId));
            for (Map.Entry<Long, List<EvalTaskQueryDetailPo>> datasetEntry : datasetResultMap.entrySet()) {
                // 按照指标分组
                Map<Integer, List<EvalTaskQueryDetailPo>> metricResultMap = datasetEntry.getValue().stream().collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getMetricId));
                for (Map.Entry<Integer, List<EvalTaskQueryDetailPo>> metricEntry : metricResultMap.entrySet()) {
                    MetricConfigPo metricConfig = metricConfigMap.get(metricEntry.getKey().longValue());
                    TaskReportDTO.Result result = new TaskReportDTO.Result();
                    result.setApplicationName(getApplicationName(modelEntry.getKey()));
                    result.setModelName(getModelName(modelEntry.getKey()));
                    result.setDatasetId(datasetEntry.getKey());
                    result.setDatasetName(datasetMap.get(datasetEntry.getKey()).getName());
                    result.setMetricId(metricEntry.getKey());
                    result.setMetricName(metricConfig.getName());
                    if (metricConfig.getMetricType() == MetricTypeEnum.NUMBER.getCode()) {
                        double minValue;
                        double maxValue;
                        if (MetricClassifyTypeEnum.COMMON.getCode() == metricConfig.getType()) {
                            minValue = 0.0;
                            maxValue = 1.0;
                        } else {
                            JSONObject jsonObject = JSON.parseObject(metricConfig.getRanges());
                            minValue = jsonObject.getDoubleValue("startScore");
                            maxValue = jsonObject.getDoubleValue("endScore");
                        }
                        double score = minValue;
                        int size = 0;
                        if (metricConfig.getDimension() == null || metricConfig.getDimension() == MetricDimensionEnum.QUERY.getCode()) {
                            for (EvalTaskQueryDetailPo evalTaskQueryDetail : metricEntry.getValue()) {
                                score += StringUtils.isBlank(evalTaskQueryDetail.getMetricResult()) ? minValue : DataConvertUtil.tryConvertDouble(evalTaskQueryDetail.getMetricResult());
                            }
                            size = metricEntry.getValue().size();
                        } else {
                            for (EvalTaskSessionPo session : sessionMap.get(datasetEntry.getKey())) {
                                if (StringUtils.isNotBlank(session.getSessionResult())) {
                                    List<SessionEvalResultParam> sessionResultList = JSON.parseArray(session.getSessionResult(), SessionEvalResultParam.class);
                                    for (SessionEvalResultParam sessionResult : sessionResultList) {
                                        if (sessionResult.getMetricId().equals(metricEntry.getKey()) && sessionResult.getApplication().equals(application)) {
                                            score += StringUtils.isBlank(sessionResult.getResult()) ? minValue : DataConvertUtil.tryConvertDouble(sessionResult.getResult());
                                            size++;
                                        }
                                    }
                                }
                            }
                        }
                        // 转换公式 new_value = (old_value - min_value) * (new_max - new_min) / (max_value - min_value) + new_min，其中new_max=100，new_min=0
                        result.setDatasetScore(size == 0 ? 0 : MathUtils.save2Double((score / size - minValue) * 100 / (maxValue - minValue)));
                    }
                    resultList.add(result);
                }
            }
        }
        return resultList;
    }

    private String getApplicationName(String application) {
        if (StringUtils.isNotBlank(application)) {
            String[] applicationInfo = application.split(NAME_SPLIT);
            if (applicationInfo.length > 0) {
                return applicationInfo[0];
            }
        }
        return application;
    }

    private String getModelName(String application) {
        if (StringUtils.isNotBlank(application)) {
            String[] applicationInfo = application.split(NAME_SPLIT);
            if (applicationInfo.length > 1) {
                return applicationInfo[1];
            }
        }
        return "-";
    }

    private TaskReportDTO.CompareResult createGsb(String modelName, String metricName) {
        TaskReportDTO.CompareResult result = new TaskReportDTO.CompareResult();
        result.setMetricName(metricName);
        result.setModelName(modelName);
        result.setSame(0);
        result.setGood(0);
        result.setBad(0);
        return result;
    }

    private TaskReportDTO.BasicInfo buildBasicInfo(EvalTaskPo taskPo, Map<Long, EvalDatasetPo> datasetMap) {
        TaskReportDTO.BasicInfo basicInfo = new TaskReportDTO.BasicInfo();
        AbilityEnum abilityEnum = AbilityEnum.parse(taskPo.getAbility());
        basicInfo.setAbilityType(abilityEnum == null ? "" : abilityEnum.getDescription());
        List<Long> metricIdList = Arrays.stream(taskPo.getMetrics().split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricIdList);
        List<String> metricNameList = metricConfigList.stream().map(MetricConfigPo::getName).collect(Collectors.toList());
        basicInfo.setMetric(StringUtils.join(metricNameList, ","));
        List<String> datasetNameList = datasetMap.values().stream().map(EvalDatasetPo::getName).collect(Collectors.toList());
        basicInfo.setDataset(StringUtils.join(datasetNameList, ","));
        return basicInfo;
    }

    private PageData<TaskDTO> convertInspectTaskPageInfo(int totalNum, int pageNum, int pageSize, List<AutoInspectDetailPo> dataList, TaskConditionParam param) {
        List<TaskDTO> result = new ArrayList<>();
        List<AutoInspectDetailPo> hasTaskList = dataList.stream().filter(autoInspectDetailPo -> autoInspectDetailPo.getTaskId() != null).collect(Collectors.toList());
        List<AutoInspectDetailPo> noTaskList = dataList.stream().filter(autoInspectDetailPo -> autoInspectDetailPo.getTaskId() == null).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(hasTaskList)) {
            List<Long> taskIdList = hasTaskList.stream().map(AutoInspectDetailPo::getTaskId).collect(Collectors.toList());
            List<EvalTaskPo> taskList = evalTaskGeneratorService.getByIdList(taskIdList);
            PageData<TaskDTO> pageData = convertTaskPageInfo(totalNum, pageNum, pageSize, taskList, param);
            result.addAll(pageData.getData());
        }
        if (CollectionUtils.isNotEmpty(noTaskList)) {
            result.addAll(buildTaskDTOFromInspectDetail(noTaskList));
        }
        result.sort(Comparator.comparing(TaskDTO::getUpdateTime).reversed());
        return PageData.create(totalNum, pageNum, pageSize, result);
    }

    private PageData<TaskDTO> convertTaskPageInfo(int totalNum, int pageNum, int pageSize, List<EvalTaskPo> dataList, TaskConditionParam param) {
        // 根据数据集id，查名称
        List<Long> datasetIdList = new ArrayList<>();
        dataList.forEach(evalTaskPo -> {
            if (StringUtils.isNotBlank(evalTaskPo.getDatasetIds())) {
                datasetIdList.addAll(Arrays.stream(evalTaskPo.getDatasetIds().split(",")).map(Long::parseLong).collect(Collectors.toList()));
            }
        });
        List<EvalDatasetPo> evalDatasetList = evalDatasetGeneratorService.getByIdList(datasetIdList);
        Map<Long, EvalDatasetPo> evalDatasetMap = evalDatasetList.stream().collect(Collectors.toMap(EvalDatasetPo::getId, Function.identity()));
        // 根据任务id查询任务进度
        List<Long> taskIdList = dataList.stream().map(EvalTaskPo::getId).collect(Collectors.toList());
        List<TaskDetailCountDTO> queryCountList = evalTaskQueryGeneratorService.getQueryCount(taskIdList,TaskQueryStatusEnum.COMPLETE_STATUS);
        Map<Long, TaskDetailCountDTO> taskQueryMap = Optional.ofNullable(queryCountList).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(TaskDetailCountDTO::getTaskId,Function.identity(),(k1,k2)->k1));
        Map<Long, TaskDetailCountDTO> sessionMap = getSessionMap(taskIdList);

        List<AutoInspectDetailPo> autoInspectDetailPos = autoInspectDetailGeneratorService.getByTaskIdList(taskIdList);
        Map<Long, AutoInspectDetailPo> autoInspectDetailMap = autoInspectDetailPos.stream().collect(Collectors.toMap(AutoInspectDetailPo::getTaskId, Function.identity()));

        //metric
        List<Long> metricIds = Optional.ofNullable(dataList).orElse(Collections.emptyList()).stream().map(EvalTaskPo::getMetrics).map(metrics -> metrics.split(",")).flatMap(Arrays::stream).distinct().map(Long::parseLong).collect(Collectors.toList());
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricIds);
        Map<Long, MetricConfigPo> metricConfigMap = Optional.ofNullable(metricConfigList).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(MetricConfigPo::getId, Function.identity(), (k1, k2) -> k1));
        //application
        Map<Long, ApplicationConfigPo> applicationConfigMap = getApplicationMap(dataList);

        // 获取模型版本映射
        Map<String, String> aidaVersionMap = getAidaVersionMap();
        List<TaskDTO> taskList = dataList.stream().map(evalTaskPo -> {
            TaskDTO taskDTO = new TaskDTO();
            taskDTO.setId(evalTaskPo.getId());
            taskDTO.setName(evalTaskPo.getName());
            taskDTO.setInputSource(evalTaskPo.getInputSource());
            AbilityEnum abilityEnum = AbilityEnum.parse(evalTaskPo.getAbility());
            taskDTO.setAbilityType(abilityEnum == null ? AbilityEnum.SINGLE_ROUND.getCode() : abilityEnum.getCode());
            List<String> datasetNames = new ArrayList<>();
            if (StringUtils.isNotBlank(evalTaskPo.getDatasetIds())) {
                List<Long> datasetIds = Arrays.stream(evalTaskPo.getDatasetIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
                datasetIds.forEach(datasetId -> {
                    if (evalDatasetMap.containsKey(datasetId)) {
                        datasetNames.add(evalDatasetMap.get(datasetId).getName());
                    }
                });
                taskDTO.setDatasetIdList(Arrays.stream(evalTaskPo.getDatasetIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()));

            }
            //取字段映射配置
            if (StringUtils.isNotBlank(evalTaskPo.getExtra())) {
                TaskExtraParam taskExtraParam = JSON.parseObject(evalTaskPo.getExtra(), new TypeReference<TaskExtraParam>() {
                });
                taskDTO.setBindFields(taskExtraParam.getBindFields());
                taskDTO.setMockTimes(taskExtraParam.getMockTimes());
                taskDTO.setScoreThreshold(taskExtraParam.getScoreThreshold());
                if (StringUtils.isNotBlank(taskExtraParam.getEvalRequest())) {
                    EvalTaskRequest evalRequest = JSON.parseObject(taskExtraParam.getEvalRequest(), new TypeReference<EvalTaskRequest>() {
                    });
                    taskDTO.setModelResultParamList(buildModelResultParamList(evalRequest));
                    taskDTO.setResultParamList(buildResultParamList(evalRequest));
                    taskDTO.setWhetherRegressDataset(evalRequest.getWhetherRegressDataset());
                    taskDTO.setHistorySource(evalRequest.getHistorySource());
                    taskDTO.setMetricList(buildMetricList(evalRequest));
                    taskDTO.setScoreThresholdList(evalRequest.getScoreThreshold());
                }
            }
            Long metricId = Arrays.stream(evalTaskPo.getMetrics().split(",")).map(Long::parseLong).findFirst().orElse(null);
            if (MapUtils.isNotEmpty(metricConfigMap) && Objects.nonNull(metricId) && Objects.nonNull(metricConfigMap.get(metricId))) {
                MetricConfigPo metricConfigPo = metricConfigMap.get(metricId);
                taskDTO.setMetricType(Objects.nonNull(metricConfigPo) ? metricConfigPo.getEvalType() : null);
            }

            //设置模型名称和模型版本 下面是把多个的情况拼一块，这里是返给前端一个list
            taskDTO.setAidaModelConfig(buildAidaModelConfig(evalTaskPo, applicationConfigMap));
            taskDTO.setDataSetName(StringUtils.join(datasetNames.toArray(), ","));
            //  设置模型名称和模型版本
            setModelNameAndModelVersion(evalTaskPo, taskDTO, aidaVersionMap, applicationConfigMap);
            taskDTO.setStatus(evalTaskPo.getStatus());
            taskDTO.setProgress(buildProgress(evalTaskPo, taskQueryMap.get(evalTaskPo.getId()), sessionMap.get(evalTaskPo.getId())));
            taskDTO.setCreatorMis(evalTaskPo.getCreatorMis());
            taskDTO.setScene(evalTaskPo.getScene());
            taskDTO.setModelSource(getModelSource(evalTaskPo, applicationConfigMap));
            taskDTO.setCallType(evalTaskPo.getCallType());
            String autoResult = evalTaskPo.getAutoResult();
            if (StringUtils.isNotBlank(evalTaskPo.getApplicationConfig())) {
                List<Long> applicationIds = Arrays.asList(evalTaskPo.getApplicationConfig().replace("[", "").replace("]", "").replace("\"", "").split(",")).stream().map(Long::parseLong).collect(Collectors.toList());
                taskDTO.setApplicationConfig(applicationIds);

            }
            if (StringUtils.isNotBlank(autoResult)) {
                JSONObject jsonObject = JSONObject.parseObject(autoResult);
                if (jsonObject.containsKey("avgScore")) {
                    taskDTO.setScore(jsonObject.getDouble("avgScore").intValue());
                }
            }
            if (param.getTaskType() != null && param.getTaskType() == TaskTypeEnum.INSPECT.getCode() && MapUtils.isNotEmpty(autoInspectDetailMap)) {
                if (autoInspectDetailMap.get(taskDTO.getId()) != null) {
                    taskDTO.setScore(Math.toIntExact(autoInspectDetailMap.get(taskDTO.getId()).getScore()));
                }
            }
            if (StringUtils.isNotBlank(evalTaskPo.getInspectors())) {
                taskDTO.setInspectors(Arrays.asList(evalTaskPo.getInspectors().split(",")));
            }
            taskDTO.setCreateTime(DateUtil.format(evalTaskPo.getGmtCreated(), null));
            taskDTO.setUpdateTime(DateUtil.format(evalTaskPo.getGmtModified(), null));
            return taskDTO;
        }).collect(Collectors.toList());
        return PageData.create(totalNum, pageNum, pageSize, taskList);
    }

    /**
     * 获取应用配置map
     * @param dataList
     * @return
     */
    private Map<Long, ApplicationConfigPo> getApplicationMap(List<EvalTaskPo> dataList) {
        List<Long> applicationIdList = Optional.ofNullable(dataList).orElse(Collections.emptyList()).stream().map(taskPo -> {
            List<String> applicationInfoList = JSON.parseArray(taskPo.getApplicationConfig(), String.class);
            return Optional.ofNullable(applicationInfoList).orElse(Collections.emptyList()).stream().map(DataConvertUtil::tryConvertLongWithNull).filter(Objects::nonNull).collect(Collectors.toList());
        }).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        List<ApplicationConfigPo> applicationConfigPoList = applicationConfigGeneratorService.getByIdList(applicationIdList);
        Map<Long, ApplicationConfigPo> applicationConfigMap = Optional.ofNullable(applicationConfigPoList).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(ApplicationConfigPo::getId, Function.identity(), (k1, k2) -> k1));
        return applicationConfigMap;
    }


    /**
     * 解析任务的modelSource
     *
     * @param evalTaskPo
     * @return
     */
    private Integer getModelSource(EvalTaskPo evalTaskPo, Map<Long, ApplicationConfigPo> applicationConfigMap) {
        List<String> applicationInfoList = JSON.parseArray(evalTaskPo.getApplicationConfig(), String.class);
        Long applicationId = Optional.ofNullable(applicationInfoList).orElse(Collections.emptyList()).stream().map(DataConvertUtil::tryConvertLongWithNull).filter(Objects::nonNull).findFirst().orElse(null);
        if (MapUtils.isNotEmpty(applicationConfigMap) && Objects.nonNull(applicationId) && Objects.nonNull(applicationConfigMap.get(applicationId))) {
            return applicationConfigMap.get(applicationId).getSource();
        }
        return null;
    }

    private static List<TaskMetricInfoDTO> buildMetricList(EvalTaskRequest evalRequest) {
        return Optional.ofNullable(evalRequest.getMetricList()).orElse(Collections.emptyList()).stream().filter(Objects::nonNull).map(metric -> {
            TaskMetricInfoDTO metricInfoDTO = new TaskMetricInfoDTO();
            metricInfoDTO.setMetricId(Objects.nonNull(metric.getMetricId()) ? Integer.parseInt(metric.getMetricId() + "") : null);
            metricInfoDTO.setOutputKey(metric.getOutputKey());
            return metricInfoDTO;
        }).collect(Collectors.toList());
    }

    private static List<ApplicationResultParam> buildResultParamList(EvalTaskRequest evalRequest) {
        List<ApplicationResultParam> resultParamList = Lists.newArrayList();
        if (MapUtils.isEmpty(evalRequest.getApplicationParamMap())) {
            return null;
        }
        evalRequest.getApplicationParamMap().forEach((k, v) -> {
            ApplicationResultParam applicationResultParam = new ApplicationResultParam();
            applicationResultParam.setApplicationId(k);
            applicationResultParam.setModelResultParam(v);
            resultParamList.add(applicationResultParam);
        });
        return resultParamList;
    }

    private static List<ApplicationResultParam> buildModelResultParamList(EvalTaskRequest evalRequest) {
        List<ApplicationResultParam> modelResultParamList = Lists.newArrayList();
        if (MapUtils.isEmpty(evalRequest.getApplicationOutputMap())) {
            return null;
        }
        evalRequest.getApplicationOutputMap().forEach((k, v) -> {
            ApplicationResultParam applicationResultParam = new ApplicationResultParam();
            applicationResultParam.setApplicationId(k);
            applicationResultParam.setModelResultParam(v);
            modelResultParamList.add(applicationResultParam);
        });
        return modelResultParamList;
    }

    private List<AidaModelConfig> buildAidaModelConfig(EvalTaskPo evalTaskPo, Map<Long, ApplicationConfigPo> applicationConfigMap) {
        List<AidaModelConfig> aidaModelConfigs = Lists.newArrayList();
        if (StringUtils.isBlank(evalTaskPo.getApplicationConfig())) {
            return null;
        }
        List<String> applicationInfoList = JSON.parseArray(evalTaskPo.getApplicationConfig(), String.class);
        applicationInfoList.forEach(applicationId -> {
            if (MapUtils.isNotEmpty(applicationConfigMap) && Objects.nonNull(applicationConfigMap.get(Long.parseLong(applicationId)))) {
                AidaModelConfig aidaModelConfig = new AidaModelConfig();
                ApplicationConfigPo applicationConfigPo = applicationConfigMap.get(Long.parseLong(applicationId));
                aidaModelConfig.setApplicationId(applicationConfigPo.getPlatformApp());
                aidaModelConfig.setModelConfigVersionId(applicationConfigPo.getRobotId());
                aidaModelConfig.setApplicationName(applicationConfigPo.getName());
                aidaModelConfig.setWorkspaceId(applicationConfigPo.getPlatformWorkspace());
                aidaModelConfigs.add(aidaModelConfig);
            }
        });
        return aidaModelConfigs;
    }

    private String buildVersion(String modelNameStr) {
        if (StringUtils.isBlank(modelNameStr)) {
            return "-";
        }
        return Arrays.stream(modelNameStr.split(","))
                .map(modelName -> modelName.startsWith("ai搭机器人:") ? modelName.replace("ai搭机器人:", "") : "-")
                .collect(Collectors.joining(","));
    }

    private Map<Long, TaskDetailCountDTO> getSessionMap(List<Long> taskIdList) {
        List<TaskDetailCountDTO> sessionCountList = evalTaskSessionGeneratorService.getSessionCount(taskIdList,TaskSessionStatusEnum.COMPLETE_STATUS);
        return Optional.ofNullable(sessionCountList).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(TaskDetailCountDTO::getTaskId,Function.identity(),(k1,k2)->k1));
    }

    private Map<Long, List<String>> buildApplicationInfo(List<EvalTaskPo> evalTaskPoList) {
        if (CollectionUtils.isEmpty(evalTaskPoList)) {
            return new HashMap<>();
        }
        // 任务id和应用id的映射
        Map<Long, List<String>> applicationTaskMap = evalTaskPoList.stream()
                .filter(evalTaskPo -> StringUtils.isNotBlank(evalTaskPo.getApplicationConfig()) && !"null".equals(evalTaskPo.getApplicationConfig()))
                .map(evalTaskPo -> {
                    List<String> applicationConfigList = DataConvertUtil.tryConvertArray(evalTaskPo.getApplicationConfig());
                    return new AbstractMap.SimpleEntry<>(evalTaskPo.getId(), applicationConfigList);
                })
                .filter(entry -> entry.getValue() != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue
                ));

        List<Long> applicationIdList = applicationTaskMap.values().stream().flatMap(Collection::stream).map(DataConvertUtil::tryConvertLongWithNull).filter(Objects::nonNull).collect(Collectors.toList());

        // 应用id和应用名称的映射
        Map<String, String> applicationIdNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(applicationIdList)) {
            List<ApplicationConfigPo> applicationConfigList = applicationConfigGeneratorService.getByIdList(applicationIdList);
            // 应用id和应用名称的映射
            applicationIdNameMap = applicationConfigList.stream().collect(Collectors.toMap(applicationConfigPo -> String.valueOf(applicationConfigPo.getId()), ApplicationConfigPo::getName));
        }
        Map<String, String> finalApplicationIdNameMap = applicationIdNameMap;
        return applicationTaskMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .map(application -> finalApplicationIdNameMap.getOrDefault(application, application))
                                .collect(Collectors.toList())
                ));
    }

    private String buildApplicationInfo(EvalTaskPo evalTaskPo) {
        if (StringUtils.isBlank(evalTaskPo.getApplicationConfig())) {
            return evalTaskPo.getTestModel();
        }
        try {
            List<String> applicationInfoList = JSON.parseArray(evalTaskPo.getApplicationConfig(), String.class);
            List<Long> applicationIdList = new ArrayList<>();
            List<String> applicationNameList = new ArrayList<>();
            for (String applicationInfo : applicationInfoList) {
                Long result = DataConvertUtil.tryConvertLongWithNull(applicationInfo);
                if (result != null) {
                    if (!applicationIdList.contains(result.longValue())) {
                        applicationIdList.add(result.longValue());
                    }
                } else {
                    applicationNameList.add(applicationInfo);
                }
            }
            if (CollectionUtils.isNotEmpty(applicationIdList)) {
                List<ApplicationConfigPo> applicationConfigPoList = applicationConfigGeneratorService.getByIdList(applicationIdList);

                if (CollectionUtils.isNotEmpty(applicationConfigPoList)) {
                    if (applicationConfigPoList.size() != applicationIdList.size()) {
                        Set<Long> existApplicationIdList = applicationConfigPoList.stream().map(ApplicationConfigPo::getId).collect(Collectors.toSet());
                        // 查出来的结果不相等，说明有应用被删除，将id作为应用信息
                        for (Long applicationId : applicationIdList) {
                            if (!existApplicationIdList.contains(applicationId)) {
                                applicationNameList.add(String.valueOf(applicationId));
                            }
                        }
                    }
                    applicationNameList.addAll(applicationConfigPoList.stream().map(ApplicationConfigPo::getName).collect(Collectors.toList()));
                } else {
                    // 所有的应用都查不到，说明都已经被删除
                    for (Long applicationId : applicationIdList) {
                        applicationNameList.add(String.valueOf(applicationId));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(applicationNameList)) {
                return String.join(",", applicationNameList);
            }
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
            log.error("解析应用出错,task={},msg={}", JSON.toJSONString(evalTaskPo), e.getMessage(), e);
        }
        return evalTaskPo.getTestModel();
    }

    private PageData<TaskDetailDTO> convertTaskDetailPageInfo(int totalNum, int pageNum, int pageSize, List<EvalTaskQueryPo> pageDataList, List<EvalTaskQueryDetailPo> queryDetailList, EvalTaskPo evalTask) {
        TaskLogConditionParam conditionTaskLog = new TaskLogConditionParam();
        conditionTaskLog.setTaskId(evalTask.getId());
        List<EvalTaskLogPo> taskLogList = evalTaskLogGeneratorService.getByCondition(conditionTaskLog);
        Map<Long, List<EvalTaskLogPo>> taskLogPoMap = Optional.ofNullable(taskLogList).orElse(Collections.emptyList()).stream().collect(Collectors.groupingBy(EvalTaskLogPo::getQueryId));

        List<TaskDetailDTO> taskDetailList = convertTaskDetailList(pageDataList, queryDetailList, evalTask, taskLogPoMap);
        // 失败的query无需质检
        taskDetailList.forEach(taskDetailDTO -> {
            boolean canInspect = taskDetailDTO.getStatus() == TaskQueryStatusEnum.INSPECTING.getCode();
            for (TaskDetailDTO.EvalModelDTO evalModelDTO : taskDetailDTO.getResult()) {
                for (TaskDetailDTO.EvalOutputDTO evalOutputDTO : evalModelDTO.getData()) {
                    for (TaskDetailDTO.EvalInfo evalInfo : evalOutputDTO.getEval()) {
                        evalInfo.setCanInspect(canInspect && evalInfo.getCanInspect());
                    }
                }
            }
        });
        return PageData.create(totalNum, pageNum, pageSize, taskDetailList);
    }

    private List<TaskDetailDTO> convertTaskDetailList(List<EvalTaskQueryPo> taskQueryList, List<EvalTaskQueryDetailPo> queryDetailList, EvalTaskPo evalTaskPo, Map<Long, List<EvalTaskLogPo>> taskLogPoMap) {
        Map<String, String> aidaVersionMap = getAidaVersionMap();
        Map<Long, MetricConfigPo> metricConfigMap = getMetricConfigMap(queryDetailList);
        // 第一级 按照query分组 {"queryId":[]}
        Map<Long, List<EvalTaskQueryDetailPo>> queryDetailMap = queryDetailList.stream().collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getQueryId));
        Map<Long, ApplicationConfigPo> applicationConfigPoMap = getApplicationConfigPoMap(queryDetailList);
        Map<Long, EvalDatasetPo> datasetMap = getDatasetPoMap(taskQueryList);
        return taskQueryList.stream().map(evalTaskQueryPo -> convertTaskDetail(evalTaskQueryPo, datasetMap, queryDetailMap, metricConfigMap, applicationConfigPoMap, evalTaskPo, aidaVersionMap, taskLogPoMap)).collect(Collectors.toList());
    }

    private Map<Long, EvalDatasetPo> getDatasetPoMap(List<EvalTaskQueryPo> taskQueryList) {
        List<Long> datasetIdList = taskQueryList.stream().map(EvalTaskQueryPo::getDatasetId).collect(Collectors.toList());
        List<EvalDatasetPo> datasetList = evalDatasetGeneratorService.getByIdList(datasetIdList);
        return datasetList.stream().collect(Collectors.toMap(EvalDatasetPo::getId, Function.identity()));
    }

    private Map<Long, MetricConfigPo> getMetricConfigMap(List<EvalTaskQueryDetailPo> queryDetailList) {
        List<Long> metricIdList = queryDetailList.stream().map(EvalTaskQueryDetailPo::getMetricId).map(Integer::longValue).collect(Collectors.toList());
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricIdList);
        return metricConfigList.stream().collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));
    }

    private Map<Long, ApplicationConfigPo> getApplicationConfigPoMap(List<EvalTaskQueryDetailPo> queryDetailList) {
        // 解析应用名称
        Map<Long, ApplicationConfigPo> applicationConfigPoMap = new HashMap<>();
        List<Long> applicationIdList = queryDetailList.stream().map(queryDetailPo -> DataConvertUtil.tryConvertLongWithNull(queryDetailPo.getTestApplication())).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(applicationIdList)) {
            List<ApplicationConfigPo> applicationConfigPoList = applicationConfigGeneratorService.getByIdList(applicationIdList);
            if (CollectionUtils.isNotEmpty(applicationConfigPoList)) {
                applicationConfigPoMap = applicationConfigPoList.stream().collect(Collectors.toMap(ApplicationConfigPo::getId, Function.identity()));
            }
        }
        return applicationConfigPoMap;
    }

    private TaskDetailDTO convertTaskDetail(EvalTaskQueryPo evalTaskQueryPo, Map<Long, EvalDatasetPo> datasetMap,
                                            Map<Long, List<EvalTaskQueryDetailPo>> queryDetailMap,
                                            Map<Long, MetricConfigPo> metricConfigMap,
                                            Map<Long, ApplicationConfigPo> applicationConfigPoMap,
                                            EvalTaskPo evalTaskPo, Map<String, String> aidaVersionMap, Map<Long, List<EvalTaskLogPo>> taskLogPoMap) {
        // 填充query维度信息
        TaskDetailDTO taskDetailResult = new TaskDetailDTO();
        EvalDatasetPo evalDataset = datasetMap.get(evalTaskQueryPo.getDatasetId());
        taskDetailResult.setId(evalTaskQueryPo.getId());
        taskDetailResult.setSessionId(evalTaskQueryPo.getSessionId());
        taskDetailResult.setConversationId(evalTaskQueryPo.getConversationId());
        taskDetailResult.setInput(evalTaskQueryPo.getInput());
        Map<String, String> content = new HashMap<>();

        taskDetailResult.setContent(convertContentHead(evalTaskQueryPo, evalDataset, content));
        if (StringUtils.isNotBlank(evalTaskQueryPo.getParams())) {
            taskDetailResult.setParams(JSON.parseObject(evalTaskQueryPo.getParams(), new TypeReference<Map<String, String>>() {
            }));
        }
        taskDetailResult.setStatus(evalTaskQueryPo.getStatus());
        Long datasetId = evalTaskQueryPo.getDatasetId();

        taskDetailResult.setDatasetId(datasetId);
        taskDetailResult.setDatasetName(evalDataset == null || StringUtils.isBlank(evalDataset.getName()) ? "" : evalDataset.getName());
        taskDetailResult.setCreateTime(DateUtil.format(evalTaskQueryPo.getGmtCreated(), null));
        taskDetailResult.setUpdateTime(DateUtil.format(evalTaskQueryPo.getGmtModified(), null));
        List<EvalTaskQueryDetailPo> queryDetailList = queryDetailMap.get(evalTaskQueryPo.getId());
        // 获取模型分组结果
        List<TaskDetailDTO.EvalModelDTO> evalModelResultList = convertModelResult(evalTaskQueryPo, queryDetailList, metricConfigMap, applicationConfigPoMap, aidaVersionMap, evalDataset, true, taskLogPoMap, evalTaskPo);
        taskDetailResult.setResult(evalModelResultList);
        taskDetailResult.setType(evalTaskPo.getType());
        return taskDetailResult;
    }

    /**
     * 转换query中content头   从key转成成name
     *
     * @param evalTaskQueryPo
     * @param evalDataset
     * @param content
     * @return
     */
    private static String convertContentHead(EvalTaskQueryPo evalTaskQueryPo, EvalDatasetPo evalDataset, Map<String, String> content) {
        if (Objects.isNull(evalDataset) && StringUtils.isBlank(evalDataset.getExtra())) {
            return null;
        }
        DatasetExtraParam datasetExtraParam = JSON.parseObject(evalDataset.getExtra(), DatasetExtraParam.class);
        if (Objects.isNull(datasetExtraParam)) {
            return null;
        }
        Map<String, String> keyNameMap = Optional.ofNullable(datasetExtraParam.getHeadMapList()).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(SingleTemplateFieldBindDTO::getColumnKey, SingleTemplateFieldBindDTO::getColumnName, (k1, k2) -> k1));
        if (StringUtils.isBlank(evalTaskQueryPo.getContent())) {
            return null;
        }
        Map<String, String> contentMap = JSON.parseObject(evalTaskQueryPo.getContent(), new TypeReference<HashMap<String, String>>() {
        });
        if (MapUtils.isEmpty(keyNameMap) || MapUtils.isEmpty(contentMap)) {
            return JSON.toJSONString(contentMap);
        }
        contentMap.forEach((k, v) -> {
            if (StringUtils.isNotBlank(keyNameMap.get(k))) {
                content.put(keyNameMap.get(k), v);
            } else {
                content.put(k, v);
            }
        });
        return JSON.toJSONString(content);
    }

    private List<TaskDetailDTO.EvalModelDTO> convertModelResult(EvalTaskQueryPo expectOut, List<EvalTaskQueryDetailPo> queryDetailList, Map<Long, MetricConfigPo> metricConfigMap, Map<Long, ApplicationConfigPo> applicationConfigPoMap, Map<String, String> aidaVersionMap, EvalDatasetPo evalDataset, boolean flag, Map<Long, List<EvalTaskLogPo>> taskLogPoMap, EvalTaskPo evalTaskPo) {
        List<TaskDetailDTO.EvalModelDTO> evalModelResultList = new ArrayList<>();
        // 获取模型版本映射
        if (CollectionUtils.isNotEmpty(queryDetailList)) {
            // 第二级 按照应用分组 {"testApplication":[]}
            Map<String, List<EvalTaskQueryDetailPo>> queryApplicationDetailMap = getApplicationDetailMap(queryDetailList, applicationConfigPoMap);
            // 获取应用输出分组结果
            Map<String, EvalTaskQueryDetailPo> evalTaskQueryDetailPoMap = Optional.ofNullable(queryDetailList).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(detail -> detail.getTestApplication() + detail.getMetricId() + (StringUtils.isNotBlank(detail.getOutputKey()) ? detail.getOutputKey() : "完整输出"), Function.identity(), (k1, k2) -> k1));
            queryApplicationDetailMap.entrySet().forEach(queryApplicationDetailEntry -> {
                TaskDetailDTO.EvalModelDTO evalModelResult = convertOutputResult(expectOut, queryApplicationDetailEntry, metricConfigMap, evalTaskQueryDetailPoMap, evalDataset, flag, taskLogPoMap, evalTaskPo);

                // 填充应用名称和版本信息
                Long applicationId = DataConvertUtil.tryConvertLongWithNull(queryApplicationDetailEntry.getValue().get(0).getTestApplication());
                ApplicationConfigPo configPo = applicationConfigPoMap.get(applicationId);
                if (applicationId != null) {
                    evalModelResult.setModelName(configPo.getName());
                    evalModelResult.setVersion(aidaVersionMap.getOrDefault(configPo.getRobotId(), "-"));
                }

                evalModelResultList.add(evalModelResult);
            });
        }
        return evalModelResultList;
    }

    private Map<String, List<EvalTaskQueryDetailPo>> getApplicationDetailMap(List<EvalTaskQueryDetailPo> queryDetailList, Map<Long, ApplicationConfigPo> applicationConfigPoMap) {
        if (CollectionUtils.isEmpty(queryDetailList)) {
            return new HashMap<>();
        }
        // 兼容历史数据，如果没有应用信息，按照模型分组
        if (StringUtils.isBlank(queryDetailList.get(0).getTestApplication())) {
            return queryDetailList.stream().peek(evalTaskQueryDetailPo -> {
                if (StringUtils.isBlank(evalTaskQueryDetailPo.getTestModel())) {
                    // 兼容ai搭能力，后续需要将查找ai搭接口获取模型能力
                    evalTaskQueryDetailPo.setTestModel("未知应用");
                }
            }).collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getTestModel));
        }

        Map<String, List<EvalTaskQueryDetailPo>> queryApplicationDetailMap = new HashMap<>();
        for (EvalTaskQueryDetailPo queryDetail : queryDetailList) {
            Long applicationId = DataConvertUtil.tryConvertLongWithNull(queryDetail.getTestApplication());
            String applicationName;
            if (applicationId == null || !applicationConfigPoMap.containsKey(applicationId)) {
                applicationName = queryDetail.getTestApplication() + NAME_SPLIT + "-";
            } else {
                String modelName = commonEvalStrategyService.getModelName(applicationConfigPoMap.get(applicationId).getModelConfig());
                applicationName = applicationConfigPoMap.get(applicationId).getName() + NAME_SPLIT + (StringUtils.isNotBlank(modelName) ? modelName : "-");
            }
            List<EvalTaskQueryDetailPo> detailPoList = queryApplicationDetailMap.getOrDefault(applicationName, new ArrayList<>());
            detailPoList.add(queryDetail);
            queryApplicationDetailMap.put(applicationName, detailPoList);
        }
        return queryApplicationDetailMap;
    }


    private TaskDetailDTO.EvalModelDTO convertOutputResult(EvalTaskQueryPo taskQueryPo, Map.Entry<String, List<EvalTaskQueryDetailPo>> queryApplicationDetailEntry, Map<Long, MetricConfigPo> metricConfigMap, Map<String, EvalTaskQueryDetailPo> evalTaskQueryDetailPoMap, EvalDatasetPo evalDataset, boolean flag, Map<Long, List<EvalTaskLogPo>> taskLogPoMap, EvalTaskPo evalTaskPo) {
        TaskDetailDTO.EvalModelDTO evalModelResult = new TaskDetailDTO.EvalModelDTO();
        evalModelResult.setModelName(getApplicationName(queryApplicationDetailEntry.getKey()));
        evalModelResult.setVersion(buildVersion(evalModelResult.getModelName()));
        // 第三级 按照指标分组 {"metricId":[]} 目前每个指标、每个模型只有一个输出
        Map<Integer, List<EvalTaskQueryDetailPo>> metricDetailMap = queryApplicationDetailEntry.getValue().stream().collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getMetricId));
        List<TaskDetailDTO.EvalOutputDTO> resultDataList = new ArrayList<>();
        List<TaskDetailDTO.EvalInfo> evalInfoList = new ArrayList<>();
        List<Long> queryDetailIdList = new ArrayList<>();
        String modelOutput = queryApplicationDetailEntry.getValue().get(0).getModelOutput();
        // 指标id:outputKey map
        Map<Long, List<String>> metricOutputKeyMap = queryApplicationDetailEntry.getValue().stream().collect(Collectors.groupingBy(evalTask -> evalTask.getMetricId().longValue(), Collectors.mapping(EvalTaskQueryDetailPo::getOutputKey, Collectors.toList())));
        for (Map.Entry<Integer, List<EvalTaskQueryDetailPo>> metricDetailEntry : metricDetailMap.entrySet()) {
            List<EvalTaskQueryDetailPo> queryMetricDetailList = metricDetailEntry.getValue();
            Map<String, EvalTaskQueryDetailPo> queryDetailPoMap = queryMetricDetailList.stream().collect(Collectors.toMap(EvalTaskQueryDetailPo::getOutputKey,
                    Function.identity(),
                    (existing, replacement) -> existing));
            queryDetailIdList.add(queryMetricDetailList.get(0).getId());
            List<String> outputKeys = metricOutputKeyMap.get(metricDetailEntry.getKey().longValue());
            if (CollectionUtils.isNotEmpty(outputKeys)) {
                for (String outputKey : outputKeys) {
                    EvalTaskQueryDetailPo queryMetricDetail = queryDetailPoMap.get(outputKey);
                    if (Objects.isNull(queryMetricDetail)) {
                        continue;
                    }
                    //转换页面展示的变量
                    //outputName 是错题集下的key-name的映射  evalDataset.getExtra()里面的 headMapList  如果没有，直接取outputKey
                    String outputName = outputKey;
                    if (Objects.nonNull(evalDataset) && StringUtils.isNotBlank(evalDataset.getExtra())) {
                        DatasetExtraParam datasetExtraParam = JSON.parseObject(evalDataset.getExtra(), new TypeReference<DatasetExtraParam>() {
                        });
                        Map<String, String> keyNameMap = Optional.ofNullable(datasetExtraParam.getHeadMapList()).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(SingleTemplateFieldBindDTO::getColumnKey, SingleTemplateFieldBindDTO::getColumnName, (a, b) -> a));
                        if (MapUtils.isNotEmpty(keyNameMap) && StringUtils.isNotBlank(keyNameMap.get(outputKey))) {
                            outputName = keyNameMap.get(outputKey);
                        }
                    }
                    if (Objects.nonNull(taskQueryPo.getMetricResult()) && taskQueryPo.getMetricResult().contains("childManualAnnotation")) {
                        Optional.ofNullable(getEvalInfo(taskQueryPo, metricDetailEntry, outputKey, outputName, queryMetricDetail, evalTaskQueryDetailPoMap)).ifPresent(evalInfoList::addAll);
                    } else {
                        MetricConfigPo metricConfigPo = metricConfigMap.get(metricDetailEntry.getKey().longValue());
                        if (Objects.nonNull(metricConfigPo)) {
                            String metricName = metricConfigPo.getName();
                            TaskDetailDTO.EvalInfo evalInfo = buildEval(evalTaskQueryDetailPoMap, outputKey, outputName, metricDetailEntry.getKey(), metricName, taskQueryPo, queryMetricDetail);
                            evalInfo.setResult(queryMetricDetail.getMetricResult());
                            evalInfo.setResultNote(queryMetricDetail.getNote());
                            evalInfoList.add(evalInfo);
                        }

                    }

                }
            }
        }
        TaskDetailDTO.EvalOutputDTO resultData = new TaskDetailDTO.EvalOutputDTO();
        if (flag) {
            resultData.setOutput(getOutput(taskQueryPo, taskLogPoMap, evalTaskPo));
        } else {
            resultData.setOutput(modelOutput);
        }
        resultData.setQueryDetailIdList(queryDetailIdList);
        resultData.setEval(evalInfoList.stream().distinct().collect(Collectors.toList()));
        resultDataList.add(resultData);
        evalModelResult.setData(resultDataList);
        return evalModelResult;
    }

    private static String getOutput(EvalTaskQueryPo taskQueryPo, Map<Long, List<EvalTaskLogPo>> taskLogPoMap, EvalTaskPo evalTaskPo) {
        if (CallTypeEnum.OFFLINE.getCode() == evalTaskPo.getCallType()) {
            if (MapUtils.isNotEmpty(taskLogPoMap)) {
                List<EvalTaskLogPo> evalTaskLogPos = taskLogPoMap.get(taskQueryPo.getId());
                String content = Optional.ofNullable(evalTaskLogPos).orElse(Collections.emptyList()).stream().filter(taskLog -> StringUtils.isNotBlank(taskLog.getContent())).map(EvalTaskLogPo::getContent).findFirst().orElse(null);
                if (StringUtils.isNotBlank(content)) {
                    GptContextParam param = JSON.parseObject(content, GptContextParam.class);
                    return param.getOutput();
                }
            }
        } else {
            //return taskQueryPo.getModelOutput();
        }
        return null;
    }


    public List<TaskDetailDTO.EvalInfo> getEvalInfo(EvalTaskQueryPo taskQueryPo, Map.Entry<Integer, List<EvalTaskQueryDetailPo>> metricDetailEntry, String outputKey, String outputName, EvalTaskQueryDetailPo queryMetricDetail, Map<String, EvalTaskQueryDetailPo> queryDetailPoMap) {

        List<ScoreParam.Level> levels = Lists.newArrayList();
        Map<String, ScoreParam> scoreParamMap = JSONObject.parseObject(taskQueryPo.getMetricResult(), new TypeReference<Map<String, ScoreParam>>() {
        });
        if (MapUtils.isEmpty(scoreParamMap)) {
            return null;
        }
        Map<String, TaskDetailDTO.EvalInfo> evalInfoMap = Maps.newHashMap();
        for (Map.Entry<String, ScoreParam> scoreParamEntry : scoreParamMap.entrySet()) {
            if (Objects.isNull(scoreParamEntry.getValue())) {
                continue;
            }
            Map<Long, List<ScoreParam.ScoreItem>> scoreItemMap = Maps.newHashMap();
            for (ChildMetricParam childMetricParam : scoreParamEntry.getValue().getMetrics()) {
                if (!Objects.equals(childMetricParam.getMetricId().intValue(), metricDetailEntry.getKey())) {
                    continue;
                }
                List<ScoreParam.ScoreItem> scoreItems = Lists.newArrayList();
                markExecuteService.getManualAnnotations(childMetricParam, scoreItems, "", levels);
                scoreItemMap.put(childMetricParam.getMetricId(), scoreItems);
            }
            if (MapUtils.isEmpty(scoreItemMap)) {
                return null;
            }

            for (ScoreParam.ScoreItem scoreItem : scoreItemMap.get(metricDetailEntry.getKey().longValue())) {
                String key = markExecuteService.getRootPath(scoreItem) + scoreItem.getTargetName();
                TaskDetailDTO.EvalInfo evalInfo = buildEval(queryDetailPoMap, outputKey, outputName, scoreItem.getTargetCode(), scoreItem.getTargetName(), taskQueryPo, queryMetricDetail);
                evalInfo.setHead(key);
                evalInfo.setMetricName(key);
                if (evalInfoMap.containsKey(key)) {
                    evalInfo.setResult(evalInfoMap.get(key).getResult() + "," + scoreItem.getTargetValue());
                } else {
                    evalInfo.setResult(scoreItem.getTargetValue());
                }
                evalInfoMap.put(key, evalInfo);
            }
        }

        return new ArrayList<>(evalInfoMap.values());
    }


    public TaskDetailDTO.EvalInfo buildEval(Map<String, EvalTaskQueryDetailPo> queryDetailPoMap, String outputKey, String outputName, Integer metricId, String metricName, EvalTaskQueryPo taskQueryPo, EvalTaskQueryDetailPo queryMetricDetail) {
        TaskDetailDTO.EvalInfo evalInfo = new TaskDetailDTO.EvalInfo();

        if (StringUtils.isBlank(outputKey)) {
            outputKey = "完整输出";
        }
        EvalTaskQueryDetailPo evalTaskQueryDetailPo = queryDetailPoMap.get(queryMetricDetail.getTestApplication() + metricId + outputKey);
        if (Objects.isNull(evalTaskQueryDetailPo)) {
            return null;
        }
        evalInfo.setOutputKey(outputName);
        evalInfo.setQueryDetailId(evalTaskQueryDetailPo.getId());
        evalInfo.setMetricId(metricId);
        evalInfo.setMetricName(metricName);
        evalInfo.setInspector(evalTaskQueryDetailPo.getInspectMis());
        evalInfo.setCorrectAnswer(evalTaskQueryDetailPo.getAnswer());
        evalInfo.setReason(evalTaskQueryDetailPo.getReason());
        if (evalTaskQueryDetailPo.getStatus() == AutoTaskEvalStatusEnum.INSPECT_PASS.getCode()) {
            evalInfo.setInspectResult(InspectResultEnum.PAAS.getCode());
        } else if (evalTaskQueryDetailPo.getStatus() == AutoTaskEvalStatusEnum.INSPECT_FAILED.getCode()) {
            evalInfo.setInspectResult(InspectResultEnum.FAIL.getCode());
        }

        evalInfo.setCanInspect(isShowInspect(evalTaskQueryDetailPo, evalTaskQueryDetailPo));
        evalInfo.setMarkMis(evalTaskQueryDetailPo.getMarkMis());
        evalInfo.setMarkTime(DateUtil.format(evalTaskQueryDetailPo.getMarkTime(), ""));

        evalInfo.setOutput(evalTaskQueryDetailPo.getModelOutput());
        String expectOutput = StringUtils.isNotBlank(evalTaskQueryDetailPo.getExpect()) ? evalTaskQueryDetailPo.getExpect() : taskQueryPo.getExpect();
        evalInfo.setExpectOutput(expectOutput);
        return evalInfo;
    }

    private static boolean isShowInspect(EvalTaskQueryDetailPo detailPo, EvalTaskQueryDetailPo evalTaskQueryDetailPo) {
        String login = UserUtils.getUser().getLogin();
        //mis不为空，说明是待质检、质检通过、质检驳回  质检人是当前系统登录人
        return StringUtils.isNotBlank(detailPo.getInspectMis()) && login.equals(detailPo.getInspectMis());
    }

    private Map<Long, List<ScoreParam.ScoreItem>> buildTreeMetric(EvalTaskQueryDetailPo queryMetricDetail, Long metricId) {

        //递归获取所有指标 遍历，拼接到dataset_detail content
        List<ChildMetricParam> childMetricParams = JSONObject.parseObject(queryMetricDetail.getMetricResult(), new TypeReference<List<ChildMetricParam>>() {
        });
        Map<Long, List<ScoreParam.ScoreItem>> scoreItemMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(childMetricParams)) {
            List<ScoreParam.ScoreItem> scoreItems = new ArrayList<>();
            for (ChildMetricParam metricParam : childMetricParams) {
                if (!Objects.equals(metricParam.getMetricId(), metricId)) {
                    continue;
                }
                getManualAnnotations(metricParam, scoreItems);
                scoreItemMap.put(metricParam.getMetricId(), scoreItems);
            }
        }
        return scoreItemMap;
    }


    private void getManualAnnotations(ChildMetricParam metricParam, List<ScoreParam.ScoreItem> scoreItems) {
        if (Objects.isNull(metricParam)
                || Objects.isNull(metricParam.getMetricId())
                || Objects.isNull(metricParam.getChildManualAnnotation())) {
            return;
        }
        ScoreParam.ScoreItem scoreItem = new ScoreParam.ScoreItem();
        ManualAnnotationParam childManualAnnotation = metricParam.getChildManualAnnotation();
        scoreItem.setTargetCode(metricParam.getMetricId().intValue());
        scoreItem.setTargetName(childManualAnnotation.getAnnotationName());
        if (MetricAnnotationTypeEnum.ENUMERATION.getCode().equals(childManualAnnotation.getAnnotationType())) {
            if (CollectionUtils.isNotEmpty(childManualAnnotation.getEnumValueList())) {
                scoreItem.setTargetValue(childManualAnnotation.getEnumValueList().get(0).getEnumValue());
            }
        } else {
            scoreItem.setTargetValue(childManualAnnotation.getInput());
        }
        scoreItems.add(scoreItem);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(childManualAnnotation.getEnumValueList())) {
            return;
        }
        for (EnumMetricParam enumMetricParam : childManualAnnotation.getEnumValueList()) {
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(enumMetricParam.getMetrics())) {
                continue;
            }
            for (ChildMetricParam metric : enumMetricParam.getMetrics()) {
                if (Objects.isNull(metricParam)) {
                    continue;
                }
                getManualAnnotations(metric, scoreItems);
            }
        }
    }

    private Map<Long, List<String>> extractMetricOutputKeyMap(EvalTaskPo evalTaskPO) {
        if (StringUtils.isBlank(evalTaskPO.getExtra())) {
            return Maps.newHashMap();
        }

        TaskExtraParam taskExtraParam = JSON.parseObject(evalTaskPO.getExtra(), TaskExtraParam.class);
        List<TaskMetricParam> metricList = taskExtraParam.getMetricList();

        if (CollectionUtils.isEmpty(metricList)) {
            return Maps.newHashMap();
        }

        return metricList.stream().collect(Collectors.groupingBy(TaskMetricParam::getMetricId, Collectors.mapping(TaskMetricParam::getOutputKey, Collectors.toList())));
    }

    private String calculateArenaVoteResult(int metricResult) {
        ArenaVoteResultEnum voteResult = ArenaVoteResultEnum.parse(metricResult);
        String arenaEvalResult = "";
        switch (voteResult) {
            case PREFER_A:
                arenaEvalResult = ArenaVoteResultEnum.PREFER_A.getInfo();
                break;
            case PREFER_B:
                arenaEvalResult = ArenaVoteResultEnum.PREFER_B.getInfo();
                break;
            case BOTH_GOOD:
                arenaEvalResult = ArenaVoteResultEnum.BOTH_GOOD.getInfo();
                break;
            case BOTH_BAD:
                arenaEvalResult = ArenaVoteResultEnum.BOTH_BAD.getInfo();
                break;
            default:
                arenaEvalResult = ArenaVoteResultEnum.TBD.getInfo();
                break;
        }
        return arenaEvalResult;
    }


    private void checkParam(TaskParam param) {
        CommonUtils.checkEval(param != null, "参数为空");
        AbilityEnum abilityEnum = AbilityEnum.parse(param.getAbilityType());
        CommonUtils.checkEval(abilityEnum != null, "能力类型错误");
        CommonUtils.checkEval(StringUtils.isNotBlank(param.getName()), "任务名称不能为空");
        checkApplicationLimit(param);
        TaskConditionParam condition = new TaskConditionParam();
        condition.setCreatorMis(UserUtils.getUser().getLogin());
        condition.setName(param.getName());
        condition.setTaskType(param.getTaskType());
        List<EvalTaskPo> taskList = evalTaskGeneratorService.getByCondition(condition);
        CommonUtils.checkEval(CollectionUtils.isEmpty(taskList), "任务名称重复");
        if (null != param.getTaskType() && param.getTaskType().equals(TaskTypeEnum.ARENA.getCode())) {
            CommonUtils.checkEval(param.getModelTypeList().size() >= 2, "创建竞技任务时，模型数量不能少于2个");
            CommonUtils.checkEval(param.getArenaMetric() != null, "创建竞技任务时，竞技指标不能为空");
        } else {
            CommonUtils.checkEval(CollectionUtils.isNotEmpty(param.getDatasetIdList()), "数据集不能为空");
            checkDatasetSizeLimit(param);
            CommonUtils.checkEval(CollectionUtils.isNotEmpty(param.getMetricList()), "评测指标不能为空");
        }
    }

    private void checkDatasetSizeLimit(TaskParam param) {
        Long datasetSize = evalDatasetDetailGeneratorService.countByDatasetIdList(param.getDatasetIdList());
        EvalTaskLimitConfig config = Lion.getBean(ConfigUtil.getAppkey(), LionConstants.EVAL_TASK_LIMIT_CONFIG, EvalTaskLimitConfig.class);
        int limit = config.getWhitelist().stream()
                .filter(workspaceLimit -> workspaceLimit.getWorkspaceId().equals(WorkspaceContextHolder.getWorkspaceId()))
                .findFirst()
                .map(EvalTaskLimitConfig.WorkspaceLimit::getDatasetLimit)
                .orElse(config.getDefaultDatasetLimit());
        CommonUtils.checkEval(datasetSize <= limit, String.format("数据集不能超过%d条", limit));
    }


    /**
     * 检查待测应用数量限制
     *
     * @param param 任务参数
     */
    private void checkApplicationLimit(TaskParam param) {
        // 仅限制自动任务
        if (param.getTaskType() != null && param.getTaskType() != TaskTypeEnum.AUTO.getCode()) {
            return;
        }
        // 获取限制数量
        EvalTaskLimitConfig config = Lion.getBean(ConfigUtil.getAppkey(), LionConstants.EVAL_TASK_LIMIT_CONFIG, EvalTaskLimitConfig.class);
        int limit = config.getWhitelist().stream()
                .filter(workspaceLimit -> workspaceLimit.getWorkspaceId().equals(WorkspaceContextHolder.getWorkspaceId()))
                .findFirst()
                .map(EvalTaskLimitConfig.WorkspaceLimit::getApplicationLimit)
                .orElse(config.getDefaultApplicationLimit());

        // 获取待测应用列表
        List<?> listToCheck = null;


        if (param.getModelSource() == TaskModelSourceEnum.AI.getCode()) {
            // AI搭来源
            listToCheck = param.getAidaModelConfig();
        } else if (param.getModelSource() == TaskModelSourceEnum.APPLICATION.getCode()) {
            // 评测系统来源
            listToCheck = param.getModelResultParamList();
        }

        // 检查待测应用数量是否超过限制
        if (listToCheck != null) {
            CommonUtils.checkEval(listToCheck.size() <= limit, String.format("待测应用选择不能超过%d个", limit));
        }
    }

    private AreaEvalRequest buildAreaEvalRequest(TaskParam taskParam, List<EvalDatasetDetailPo> datasetDetailList) {
        AreaEvalRequest evalRequest = new AreaEvalRequest();
        User user = UserUtils.getUser();
        evalRequest.setAbility(AbilityEnum.parse(taskParam.getAbilityType()).getName());
        evalRequest.setCreatorMis(user.getLogin());
        evalRequest.setCreatorName(user.getName());
        evalRequest.setWorkspaceId(WorkspaceContextHolder.getWorkspaceId());
        evalRequest.setApplicationId(WorkspaceContextHolder.getApplicationId());
        //数据集不为空
        if (CollectionUtils.isNotEmpty(datasetDetailList)) {
            evalRequest.setSampleDataList(buildSampleList(datasetDetailList));
            evalRequest.setDataSetIdList(taskParam.getDatasetIdList());
        }
        evalRequest.setArenaMetric(taskParam.getArenaMetric());
        evalRequest.setModelType(taskParam.getModelTypeList());
        evalRequest.setName(taskParam.getName());
        //获取Aida配置
        String prompt = StringUtils.defaultIfEmpty(taskParam.getPrompt(), EMPTY);
        evalRequest.setModelConfigMap(getAidaConfig(taskParam.getTaskType(), prompt, taskParam.getModelTypeList()));
        evalRequest.setPrompt(prompt);
        return evalRequest;
    }

    /**
     * 构建评测任务请求
     */
    public EvalTaskRequest buildEvalTaskRequest(TaskParam taskParam, List<EvalDatasetDetailPo> datasetDetailList) {

        EvalTaskRequest evalRequest = new EvalTaskRequest();
        // 设置任务基本信息
        setBasicInfo(taskParam, evalRequest);

        // 设置数据集信息
        evalRequest.setSampleDataList(buildSampleList(datasetDetailList));
        evalRequest.setDataSetIdList(taskParam.getDatasetIdList());

        // 设置指标信息
        setMetric(taskParam, evalRequest);

        // 设置质检人信息
        if (CollectionUtils.isNotEmpty(taskParam.getInspectors())) {
            evalRequest.setInspectors(StringUtils.join(taskParam.getInspectors(), ","));
        }

        // 设置扩展功能信息
        setExtraInfo(taskParam, evalRequest);
        return evalRequest;
    }

    /**
     * 构建巡检评测任务请求
     */
    public void buildInspectEvalTaskRequest(EvalTaskRequest evalRequest, TaskParam taskParam, List<EvalDatasetDetailPo> datasetDetailList) {
        evalRequest.setSampleDataList(buildSampleList(datasetDetailList));
        evalRequest.setDataSetIdList(taskParam.getDatasetIdList());
        setMetric(taskParam, evalRequest);
        // 设置质检人信息
        if (CollectionUtils.isNotEmpty(taskParam.getInspectors())) {
            evalRequest.setInspectors(StringUtils.join(taskParam.getInspectors(), ","));
        }
        // 设置扩展功能信息
        setExtraInfo(taskParam, evalRequest);
    }

    /**
     * 设置任务基本信息
     */
    private void setBasicInfo(TaskParam taskParam, EvalTaskRequest evalRequest) {
        User user = UserUtils.getUser();
        evalRequest.setIsInner(aidaInvokeServiceProxy.isInner(user.getLogin()));
        evalRequest.setName(taskParam.getName());
        evalRequest.setAbility(AbilityEnum.parse(taskParam.getAbilityType()).getName());
        evalRequest.setCreatorMis(user.getLogin());
        evalRequest.setCreatorName(user.getName());
        evalRequest.setWorkspaceId(WorkspaceContextHolder.getWorkspaceId());
        evalRequest.setApplicationId(getApplicationId(taskParam));
        evalRequest.setCallType(taskParam.getCallType());
        evalRequest.setApplicationSource(taskParam.getModelSource());
        evalRequest.setTaskType(taskParam.getTaskType());
    }

    /**
     * 设置扩展功能信息
     */
    private void setExtraInfo(TaskParam taskParam, EvalTaskRequest evalRequest) {
        // 设置出参解析信息
        setOutputParam(taskParam, evalRequest);
        // 输入来源 @see TaskInputSourceEnum
        evalRequest.setInputSource(taskParam.getInputSource() == null ? TaskInputSourceEnum.DATASET.getCode() : taskParam.getInputSource());
        // 模拟次数
        evalRequest.setMockTimes(taskParam.getMockTimes());
        // 应用信息
        evalRequest.setApplicationConfig(new ArrayList<>(evalRequest.getApplicationModelMap().keySet()));
        // 绑定字段
        evalRequest.setBindFields(taskParam.getBindFields());
        // 模拟机器人信息
        evalRequest.setRobotMockId(taskParam.getRobotMockId());
        evalRequest.setRobotMockBindFields(taskParam.getRobotMockBindFields());
        // 单节点评测信息  // 回归测试
        if (CollectionUtils.isNotEmpty(taskParam.getAidaModelConfig()) && taskParam.getWhetherRegressDataset()) {
            //取错题集关联的节点id
            List<EvalDatasetPo> evalDatasetPos = evalDatasetGeneratorService.getByIdList(taskParam.getDatasetIdList());
            String nodeId = Optional.ofNullable(evalDatasetPos).orElse(Collections.emptyList()).stream().findFirst().map(EvalDatasetPo::getNodeId).orElse(null);
            if (StringUtils.isNotBlank(nodeId)) {
                //调用aida查询应用的数据
                InnerAppConfigDTO aidaRobotInfo = aidaInvokeServiceProxy.getAidaRobotInfo(taskParam.getAidaModelConfig().get(0).getApplicationId(), taskParam.getAidaModelConfig().get(0).getModelConfigVersionId());
                CommonUtils.checkEval(Objects.nonNull(aidaRobotInfo) && StringUtils.isNotBlank(aidaRobotInfo.getNodeConfig()), "获取大模型节点信息失败");
                Map<String, AidaNodeConfigInfoDTO> bizNodeMap = JSON.parseObject(aidaRobotInfo.getNodeConfig(), new TypeReference<Map<String, AidaNodeConfigInfoDTO>>() {
                });
                //获取错题集关联的大模型节点数据
                AidaNodeConfigInfoDTO nodeConfigInfo = Optional.ofNullable(bizNodeMap).orElse(Collections.emptyMap()).values().stream().filter(aidaNodeConfigInfoDTO -> InspectWorkbenchExecuteServiceImpl.LLM_NODE_TYPE.equals(aidaNodeConfigInfoDTO.getType()) && Objects.equals(aidaNodeConfigInfoDTO.getId(), nodeId)).findFirst().orElse(null);
                CommonUtils.checkEval(Objects.nonNull(nodeConfigInfo), "获取大模型节点为空");
                evalRequest.setNodeId(nodeConfigInfo.getId());
                CommonUtils.checkEval(Objects.nonNull(nodeConfigInfo.getConfig()), "获取大模型节点配置信息为空");
                AidaNodeConfigInfoDTO.Config config = JSON.parseObject(nodeConfigInfo.getConfig().toString(), AidaNodeConfigInfoDTO.Config.class);
                evalRequest.setNodeAppModelVersionId(config.getApp_model_version_id());
                evalRequest.setNodeApiToken(config.getAuthorization());
            }
        }
        // 历史字段来源
        evalRequest.setHistorySource(taskParam.getHistorySource());
        //多选评测指标
        List<TaskMetricParam> metricList = taskParam.getMetricList().stream().peek(taskMetricParam -> {
            if (StringUtils.isBlank(taskMetricParam.getOutputKey())) {
                taskMetricParam.setOutputKey(CommonConstants.COMPLETE_OUTPUT);
            }
        }).collect(Collectors.toList());
        evalRequest.setMetricList(metricList);
        evalRequest.setWhetherRegressDataset(taskParam.getWhetherRegressDataset());
    }

    private void setEvalRequestNodeInfo(EvalTaskRequest evalRequest, TaskBigModelNodeDTO taskBigModelNodeDTO) {
        evalRequest.setNodeId(taskBigModelNodeDTO.getNodeId());
        evalRequest.setNodeAppModelVersionId(taskBigModelNodeDTO.getVersionId());
        evalRequest.setNodeApiToken(taskBigModelNodeDTO.getApiToken());
    }

    private void setEvalRequestNodeInfoFromAida(EvalTaskRequest evalRequest, TaskParam taskParam) {
        InnerAppConfigDTO aidaRobotInfo = aidaInvokeServiceProxy.getAidaRobotInfo(taskParam.getAidaModelConfig().get(0).getApplicationId(), taskParam.getAidaModelConfig().get(0).getModelConfigVersionId(), false);

        if (null != aidaRobotInfo && aidaRobotInfo.getAppType().equals(ApplicationAbilityEnum.RULE.getCode()) && taskParam.getWhetherRegressDataset()) {
            CommonUtils.checkEval(null != aidaRobotInfo.getLlmNodeId() && null != aidaRobotInfo.getNodeAppModelVersionId() && null != aidaRobotInfo.getNodeApiToken(), "该工作流应用没有生效的大模型节点,请联系ai搭开发人员");
            evalRequest.setNodeId(aidaRobotInfo.getLlmNodeId());
            evalRequest.setNodeAppModelVersionId(aidaRobotInfo.getNodeAppModelVersionId());
            evalRequest.setNodeApiToken(aidaRobotInfo.getNodeApiToken());
        }
    }

    private String getApplicationId(TaskParam taskParam) {
        String applicationId = WorkspaceContextHolder.getApplicationId();
        if (StringUtils.isNotBlank(applicationId)) {
            return applicationId;
        }
        if (taskParam.getModelSource() == TaskModelSourceEnum.APPLICATION.getCode()) {
            if (taskParam.getApplicationConfig().size() == 1) {
                ApplicationConfigPo applicationConfig = applicationConfigGeneratorService.getById(taskParam.getApplicationConfig().get(0));
                applicationId = applicationConfig.getPlatformApp();
            }
        } else {
            if (taskParam.getAidaModelConfig().size() == 1) {
                applicationId = taskParam.getAidaModelConfig().get(0).getApplicationId();
            }
        }
        return applicationId;
    }

    /**
     * 设置指标信息
     */
    private void setMetric(TaskParam taskParam, EvalTaskRequest evalRequest) {
        List<Long> metricIdList = taskParam.getMetricList().stream().map(TaskMetricParam::getMetricId).collect(Collectors.toList());
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricIdList);
        List<Integer> queryMetricList = metricConfigList.stream().filter(metricConfigPo -> metricConfigPo.getDimension() == null || metricConfigPo.getDimension() == MetricDimensionEnum.QUERY.getCode()).map(MetricConfigPo::getId).map(Long::intValue).collect(Collectors.toList());
        List<Integer> sessionMetricList = metricConfigList.stream().filter(metricConfigPo -> metricConfigPo.getDimension() != null && metricConfigPo.getDimension() == MetricDimensionEnum.SESSION.getCode()).map(MetricConfigPo::getId).map(Long::intValue).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(queryMetricList)) {
            evalRequest.setQueryMetric(queryMetricList);
        }
        if (CollectionUtils.isNotEmpty(sessionMetricList)) {
            evalRequest.setSessionMetric(sessionMetricList);
        }
        evalRequest.setScoreThreshold(taskParam.getScoreThresholdList());
    }

    /**
     * 设置出参解析信息
     */
    private void setOutputParam(TaskParam taskParam, EvalTaskRequest evalRequest) {
        if (taskParam.getModelSource() == TaskModelSourceEnum.APPLICATION.getCode()) {
            List<ApplicationConfigPo> applicationConfigList = applicationConfigGeneratorService.getByIdList(taskParam.getApplicationConfig());
            evalRequest.setApplicationModelMap(applicationConfigList.stream().collect(Collectors.toMap(applicationConfigPo -> String.valueOf(applicationConfigPo.getId()), commonEvalStrategyService::getModelName)));
            if (CollectionUtils.isNotEmpty(taskParam.getModelResultParamList())) {
                Map<Long, String> applicationOutputMap = taskParam.getModelResultParamList().stream().collect(Collectors.toMap(ApplicationResultParam::getApplicationId, ApplicationResultParam::getModelResultParam));
                evalRequest.setApplicationOutputMap(applicationOutputMap);
            }
            if (CollectionUtils.isNotEmpty(taskParam.getResultParamList())) {
                evalRequest.setApplicationParamMap(taskParam.getResultParamList().stream().collect(Collectors.toMap(ApplicationResultParam::getApplicationId, ApplicationResultParam::getModelResultParam)));
            }
        } else {
            Map<String, String> applicationModelMap = new HashMap<>();
            Map<String, String> aidaInfoMap = new HashMap<>();
            Map<String, String> aidaResultMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(taskParam.getModelResultParamList())) {
                aidaInfoMap = taskParam.getModelResultParamList().stream().collect(Collectors.toMap(resultParam -> resultParam.getAidaModelConfig().getApplicationId() + "_" + resultParam.getAidaModelConfig().getModelConfigVersionId(), ApplicationResultParam::getModelResultParam));
            }
            if (CollectionUtils.isNotEmpty(taskParam.getResultParamList())) {
                aidaResultMap = taskParam.getResultParamList().stream().collect(Collectors.toMap(resultParam -> resultParam.getAidaModelConfig().getApplicationId() + "_" + resultParam.getAidaModelConfig().getModelConfigVersionId(), ApplicationResultParam::getModelResultParam));
            }
            Map<Long, String> outputParamMap = new HashMap<>();
            Map<Long, String> resultParamMap = new HashMap<>();

            List<String> appIds = taskParam.getAidaModelConfig().stream().map(AidaModelConfig::getApplicationId).collect(Collectors.toList());
            Map<String, AppDTO> appMap = aidaInvokeServiceProxy.listAidaAppDtoByIds(appIds).stream().collect(Collectors.toMap(AppDTO::getId, Function.identity()));

            for (AidaModelConfig aidaModelConfig : taskParam.getAidaModelConfig()) {
                ApplicationParam applicationParam = new ApplicationParam();
                // 兼容历史数据
                applicationParam.setNewName(appMap.containsKey(aidaModelConfig.getApplicationId()) ? appMap.get(aidaModelConfig.getApplicationId()).getName() + "-" + aidaModelConfig.getModelConfigVersionName() : aidaModelConfig.getModelConfigVersionId());
                applicationParam.setSource(ApplicationSourceEnum.VIRTUAL.getCode());
                if (evalRequest.getTaskType() != null && evalRequest.getTaskType() == TaskTypeEnum.INSPECT.getCode()) {
                    applicationParam.setCreateMis(evalRequest.getCreatorMis());
                } else {
                    applicationParam.setCreateMis(UserUtils.getUser().getLogin());
                }
                applicationParam.setAidaModelConfig(aidaModelConfig);
                ApplicationConfigPo applicationConfigPo = applicationExecuteService.getOrCreateFromThirdSystem(applicationParam);
                applicationModelMap.put(String.valueOf(applicationConfigPo.getId()), commonEvalStrategyService.getModelName(applicationConfigPo.getModelConfig()));
                String outputParam = aidaInfoMap.get(aidaModelConfig.getApplicationId() + "_" + aidaModelConfig.getModelConfigVersionId());
                if (StringUtils.isNotBlank(outputParam)) {
                    outputParamMap.put(applicationConfigPo.getId(), outputParam);
                }
                String resultParam = aidaResultMap.get(aidaModelConfig.getApplicationId() + "_" + aidaModelConfig.getModelConfigVersionId());
                if (StringUtils.isNotBlank(resultParam)) {
                    resultParamMap.put(applicationConfigPo.getId(), resultParam);
                }
            }
            evalRequest.setApplicationModelMap(applicationModelMap);
            if (MapUtils.isNotEmpty(outputParamMap)) {
                evalRequest.setApplicationOutputMap(outputParamMap);
            }
            if (MapUtils.isNotEmpty(resultParamMap)) {
                evalRequest.setApplicationParamMap(resultParamMap);
            }
        }
    }

    public List<SampleData> buildSampleList(List<EvalDatasetDetailPo> datasetDetailList) {
        List<SampleData> sampleDataList = new ArrayList<>();
        // 第一级分组，按照数据集分组
        Map<Long, List<EvalDatasetDetailPo>> datasetGroupMap = datasetDetailList.stream().collect(Collectors.groupingBy(EvalDatasetDetailPo::getDatasetId));
        for (Map.Entry<Long, List<EvalDatasetDetailPo>> datasetGroupEntry : datasetGroupMap.entrySet()) {
            // 第二级分组，按照sessionId分组
            Map<String, List<EvalDatasetDetailPo>> sessionGroupMap = datasetGroupEntry.getValue().stream().sorted(Comparator.comparing(EvalDatasetDetailPo::getId)).peek(sampleData -> {
                if (StringUtils.isBlank(sampleData.getSessionId()) || "-1".equals(sampleData.getSessionId())) {
                    sampleData.setSessionId(CommonUtils.uuid());
                }
            }).collect(Collectors.groupingBy(EvalDatasetDetailPo::getSessionId, LinkedHashMap::new, Collectors.toList()));
            for (Map.Entry<String, List<EvalDatasetDetailPo>> sessionGroupEntry : sessionGroupMap.entrySet()) {
                int turn = 1;
                for (EvalDatasetDetailPo datasetDetail : sessionGroupEntry.getValue()) {
                    SampleData sampleData = new SampleData();
                    sampleData.setDetailId(datasetDetail.getId());
                    sampleData.setInputContent(datasetDetail.getInput());
                    sampleData.setSessionId(datasetDetail.getSessionId());
                    sampleData.setSummary(datasetDetail.getSummary());
                    //业务参数
                    sampleData.setBusinessParam(datasetDetail.getBusinessParam());
                    if (StringUtils.isNotBlank(datasetDetail.getExpect())) {
                        List<ModelExpectDTO> modelExpect = JSONObject.parseArray(datasetDetail.getExpect(), ModelExpectDTO.class);
                        sampleData.setModelExpectList(modelExpect);
                    }
                    sampleData.setExpectedResult(datasetDetail.getExpect());
                    sampleData.setDatasetId(datasetDetail.getDatasetId());
                    sampleData.setTurn(turn);
                    turn++;
                    if (StringUtils.isNotBlank(datasetDetail.getContent())) {
                        sampleData.setContent(datasetDetail.getContent());
                    }
                    if (StringUtils.isNotBlank(datasetDetail.getParams())) {
                        sampleData.setParams(JSON.parseObject(datasetDetail.getParams(), new TypeReference<Map<String, String>>() {
                        }));
                    }
                    if (StringUtils.isNotBlank(datasetDetail.getOutput())) {
                        // 离线模式下，模型结果取自output
                        List<ModelOutputDTO> modelOutputDTOList = JSONArray.parseArray(datasetDetail.getOutput(), ModelOutputDTO.class);
                        sampleData.setOutput(modelOutputDTOList);
                    }
                    sampleDataList.add(sampleData);
                }
            }
        }
        return sampleDataList;
    }

//    private List<ScoreThresholdParam> getScoreThreshold(List<ScoreThresholdParam> scoreThresholdParams) {
//        if (CollectionUtils.isEmpty(scoreThresholdParams)) {
//            return new ArrayList<>();
//        }
//        List<ScoreThresholdParam> scoreThreshold = new ArrayList<>();
//
//        List<Long> metricIdList = scoreThresholdParams.stream().map(ScoreThresholdParam::getMetric).map(Long::valueOf).collect(Collectors.toList());
//        List<MetricConfigPo> metricConfigPoList = metricConfigGeneratorService.getByIdList(metricIdList);
//        Map<Long, MetricConfigPo> metricConfigPoMap = metricConfigPoList.stream().collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));
//        for (ScoreThresholdParam thresholdParam : scoreThresholdParams) {
//            ScoreThresholdParam scoreThresholdParam = new ScoreThresholdParam();
//            if (null == thresholdParam.getMetric() || CollectionUtils.isEmpty(thresholdParam.getScoreThresholdList()) && null == thresholdParam.getScoreThreshold()) {
//                continue;
//            }
//            //指标id
//            Integer metric = thresholdParam.getMetric();
//            scoreThresholdParam.setMetric(metric);
//            MetricConfigPo metricConfigPo = metricConfigPoMap.get(metric.longValue());
//            if (metricConfigPo.getMetricType() == MetricTypeEnum.NUMBER.getCode()) {
//                //打分型指标
//                scoreThresholdParam.setScoreThreshold(scoreThresholdParam.getScoreThreshold());
//            } else {
//                //枚举型指标
//                scoreThresholdParam.setScoreThresholdList(thresholdParam.getScoreThresholdList());
//            }
//            scoreThresholdParam.setOutputKey(thresholdParam.getOutputKey());
//            scoreThreshold.add(scoreThresholdParam);
//        }
//
//        return scoreThreshold;
//    }

    private Map<String, String> getAidaConfig(Integer taskType, String prompt, List<String> modelList) {
        if (taskType == null || TaskTypeEnum.ARENA.getCode() != taskType) {
            return new HashMap<>();
        }
        Map<String, String> aidaConfigMap = new HashMap<>();
        try {
            List<String> modelConfigList = Lion.getList(ConfigUtil.getAppkey(), "aida.exclude.model.list", String.class);
            List<String> pythonModelConfigList = Lion.getList(ConfigUtil.getAppkey(), "aida.python.model.list", String.class);
            if (CollectionUtils.isNotEmpty(modelList)) {
                for (String model : modelList) {
                    if (CollectionUtils.isNotEmpty(modelConfigList) && modelConfigList.contains(model)) {
                        continue;
                    }
                    if (CollectionUtils.isNotEmpty(pythonModelConfigList) && pythonModelConfigList.contains(model)) {
                        continue;
                    }
                    Map<String, Object> aidaRequest = new HashMap<>();
                    aidaRequest.put("user", "eval");
                    if (StringUtils.isBlank(prompt)) {
                        Map<String, String> promptMap = Lion.getMap(ConfigUtil.getAppkey(), "default.prompt.map", String.class);
                        if (MapUtils.isNotEmpty(promptMap) && promptMap.containsKey(model)) {
                            prompt = promptMap.get(model);
                        }
                    }
                    Map<String, String> userInputFormMap = Lion.getMap(ConfigUtil.getAppkey(), "default.user.input.form.map", String.class);
                    if (MapUtils.isNotEmpty(userInputFormMap) && userInputFormMap.containsKey(model)) {
                        List<Map<String, Object>> userInputFormList = JSON.parseObject(userInputFormMap.get(model), new TypeReference<List<Map<String, Object>>>() {
                        });
                        aidaRequest.put("user_input_form", userInputFormList);
                    }
                    aidaRequest.put("pre_prompt", prompt);
                    Map<String, Object> modelMap = new HashMap<>();
                    modelMap.put("name", model);
                    Map<String, Object> completionParams = JSON.parseObject("{\n" + "                        \"max_tokens\": 1024,\n" + "                            \"frequency_penalty\": 0.2,\n" + "                            \"presence_penalty\": 0,\n" + "                            \"temperature\": 0,\n" + "                            \"top_p\": 0.5,\n" + "                            \"repetition_penalty\": 1,\n" + "                            \"top_k\": 1\n" + "                    }");
                    modelMap.put("completion_params", completionParams);
                    modelMap.put("provider", getProvider(model));
                    aidaRequest.put("model", modelMap);
                    HttpHeaders httpHeaders = new HttpHeaders();
                    httpHeaders.add(HttpHeaders.AUTHORIZATION, "Bearer " + Lion.getString(ConfigUtil.getAppkey(), "ai.aida.save.secret", "app-6GvWK2h9VHYu8cVT06JMNIc1"));
                    httpHeaders.add(HttpHeaders.CONTENT_TYPE, "application/json");
                    String url = Lion.getString(ConfigUtil.getAppkey(), "ai.aida.save.url", "https://aida.csp.test.sankuai.com/v1/appVersion/save");
                    String response = HttpUtil.sendPost(JSON.toJSONString(aidaRequest), httpHeaders, url, String.class, httpConfig.getRestTemplate());
                    log.info("获取ai搭配置信息返回结果,response={},request={}", response, JSON.toJSONString(aidaRequest));
                    JSONObject jsonObject = JSON.parseObject(response);
                    aidaConfigMap.put(model, jsonObject.getString("model_config_version_id"));
                }
            }
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
            log.error("创建ai搭机器人失败", e);
        }
        return aidaConfigMap;
    }

    private String getProvider(String model) {
        if (StringUtils.isBlank(model)) {
            return "";
        }
        if (model.startsWith("gpt")) {
            return "openai";
        }
        if (model.startsWith("abab")) {
            return "minimax";
        }
        if (model.contains("glm")) {
            return "zhipuai";
        }
        return "mtselfllm";
    }

    public boolean isValidNumber(String str) {
        return StringUtils.isNumeric(str);
    }

    @Data
    private static class ScoreResult implements Serializable {
        private String reason;
        private String score;
    }


    public Map<String, EvalStrategyService> getEvalServiceMap() {
        return evalServiceMap;
    }
}
