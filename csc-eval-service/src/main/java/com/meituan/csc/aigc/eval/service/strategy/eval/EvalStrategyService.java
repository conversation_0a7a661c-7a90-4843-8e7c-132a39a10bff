package com.meituan.csc.aigc.eval.service.strategy.eval;

import com.meituan.csc.aigc.eval.dto.gpt.GptReplyDTO;
import com.meituan.csc.aigc.eval.exception.EvalException;
import com.meituan.csc.aigc.eval.param.task.ApplicationRequestParam;
import com.meituan.csc.aigc.eval.param.task.EvalTaskRequest;
import com.meituan.csc.aigc.eval.service.strategy.eval.impl.CommonEvalStrategyService;

/**
 * <AUTHOR>
 * @date 2023/12/12
 */
public interface EvalStrategyService {

    /**
     * 执行评测
     *
     * @param evalRequest 评测请求
     */
    void execute(EvalTaskRequest evalRequest);

    /**
     * 获取范式名称
     *
     * @return 范式名称
     */
    String getName();

    /**
     * 执行任务
     *
     * @param evalRequest 请求对象
     * @param evalIdInfo  ID信息
     */

    default void executeTask(EvalTaskRequest evalRequest, CommonEvalStrategyService.EvalIdInfo evalIdInfo) {
        throw new EvalException("当前能力类型不支持自动评测功能");
    }

    GptReplyDTO getGptOutput(ApplicationRequestParam applicationRequestParam) throws Exception;

}
