package com.meituan.csc.aigc.eval.service.strategy.eval.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.Lion;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.StoreBoundResponse;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.csc.aigc.agent.enums.reply.ReplyEndTypeEnum;
import com.meituan.csc.aigc.eval.constants.CatConstants;
import com.meituan.csc.aigc.eval.constants.CommonConstants;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.constants.RedisConstants;
import com.meituan.csc.aigc.eval.dao.entity.*;
import com.meituan.csc.aigc.eval.dao.service.generator.*;
import com.meituan.csc.aigc.eval.dto.gpt.GptReplyDTO;
import com.meituan.csc.aigc.eval.dto.gpt.GptRequestDTO;
import com.meituan.csc.aigc.eval.dto.metric.MetricReplyDTO;
import com.meituan.csc.aigc.eval.dto.pb.PbRequest;
import com.meituan.csc.aigc.eval.dto.pb.PbResponse;
import com.meituan.csc.aigc.eval.dto.task.ModelExpectDTO;
import com.meituan.csc.aigc.eval.dto.task.ModelOutputDTO;
import com.meituan.csc.aigc.eval.enums.*;
import com.meituan.csc.aigc.eval.enums.query.EvalResultEnum;
import com.meituan.csc.aigc.eval.enums.task.TaskHistorySourceEnum;
import com.meituan.csc.aigc.eval.exception.EvalException;
import com.meituan.csc.aigc.eval.exception.EvalSqlException;
import com.meituan.csc.aigc.eval.helper.PikeThreadLocalHelper;
import com.meituan.csc.aigc.eval.param.ConversationInfo;
import com.meituan.csc.aigc.eval.param.dataset.SampleData;
import com.meituan.csc.aigc.eval.param.gpt.*;
import com.meituan.csc.aigc.eval.param.model.ModelConfigRequestParam;
import com.meituan.csc.aigc.eval.param.session.SessionInfoParam;
import com.meituan.csc.aigc.eval.param.task.*;
import com.meituan.csc.aigc.eval.proxy.AidaInvokeServiceProxy;
import com.meituan.csc.aigc.eval.proxy.GptRequestServiceProxy;
import com.meituan.csc.aigc.eval.proxy.PbServiceProxy;
import com.meituan.csc.aigc.eval.proxy.RedisClientProxy;
import com.meituan.csc.aigc.eval.service.push.DxPushTextService;
import com.meituan.csc.aigc.eval.service.strategy.metric.MetricStrategyService;
import com.meituan.csc.aigc.eval.utils.*;
import com.meituan.csc.aigc.runtime.common.DialogResultCodeEnum;
import com.meituan.csc.aigc.runtime.common.ResultCodeEnum;
import com.meituan.csc.aigc.runtime.dto.aida.AidaFinalRes;
import com.meituan.csc.aigc.runtime.dto.recommendation.DialogResponse;
import com.meituan.csc.aigc.runtime.inner.dto.InnerAidaHistoryDTO;
import com.meituan.csc.aigc.runtime.inner.dto.InnerAppConfigDTO;
import com.meituan.inf.xmdlog.ConfigUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CommonEvalStrategyService {
    public static final String GPT_SUCCESS_CODE = "200";

    public static final String GPT_INTERRUPT_CODE = "300";

    private static final ExecutorService EVAL_THREAD_POOL = ThreadUtils.createPoolWithTraceAndCat(40, 200, 1000, "execute-aida-eval-%d");

    protected static final ExecutorService PARALLEL_EVAL_THREAD_POOL = ThreadUtils.createPoolWithTraceAndCat(40, 200, 1000, "execute-parallel-eval-%d");

    @Autowired
    protected EvalTaskGeneratorService evalTaskGeneratorService;

    @Autowired
    protected EvalTaskQueryDetailGeneratorService evalTaskQueryDetailGeneratorService;

    @Autowired
    protected EvalTaskQueryGeneratorService evalTaskQueryGeneratorService;

    @Autowired
    protected EvalTaskSessionGeneratorService evalTaskSessionGeneratorService;

    @Autowired
    private DxPushTextService dxPushTextService;

    @Autowired
    protected ApplicationConfigGeneratorService applicationConfigGeneratorService;

    @Autowired
    private GptRequestServiceProxy gptRequestServiceProxy;

    @Autowired
    protected AidaInvokeServiceProxy aidaInvokeServiceProxy;

    @Autowired
    private RedisClientProxy redisStoreClient;

    @Autowired
    private List<MetricStrategyService> metricStrategyServiceList;

    private Map<String, MetricStrategyService> metricStrategyServiceMap;

    @Autowired
    private ManualMarkTaskGeneratorService manualMarkTaskGeneratorService;

    @Autowired
    private EvalTaskLogGeneratorService evalTaskLogGeneratorService;

    @Autowired
    protected MetricConfigGeneratorService metricConfigGeneratorService;

    @Autowired
    private PbServiceProxy pbServiceProxy;

    @Autowired
    private RedisClientProxy redisClientProxy;

    @Autowired
    private EvalDatasetDetailGeneratorService evalDatasetDetailGeneratorService;

    @PostConstruct
    public void init() {
        if (MapUtils.isEmpty(metricStrategyServiceMap)) {
            metricStrategyServiceMap = new HashMap<>();
            for (MetricStrategyService metricStrategyService : metricStrategyServiceList) {
                metricStrategyServiceMap.put(metricStrategyService.getName(), metricStrategyService);
            }
        }
    }

    public void execute(EvalTaskRequest evalRequest) {
        // 异步执行任务
        EVAL_THREAD_POOL.submit(() -> {
            // 数据入库
            logData2Db(evalRequest);
            // 执行评测
            doExecute(evalRequest.getCreatorMis());
        });
    }

    public void doExecute(String createMis) {
        // 获取分布式锁
        List<EvalTaskPo> evalTaskPoList = getQueueTask(createMis);
        boolean lock = getLock(evalTaskPoList.get(0));
        if (!lock) {
            return;
        }
        EvalTaskRequest evalRequest;
        EvalIdInfo evalIdInfo;


        if (CollectionUtils.isNotEmpty(evalTaskPoList)) {
            // 取队列中第一个排队中任务进行执行
            EvalTaskPo evalTaskPo = evalTaskPoList.get(0);
            if (StringUtils.isBlank(evalTaskPo.getExtra())) {
                // 获取任务信息失败，更新任务状态为失败
                updateTaskResultStatusAndScore(evalTaskPo.getId(), AutoTaskStatusEnum.FAILED, null);
            } else {
                Transaction transaction = Cat.newTransaction(CatConstants.TASK_EXECUTE,
                        Optional.ofNullable(TaskTypeEnum.parse(evalTaskPo.getType()))
                                .map(TaskTypeEnum::getName)
                                .orElse("Unknown"));
                try {
                    Map<String, String> extMap = JSON.parseObject(evalTaskPo.getExtra(), new TypeReference<Map<String, String>>() {
                    });
                    if (!extMap.containsKey("evalRequest")) {
                        // 获取任务信息失败，更新任务状态为失败
                        updateTaskResultStatusAndScore(evalTaskPo.getId(), AutoTaskStatusEnum.FAILED, null);
                    } else {
                        evalRequest = JSON.parseObject(extMap.get("evalRequest"), EvalTaskRequest.class);
                        evalRequest.setSampleDataList(getSampleDataList(evalTaskPo.getId()));
                        evalRequest.setSessionList(getSessionList(evalTaskPo.getId()));
                        evalIdInfo = JSON.parseObject(extMap.get("evalIdInfo"), CommonEvalStrategyService.EvalIdInfo.class);
                        evalIdInfo.setEvalTaskId(evalTaskPo.getId());
                        checkParam(evalIdInfo);
                        executeTask(evalRequest, evalIdInfo);
                    }
                } catch (Exception e) {
                    transaction.setStatus(e);
                    Cat.logError(e);
                    log.error("执行评测任务异常,evalTaskPo={},msg={}", JSON.toJSONString(evalTaskPo), e.getMessage(), e);
                    // 获取任务信息失败，更新任务状态为失败
                    updateTaskResultStatusAndScore(evalTaskPo.getId(), AutoTaskStatusEnum.FAILED, null);
                } finally {
                    transaction.complete();
                }
            }
        }

        //取到缓存，说明是暂停了。这个时候不需要释放锁，因为暂停接口里已经释放了
        Long taskId = evalTaskPoList.get(0).getId();
        if (Objects.nonNull(taskId) && isTaskPause(taskId)) {
            return;
        }
        releaseLock(evalTaskPoList.get(0));
    }

    public List<EvalTaskSessionPo> getSessionList(Long taskId) {
        ZebraForceMasterHelper.forceMasterInLocalContext();
        List<EvalTaskSessionPo> sessionList = evalTaskSessionGeneratorService.getByTaskId(taskId);
        if (CollectionUtils.isEmpty(sessionList)) {
            return new ArrayList<>();
        }
        ZebraForceMasterHelper.clearLocalContext();
        // 按照session顺序进行排序
        sessionList.sort(Comparator.comparing(EvalTaskSessionPo::getId));
        return sessionList;
    }

    /**
     * 获取样本集数据
     *
     * @param taskId 任务ID
     * @return 样本集数据
     */
    public List<SampleData> getSampleDataList(Long taskId) {
        ZebraForceMasterHelper.forceMasterInLocalContext();
        List<EvalTaskQueryPo> evalTaskQueryList = evalTaskQueryGeneratorService.getByTaskId(taskId);
        if (CollectionUtils.isEmpty(evalTaskQueryList)) {
            return new ArrayList<>();
        }
        Map<Long, List<EvalTaskQueryDetailPo>> queryIdMap = Maps.newHashMap();
        List<EvalTaskQueryDetailPo> evalTaskQueryDetailList = evalTaskQueryDetailGeneratorService.getByTaskId(taskId);
        ZebraForceMasterHelper.clearLocalContext();
        // 重新组织为SampleData
        if (CollectionUtils.isNotEmpty(evalTaskQueryDetailList)) {
            for (EvalTaskQueryDetailPo detailPo : evalTaskQueryDetailList) {
                if (queryIdMap.containsKey(detailPo.getQueryId())) {
                    queryIdMap.get(detailPo.getQueryId()).add(detailPo);
                } else {
                    List<EvalTaskQueryDetailPo> list = new ArrayList<>();
                    list.add(detailPo);
                    queryIdMap.put(detailPo.getQueryId(), list);
                }
            }
        }
        return evalTaskQueryList.stream().sorted(Comparator.comparing(EvalTaskQueryPo::getId)).map(evalTaskQueryPo -> {
            List<EvalTaskQueryDetailPo> detailPoList = queryIdMap.get(evalTaskQueryPo.getId());
            List<ModelExpectDTO> modelExpectList = detailPoList.stream().map(detailPo -> {
                ModelExpectDTO modelExpectDTO = new ModelExpectDTO();
                modelExpectDTO.setOutputKey(detailPo.getOutputKey());
                modelExpectDTO.setExpect(detailPo.getExpect());
                return modelExpectDTO;
            }).collect(Collectors.toList());
            SampleData sampleData = new SampleData();
            sampleData.setSessionId(evalTaskQueryPo.getSessionId());
            sampleData.setConversationId(evalTaskQueryPo.getConversationId());
            sampleData.setInputContent(evalTaskQueryPo.getInput());
            sampleData.setModelExpectList(modelExpectList);
            sampleData.setDatasetId(evalTaskQueryPo.getDatasetId());
            sampleData.setQueryId(evalTaskQueryPo.getId());
            sampleData.setStatus(evalTaskQueryPo.getStatus());
            sampleData.setTurn(evalTaskQueryPo.getTurn());
            if (StringUtils.isNotBlank(evalTaskQueryPo.getParams())) {
                sampleData.setParams(JSON.parseObject(evalTaskQueryPo.getParams(), new TypeReference<Map<String, String>>() {
                }));
            }
            sampleData.setBusinessParam(evalTaskQueryPo.getBusinessParam());
            sampleData.setTaskQueryDetailPoList(detailPoList);
            return sampleData;
        }).collect(Collectors.toList());
    }

    protected void executeTask(EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo) {
        throw new EvalException("该能力未实现执行任务策略");
    }

    protected int getParallelNum(EvalTaskRequest evalRequest) {
        Map<String, Integer> parallelConfig = Lion.getMap(ConfigUtil.getAppkey(), LionConstants.EVAL_TASK_PARALLEL_NUM, Integer.class);
        int parallelNum = parallelConfig.getOrDefault(evalRequest.getCreatorMis(), parallelConfig.getOrDefault("default", 1));
        log.info("用户【{}】的并行任务数量配置为{}", evalRequest.getCreatorMis(), parallelNum);
        return parallelNum;
    }

    protected EvalExecuteResult mergeResult(List<Future<EvalExecuteResult>> futureList) {
        EvalExecuteResult evalExecuteResult = new EvalExecuteResult();
        AutoTaskStatusEnum isTaskEnd = AutoTaskStatusEnum.FINISHED;
        for (Future<EvalExecuteResult> future : futureList) {
            try {
                // 超时时间控制
                EvalExecuteResult curEvalExecuteResult = future.get(86400, TimeUnit.SECONDS);
                if (curEvalExecuteResult != null) {
                    // 状态优先级 1.删除 2.超时 3.执行结束-自动结束/人工标注
                    if (!AutoTaskStatusEnum.DELETE.equals(isTaskEnd)) {
                        if (AutoTaskStatusEnum.DELETE.equals(curEvalExecuteResult.getIsTaskEnd())) {
                            isTaskEnd = AutoTaskStatusEnum.DELETE;
                        } else if (!AutoTaskStatusEnum.TIMEOUT.equals(isTaskEnd)) {
                            isTaskEnd = curEvalExecuteResult.getIsTaskEnd();
                        } else if (AutoTaskStatusEnum.PAUSE.equals(curEvalExecuteResult.getIsTaskEnd())) {
                            isTaskEnd = AutoTaskStatusEnum.PAUSE;
                        }
                    }
                }
            } catch (Exception e) {
                Cat.logError(new EvalException(e));
                log.error("获取评测结果失败,msg={}", e.getMessage(), e);
            }
        }
        evalExecuteResult.setIsTaskEnd(isTaskEnd);
        return evalExecuteResult;
    }

    public String getOutputMessage(Long applicationId, String expect, EvalTaskRequest evalRequest, String reply) {
        if (evalRequest == null) {
            return reply;
        }
        String message = reply;
        if (evalRequest.getHistorySource() != null && evalRequest.getHistorySource() == TaskHistorySourceEnum.EXPECT.getCode()) {
            List<ModelExpectDTO> modelExpectList = DataConvertUtil.tryConvertObjectArray(expect, ModelExpectDTO.class);
            if (CollectionUtils.isNotEmpty(modelExpectList)) {
                message = modelExpectList.get(0).getExpect();
            } else {
                message = expect;
            }
        } else if (StringUtils.isNotBlank(reply)) {
            if (MapUtils.isNotEmpty(evalRequest.getApplicationOutputMap()) && evalRequest.getApplicationOutputMap().containsKey(applicationId)) {
                String outputParam = evalRequest.getApplicationOutputMap().get(applicationId);
                JSONObject jsonObject = DataConvertUtil.tryConvertJson(reply);
                if (jsonObject != null) {
                    message = jsonObject.getString(outputParam);
                }
            }
            if (StringUtils.isBlank(message)) {
                message = reply;
            }
        }
        return message;
    }

    protected void handleTaskEnd(EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo, AutoTaskStatusEnum isTaskEnd) {
        Double avgScore = null;
        if (AutoTaskStatusEnum.FINISHED.equals(isTaskEnd)) {
            // 兼容AI搭原有计算指标逻辑
            if (CollectionUtils.isNotEmpty(evalRequest.getQueryMetric()) && evalRequest.getQueryMetric().size() == 1) {
                Integer metricId = evalRequest.getQueryMetric().get(0);
                if (EvalMetricTypeEnum.COMMON_METRIC.contains(metricId)) {
                    Double scoreThreshold = null;
                    if (AbilityEnum.MULTI_ROUND.getName().equals(evalRequest.getAbility())) {
                        scoreThreshold = getScoreThreshold(metricId, getAvgEvalScore(evalRequest.getScoreThreshold(), metricId));
                    }
                    // 统计平均分
                    avgScore = calScore(evalIdInfo.getEvalTaskId(), scoreThreshold);
                }
            }
            // 推送ai搭评测平台大象
            sendElephant(evalIdInfo, evalRequest, avgScore);
        } else if (AutoTaskStatusEnum.MARK.equals(isTaskEnd)) {
            // 人工指标的创建标注任务
            createMarkTask(evalRequest, evalIdInfo);
        }
        // 更新分数和状态
        updateTaskResultStatusAndScore(evalIdInfo.getEvalTaskId(), isTaskEnd, avgScore);
    }

    protected double calScore(Long taskId, Double scoreThreshold) {
        throw new EvalException("该能力未实现计算平均分策略");
    }

    public boolean getLock(EvalTaskPo taskPo) {
        if (Objects.isNull(taskPo)) {
            return false;
        }
        String createMis = taskPo.getCreatorMis();
        try {
            Map<String, Integer> parallelMap = Lion.getMap(ConfigUtil.getAppkey(), LionConstants.EXECUTE_PARALLEL_MAP, Integer.class);
            int parallel = 1;
            if (parallelMap != null && parallelMap.containsKey(createMis)) {
                parallel = parallelMap.get(createMis);
            }
            long expireTime = Lion.getLong(ConfigUtil.getAppkey(), LionConstants.EVAL_RUNNING_CACHE_EXPIRE_TIME, 3153600000L);
            StoreBoundResponse storeBoundResponse = redisStoreClient.incrWithUpperBound(new StoreKey(RedisConstants.EVAL_PARALLEL_TASK_EXECUTE_LOCK, createMis), 1, parallel, 0, expireTime);
            log.info("{}:执行评测任务获取锁:{},执行的任务:{}", createMis, JSON.toJSONString(storeBoundResponse.isOperated()), JSON.toJSONString(taskPo));
            if (storeBoundResponse.isOperated()) {
                return storeBoundResponse.isOperated();
            }
            log.info("{}:执行评测任务获取锁失败,执行的任务:{}", createMis, JSON.toJSONString(taskPo));
            return false;
        } catch (Exception e) {
            Cat.logError(e);
            log.error("获取分布式锁失败,createMis={},mssg={}", createMis, e.getMessage(), e);
        }
        return false;
    }

    protected EvalRequestParam buildEvalRequestParam(List<EvalTaskQueryDetailPo> queryDetailList, SampleData sampleData, String applicationConfigId, String model, EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo, ConversationInfo conversationInfo, String robotId, List<ChatGptHttpRequest.GptMessage> messageList, EvalTaskSessionPo session) {
        EvalRequestParam evalRequestParam = new EvalRequestParam();
        evalRequestParam.setInputContent(sampleData.getInputContent());
        evalRequestParam.setParams(sampleData.getParams());
        evalRequestParam.setModel(model);
        evalRequestParam.setEvalTaskId(evalIdInfo.getEvalTaskId());
        evalRequestParam.setCallType(evalRequest.getCallType());
        evalRequestParam.setAbility(evalRequest.getAbility());
        evalRequestParam.setConversionInfo(conversationInfo);
        evalRequestParam.setRobotId(robotId);
        evalRequestParam.setConversationId(session == null ? null : session.getId());
        evalRequestParam.setCreatorMis(evalRequest.getCreatorMis());
        evalRequestParam.setBusinessParam(sampleData.getBusinessParam());
        ApplicationConfigPo applicationConfigPo = applicationConfigGeneratorService.getById(applicationConfigId);
        if (applicationConfigPo != null) {
            InnerAppConfigDTO aidaRobotInfo = aidaInvokeServiceProxy.getAidaRobotInfo(applicationConfigPo.getPlatformApp(), applicationConfigPo.getRobotId());
            evalRequestParam.setAppOpenStreaming(aidaRobotInfo.getAppOpenStreaming() == null ? Boolean.FALSE : aidaRobotInfo.getAppOpenStreaming());
            evalRequestParam.setAidaAppId(applicationConfigPo.getPlatformApp());
        }

        evalRequestParam.setQueryDetailList(queryDetailList);
        evalRequestParam.setDatasetId(sampleData.getDatasetId());
        evalRequestParam.setQueryId(sampleData.getQueryId());
        evalRequestParam.setSessionId(sampleData.getSessionId());
        evalRequestParam.setTestApplication(applicationConfigId);
        evalRequestParam.setMessageList(messageList);
        evalRequestParam.setSampleData(sampleData);
        evalRequestParam.setIsInner(evalRequest.getIsInner());
        evalRequestParam.setApplicationParam(MapUtils.isEmpty(evalRequest.getApplicationParamMap()) ? null : evalRequest.getApplicationParamMap().get(Long.parseLong(applicationConfigId)));
        evalRequestParam.setTaskType(evalRequest.getTaskType());
        evalRequestParam.setNodeId(evalRequest.getNodeId());
        evalRequestParam.setNodeAppModelVersionId(evalRequest.getNodeAppModelVersionId());
        evalRequestParam.setNodeApiToken(evalRequest.getNodeApiToken());
        return evalRequestParam;
    }

    protected String getAidaAppId(String application) {
        Long applicationId = DataConvertUtil.tryConvertLongWithNull(application);
        if (applicationId != null) {
            ApplicationConfigPo applicationConfig = applicationConfigGeneratorService.getById(applicationId);
            if (applicationConfig != null) {
                return applicationConfig.getPlatformApp();
            }
        }
        return null;
    }

    public void releaseLock(EvalTaskPo evalTaskPo) {
        if (Objects.isNull(evalTaskPo)) {
            return;
        }
        String createMis = evalTaskPo.getCreatorMis();
        try {
            long lockSize = redisStoreClient.get(new StoreKey(RedisConstants.EVAL_PARALLEL_TASK_EXECUTE_LOCK, createMis));
            if (lockSize > 0) {
                redisStoreClient.decrease(new StoreKey(RedisConstants.EVAL_PARALLEL_TASK_EXECUTE_LOCK, createMis), 1);
                log.info("{}:执行评测任务释放锁成功,执行的任务:{}", createMis, JSON.toJSONString(evalTaskPo));
            } else {
                Cat.logEvent(CatConstants.TASK_PARALLEL_LOCK_EXCEPTION, createMis);
                log.warn("任务锁发生计数错误,mis={},执行的任务:{}", createMis, JSON.toJSONString(evalTaskPo));
            }
        } catch (Exception e) {
            Cat.logError(e);
            log.error("释放分布式锁失败,createMis={},执行的任务:{},msg={}", createMis, JSON.toJSONString(evalTaskPo), e.getMessage(), e);
        }
    }

    public void setInterruptCache(Long taskId) {
        redisStoreClient.set(new StoreKey(RedisConstants.TASK_INTERRUPT, taskId), taskId);
    }

    protected AutoTaskStatusEnum interruptCheck(Date startTime, Long taskId, Integer index, Integer size) {
        // 超时判断
        // TODO: 2024/10/17 超时时间改为lion
        if (startTime != null && new Date().getTime() - startTime.getTime() >= 7 * 3600000) {
            return AutoTaskStatusEnum.TIMEOUT;
        }
        // 任务删除判断
        if (isTaskDelete(taskId)) {
            return AutoTaskStatusEnum.DELETE;
        }

        // 任务暂停判断
        //index是当前执行的数据list的索引    size是session或者query的list长度   相等说明当前这条数据是最后一条，不需要再暂停了
        if (index != size - 1) {
            if (isTaskPause(taskId)) {
                return AutoTaskStatusEnum.PAUSE;
            }
        }
        return null;
    }

    /**
     * 任务暂停判断
     *
     * @param taskId
     * @return
     */
    public boolean isTaskPause(Long taskId) {
        try {
            Boolean isPause = redisStoreClient.get(new StoreKey(RedisConstants.EVAL_TASK_PAUSE, taskId));
            if (Objects.nonNull(isPause) && isPause) {
                log.info("检测到任务被暂停，执行暂停,taskId={}", taskId);
                return true;
            }
            return false;
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
            log.error("获取任务暂停标识失败,taskId={},msg={}", taskId, e.getMessage(), e);
        }
        return false;
    }

    protected boolean isTaskDelete(Long taskId) {
        try {
            Long cache = redisStoreClient.get(new StoreKey(RedisConstants.TASK_INTERRUPT, taskId));
            if (cache != null) {
                log.info("检测到任务被删除，执行中断,taskId={}", taskId);
                return true;
            }
            return false;
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
            log.error("获取任务中断标识失败,taskId={},msg={}", taskId, e.getMessage(), e);
        }
        return false;
    }

    public double getScoreThreshold(Integer evalType, Double scoreThreshold) {
        return getScoreThreshold(evalType, scoreThreshold, null);
    }

    public double getScoreThreshold(Integer evalType, Double scoreThreshold, MetricConfigPo metricConfigPo) {
        if (EvalMetricTypeEnum.EQUAL.getCode() == evalType || EvalMetricTypeEnum.CONTAIN.getCode() == evalType) {
            return 0.0;
        }
        if (scoreThreshold == null) {
            if (EvalMetricTypeEnum.SIM.getCode() == evalType || metricConfigPo == null) {
                return 0.5;
            }
            JSONObject jsonObject = JSON.parseObject(metricConfigPo.getRanges());
            return ((double) jsonObject.getInteger("startScore") + (double) jsonObject.getInteger("endScore")) / 2;
        }
        return scoreThreshold;
    }

    private void checkParam(EvalIdInfo evalIdInfo) {
        CommonUtils.checkEval(evalIdInfo != null && evalIdInfo.getEvalTaskId() != null, "参数异常");
    }

    public List<Integer> getMetricList(EvalTaskRequest evalRequest) {
        List<Integer> metricList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(evalRequest.getQueryMetric())) {
            metricList.addAll(evalRequest.getQueryMetric());
        }
        if (CollectionUtils.isNotEmpty(evalRequest.getSessionMetric())) {
            metricList.addAll(evalRequest.getSessionMetric());
        }
        return metricList;
    }

    public void updateTaskResultStatusAndScore(Long evalTaskId, AutoTaskStatusEnum taskStatusEnum, Double score) {
        // 强制走主库，保证一定能查到任务信息
        ZebraForceMasterHelper.forceMasterInLocalContext();
        EvalTaskPo evalTaskPo = evalTaskGeneratorService.getById(evalTaskId);
        ZebraForceMasterHelper.clearLocalContext();
        if (AutoTaskStatusEnum.EVALUATING.equals(taskStatusEnum)) {
            // 执行中任务要设置缓存标识
            setRunningCache(evalTaskId);
        }
        // 已完成的任务检测是否有质检人，如果有质检人则跳转到待质检状态
        if (AutoTaskStatusEnum.FINISHED.equals(taskStatusEnum) && StringUtils.isNotBlank(evalTaskPo.getInspectors())) {
            taskStatusEnum = AutoTaskStatusEnum.TO_BE_INSPECTED;
        }
        if (taskStatusEnum != null) {
            evalTaskPo.setStatus(taskStatusEnum.getCode());
        }
        if (score != null) {
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("avgScore", String.valueOf(score));
            evalTaskPo.setAutoResult(JSON.toJSONString(resultMap));
        }
        evalTaskPo.setGmtModified(new Date());
        evalTaskGeneratorService.updateById(evalTaskPo);
    }

    protected void createMarkTask(EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo) {
        ManualMarkTaskPo manualMarkTaskPo = new ManualMarkTaskPo();
        manualMarkTaskPo.setName(evalRequest.getName() + "-人工标注");
        manualMarkTaskPo.setDataSourceId(evalIdInfo.getEvalTaskId());
        if (Objects.nonNull(evalRequest.getTaskType()) && evalRequest.getTaskType().equals(TaskTypeEnum.TRAIN_MANUAL_ANNOTATION.getCode())) {
            manualMarkTaskPo.setType(MarkTaskTypeEnum.TRAIN_MARK.getCode());
        } else {
            manualMarkTaskPo.setType(MarkTaskTypeEnum.METRIC_MARK.getCode());
        }
        // TODO: 2024/4/19 目前只有session维度标注
        manualMarkTaskPo.setDimension(DimensionTypeEnum.SESSION.getCode());
        manualMarkTaskPo.setStatus(MarkTaskStatusEnum.UNMARKED.getCode());
        manualMarkTaskPo.setCreateMis(evalRequest.getCreatorMis());
        manualMarkTaskPo.setCreateName(evalRequest.getCreatorName());
        manualMarkTaskPo.setGmtCreated(evalIdInfo.getDate());
        manualMarkTaskPo.setGmtModified(evalIdInfo.getDate());
        manualMarkTaskGeneratorService.save(manualMarkTaskPo);
    }

    public ApplicationConfigPo getApplication(String application) {
        Long applicationId = Long.parseLong(application);
        return applicationConfigGeneratorService.getById(applicationId);
    }


    public void updateQueryDetailEvalResult(EvalTaskQueryDetailPo evalTaskQueryDetail, GptReplyDTO gptReply, String result, Integer status) {
        try {
            if (gptReply != null) {
                evalTaskQueryDetail.setModelOutput(StringUtils.isNotBlank(gptReply.getCode()) && GPT_SUCCESS_CODE.equals(gptReply.getCode()) ? gptReply.getAnswer() : gptReply.getMessage());
                evalTaskQueryDetail.setExecuteTime(gptReply.getExecuteTime() == null ? null : gptReply.getExecuteTime().intValue());
                evalTaskQueryDetail.setCostToken(gptReply.getTokenParam() == null ? null : JSON.toJSONString(gptReply.getTokenParam()));
            }
            if (StringUtils.isNotBlank(result)) {
                evalTaskQueryDetail.setMetricResult(result);
            }
            if (status != null) {
                evalTaskQueryDetail.setStatus(status);
            }
            evalTaskQueryDetail.setGmtModified(new Date());

            if (evalTaskQueryDetail.getId() != null) {
                evalTaskQueryDetailGeneratorService.updateById(evalTaskQueryDetail);
            } else {
                evalTaskQueryDetailGeneratorService.save(evalTaskQueryDetail);
            }
        } catch (Exception e) {
            Cat.logError(new EvalSqlException(e));
            log.error("写入数据库失败,evalTaskQueryDetail={},gptReply={},result={},msg={}", JSON.toJSONString(evalTaskQueryDetail), gptReply, result, e.getMessage(), e);
        }
    }


    protected void updateQueryStatus(Long queryId, TaskQueryStatusEnum queryStatusEnum) {
        try {
            if (queryStatusEnum == null) {
                return;
            }
            // 更新query状态
            EvalTaskQueryPo evalTaskQuery = new EvalTaskQueryPo();
            evalTaskQuery.setId(queryId);
            evalTaskQuery.setGmtModified(new Date());
            evalTaskQuery.setStatus(queryStatusEnum.getCode());
            evalTaskQueryGeneratorService.updateById(evalTaskQuery);
        } catch (Exception e) {
            Cat.logError(new EvalSqlException(e));
            log.error("写入数据库失败,queryId={},queryStatus={},msg={}", queryId, queryStatusEnum.getName(), e.getMessage(), e);
        }
    }

    protected void sendElephant(EvalIdInfo evalIdInfo, EvalTaskRequest evalRequest, Double score) {
        try {
            String pageUrl;
            String iframe = buildIframe(evalRequest, evalIdInfo);
            if (StringUtils.isBlank(evalRequest.getApplicationId())) {
                // 空间维度任务
                pageUrl = String.format(Lion.getString(ConfigUtil.getAppkey(), LionConstants.AIDA_EVAL_WORKSPACE_URL, "https://aida.sankuai.com/eval/task_auto_detail?iframeParams=%s"), iframe);
            } else {
                // 应用维度任务
                pageUrl = String.format(Lion.getString(ConfigUtil.getAppkey(), LionConstants.AIDA_EVAL_APP_URL, "https://aida.sankuai.com/application/%s/evaluating/task_auto_detail?iframeParams=%s"), evalRequest.getApplicationId(), iframe);
            }
            String dateStr = "";
            if (evalIdInfo.getDate() != null) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                dateStr = simpleDateFormat.format(evalIdInfo.getDate());
            }
            String elephantMessage;
            if (score == null) {
                String message = "您于%s提交的【%s】AI搭评测任务，已完成评测，[点击查看详情|%s]";
                elephantMessage = String.format(message, dateStr, evalRequest.getName(), pageUrl);
            } else {
                String message = "您于%s提交的【%s】AI搭评测任务，已完成评测，结果为%s，[点击查看详情|%s]";
                elephantMessage = String.format(message, dateStr, evalRequest.getName(), score, pageUrl);
            }
            dxPushTextService.pushTextByMisName(elephantMessage, evalRequest.getCreatorMis());
        } catch (Exception e) {
            log.error("ai搭评测结果推送大象消息失败,msg={}", e.getMessage(), e);
        }
    }

    private String buildIframe(EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo) throws UnsupportedEncodingException {
        Map<String, Object> iframeParams = new LinkedHashMap<>();
        iframeParams.put("detailId", evalIdInfo.getEvalTaskId());
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("name", evalRequest.getName());
        map.put("status", AutoTaskStatusEnum.FINISHED.getCode());
        iframeParams.put("query", map);
        return URLEncoder.encode(JSON.toJSONString(iframeParams), "UTF-8");
    }

    private MetricReplyDTO doExecuteMetric(EvalTaskQueryDetailPo modelDetail, MetricRequestParam metricRequestParam, String gptReply, Integer metricType) {
        EvalMetricTypeEnum evalMetricTypeEnum = EvalMetricTypeEnum.getByCode(metricType);
        MetricStrategyService metricStrategyService;
        if (evalMetricTypeEnum == null || !metricStrategyServiceMap.containsKey(evalMetricTypeEnum.getName())) {
            metricStrategyService = metricStrategyServiceMap.get(CommonConstants.CUSTOM_METRIC_NAME);
        } else {
            metricStrategyService = metricStrategyServiceMap.get(evalMetricTypeEnum.getName());
        }
        return metricStrategyService.executeMetric(modelDetail, metricRequestParam, gptReply, metricType);
    }

    public List<EvalTaskPo> getQueueTask(String createMis) {
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            List<EvalTaskPo> evalTaskPoList = evalTaskGeneratorService.getQueueTask(createMis);
            ZebraForceMasterHelper.clearLocalContext();
            return evalTaskPoList;
        } catch (Exception e) {
            Cat.logError(new EvalSqlException(e));
            log.error("获取队列信息失败,createMis={},msg={}", createMis, e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public GptReplyDTO getGptResult(GptRequestDTO gptRequest, Boolean isStreaming) {
        GptReplyDTO gptReply = new GptReplyDTO();
        Stopwatch stopwatch = Stopwatch.createStarted();
        // 请求获取ai搭输出
        AidaRequest aidaRequest = buildAidaRequest(gptRequest);
        AidaFinalRes aidaGptReply;
        if (isStreaming) {
            aidaGptReply = aidaInvokeServiceProxy.getAidaHttpResult(aidaRequest);
        } else {
            aidaGptReply = aidaInvokeServiceProxy.getAidaResult(aidaRequest);
        }
        stopwatch.stop();
        handleAidaResult(aidaRequest, aidaGptReply, gptReply);
        gptReply.setExecuteTime(stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return gptReply;
    }

    private AidaRequest buildAidaRequest(GptRequestDTO gptRequest) {
        AidaRequest aidaRequest = new AidaRequest();
        aidaRequest.setUser(gptRequest.getUser());
        aidaRequest.setApiSecretKey(StringUtils.isNotBlank(gptRequest.getApiSecretKey()) ? gptRequest.getApiSecretKey() : aidaInvokeServiceProxy.getApiSecretKey(gptRequest.getAidaAppId()));
        aidaRequest.setAppId(gptRequest.getAidaAppId());
        aidaRequest.setModelConfigVersionId(gptRequest.getModelConfigVersionId());
        aidaRequest.setInputContent(gptRequest.getInputContent());
        aidaRequest.setConversationId(gptRequest.getConversionId());
        aidaRequest.setParams(gptRequest.getDataParams());
        aidaRequest.setBusinessParam(gptRequest.getBusinessParam());
        aidaRequest.setMessageList(gptRequest.getMessageList());
        aidaRequest.setNodeId(gptRequest.getNodeId());
        aidaRequest.setNodeAppModelVersionId(gptRequest.getNodeAppModelVersionId());
        aidaRequest.setNodeApiToken(gptRequest.getNodeApiToken());
        aidaRequest.setFridayKey(aidaInvokeServiceProxy.getAidaFridayKey(gptRequest.getIsInner(), gptRequest.getAidaAppId()));
        return aidaRequest;
    }

    public void handleAidaResult(AidaRequest aidaRequest, AidaFinalRes aidaGptReply, GptReplyDTO gptReply) {
        if (aidaGptReply == null) {
            gptReply.setMessage("调用ai搭接口异常");
            return;
        }
        gptReply.setCode(aidaGptReply.getCode());
        if (!GPT_SUCCESS_CODE.equals(aidaGptReply.getCode())) {
            gptReply.setMessage(aidaGptReply.getMessage());
            return;
        }
        gptReply.setMessage(aidaGptReply.getMessage());
        gptReply.setAnswer(aidaGptReply.getAnswer());
        AidaFinalRes.Usage usage = aidaGptReply.getUsage();
        TokenParam tokenParam = new TokenParam();
        if (usage != null) {
            tokenParam.setInputTokenNum(usage.getPrompt_tokens());
            tokenParam.setOutputTokenNum(usage.getCompletion_tokens());
            tokenParam.setTotalTokenNum(usage.getTotal_tokens());
        }
        gptReply.setTokenParam(tokenParam);
        gptReply.setContent(buildAidaContext(aidaRequest, aidaGptReply));
    }

    private String buildAidaContext(AidaRequest aidaRequest, AidaFinalRes aidaGptReply) {
        GptContextParam param = new GptContextParam();
        InnerAppConfigDTO appConfigDTO = aidaInvokeServiceProxy.getAidaRobotInfo(aidaRequest.getAppId(), aidaRequest.getModelConfigVersionId());
        if (appConfigDTO != null) {
            param.setPrompt(appConfigDTO.getPrompt());
            ModelConfigRequestParam modelConfig = new ModelConfigRequestParam();
            modelConfig.setModelName(appConfigDTO.getModelName());
            if (appConfigDTO.getModelParamConfig() != null) {
                modelConfig.setModelParam(buildModelParam(appConfigDTO.getModelParamConfig()));
            }
            param.setModelConfig(modelConfig);
        }
        // 读取Lion配置决定调用新接口（包含appId参数）还是原有接口
        InnerAidaHistoryDTO aidaHistoryDTO;
        boolean enableNewApiWithAppId = LargeTableManagementUtil.enableNewApiWithAppId(aidaRequest.getAppId());
        if (enableNewApiWithAppId) {
            aidaHistoryDTO = aidaInvokeServiceProxy.getAidaHistory(aidaGptReply.getId(), aidaGptReply.getConversation_id(), aidaRequest.getAppId());
        } else {
            aidaHistoryDTO = aidaInvokeServiceProxy.getAidaHistory(aidaGptReply.getId(), aidaGptReply.getConversation_id());
        }
        if (aidaHistoryDTO != null) {
            param.setContext(aidaHistoryDTO.getRealPrompt());
        }
        param.setOutput(aidaGptReply.getAnswer());
        return JSON.toJSONString(param);
    }

    public Map<String, Object> buildModelParam(InnerAppConfigDTO.ModelParamConfig modelParamConfig) {
        Map<String, Object> modelParam = new HashMap<>();
        if (modelParamConfig.getTopP() != null) {
            modelParam.put("top_p", modelParamConfig.getTopP());
        }
        if (modelParamConfig.getTemperature() != null) {
            modelParam.put("temperature", modelParamConfig.getTemperature());
        }
        if (modelParamConfig.getRepetitionPenalty() != null) {
            modelParam.put("repetition_penalty", modelParamConfig.getRepetitionPenalty());
        }
        if (modelParamConfig.getMaxTokens() != null) {
            modelParam.put("max_tokens", modelParamConfig.getMaxTokens());
        }
        if (modelParamConfig.getContextMaxTokens() != null) {
            modelParam.put("context_max_tokens", modelParamConfig.getContextMaxTokens());
        }
        if (modelParamConfig.getPresencePenalty() != null) {
            modelParam.put("presence_penalty", modelParamConfig.getPresencePenalty());
        }
        if (modelParamConfig.getFrequencyPenalty() != null) {
            modelParam.put("frequency_penalty", modelParamConfig.getFrequencyPenalty());
        }
        if (modelParamConfig.getTopK() != null) {
            modelParam.put("top_k", modelParamConfig.getTopK());
        }
        return modelParam;
    }

    public Double getAvgEvalScore(List<ScoreThresholdParam> scoreThresholdMap, Integer metric) {
        if (CollectionUtils.isEmpty(scoreThresholdMap)) {
            return null;
        }
        for (ScoreThresholdParam scoreThresholdParam : scoreThresholdMap) {
            if (scoreThresholdParam.getMetric().equals(metric)) {
                return scoreThresholdParam.getScoreThreshold();
            }
        }
        return null;
    }

    public ScoreThresholdParam getScoreThresholdParam(List<ScoreThresholdParam> scoreThresholdMap, Integer metric) {
        if (CollectionUtils.isEmpty(scoreThresholdMap)) {
            return null;
        }
        for (ScoreThresholdParam scoreThresholdParam : scoreThresholdMap) {
            if (scoreThresholdParam.getMetric().equals(metric)) {
                return scoreThresholdParam;
            }
        }
        return null;
    }

    /**
     * 获取机器人信息MAP，key=applicatConfigID，value=robotId（AI搭）
     *
     * @param evalRequest
     * @return
     */
    protected Map<String, String> getRobotMap(EvalTaskRequest evalRequest) {
        if (CallTypeEnum.ONLINE.getCode() == evalRequest.getCallType()) {
            return evalRequest.getApplicationModelMap();
        }
        Map<String, String> result = new HashMap<>();
        for (String applicationId : evalRequest.getApplicationConfig()) {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            ApplicationConfigPo applicationConfig = applicationConfigGeneratorService.getById(Long.parseLong(applicationId));
            ZebraForceMasterHelper.clearLocalContext();
            result.put(applicationId, applicationConfig.getRobotId());
        }
        return result;
    }

    public String getModelName(String modelConfig) {
        if (StringUtils.isNotBlank(modelConfig)) {
            ModelConfigRequestParam modelConfigRequestParam = JSON.parseObject(modelConfig, ModelConfigRequestParam.class);
            return modelConfigRequestParam.getModelName();
        }
        return null;
    }

    public String getModelName(ApplicationConfigPo applicationConfig) {
        if (applicationConfig != null) {
            if (applicationConfig.getSource() == null || ApplicationSourceEnum.AIDA_SYSTEM.getCode() == applicationConfig.getSource() || ApplicationSourceEnum.VIRTUAL.getCode() == applicationConfig.getSource()) {
                String modelName = getModelName(applicationConfig.getModelConfig());
                return StringUtils.isNotBlank(modelName) ? modelName : "未知模型";
            }
            return "PB话术推荐模型";
        }
        return "未知模型";
    }

    public MetricReplyDTO executeMetric(EvalTaskQueryDetailPo modelDetail, String gptAnswer, MetricRequestParam metricRequestParam) {
        MetricReplyDTO metricReplyDTO = new MetricReplyDTO();
        // 人工指标不执行评测
        if (metricRequestParam.getMetric().getEvalType() != null && metricRequestParam.getMetric().getEvalType() == MetricEvalTypeEnum.MANUAL.getCode()) {
            metricReplyDTO.setIsEnd(false);
            return metricReplyDTO;
        }
        try {
            // 获取评测结果
            metricReplyDTO = doExecuteMetric(modelDetail, metricRequestParam, gptAnswer, metricRequestParam.getMetric().getId().intValue());
            metricReplyDTO.setIsSuccess(!StringUtils.isBlank(metricReplyDTO.getResult()));
            return metricReplyDTO;
        } catch (Exception e) {
            Cat.logError(e);
            log.error("计算指标失败,evalRequestParam={},msg={}", JSON.toJSONString(metricRequestParam), e.getMessage(), e);
            metricReplyDTO.setMessage(e.getMessage());
            metricReplyDTO.setIsSuccess(false);
            return metricReplyDTO;
        } finally {
            // 记录日志信息
            if (metricRequestParam.getLogParam() != null) {
                logJudgeApplication(metricReplyDTO, metricRequestParam.getLogParam());
            }
        }
    }

    /**
     * 解析结构化出参
     *
     * @param gptOutput gpt原始输出
     * @return 解析后的结构化输出
     */
    private String parseOutput(String applicationParam, String gptOutput) {
        if (StringUtils.isBlank(applicationParam) || StringUtils.isBlank(gptOutput)) {
            return gptOutput;
        }

        JSONObject jsonObject = DataConvertUtil.tryConvertJson(gptOutput);
        return (jsonObject != null && jsonObject.containsKey(applicationParam)) ? jsonObject.getString(applicationParam) : gptOutput;
    }

    /**
     * 记录日志信息
     */
    private void logJudgeApplication(MetricReplyDTO metricReplyDTO, CommonLogParam logParam) {
        try {
            EvalTaskLogPo taskLog = new EvalTaskLogPo();
            taskLog.setQueryDetailId(logParam.getQueryDetailId());
            taskLog.setLogType(logParam.getLogType());
            taskLog.setMetricId(logParam.getMetricId());
            taskLog.setTaskId(logParam.getTaskId());
            taskLog.setDatasetId(logParam.getDatasetId());
            taskLog.setSessionId(logParam.getSessionId());
            taskLog.setQueryId(logParam.getQueryId());
            taskLog.setTestApplication(logParam.getTestApplication());
            taskLog.setIsSuccess(StringUtils.isNotBlank(metricReplyDTO.getResult()) ? ApplicationResultEnum.SUCCESS.getCode() : ApplicationResultEnum.FAILED.getCode());
            taskLog.setCostTime(metricReplyDTO.getExecuteTime() == null ? null : metricReplyDTO.getExecuteTime().intValue());
            taskLog.setCostToken(metricReplyDTO.getTokenParam() == null ? null : JSON.toJSONString(metricReplyDTO.getTokenParam()));
            taskLog.setContent(metricReplyDTO.getContent());
            Date date = new Date();
            taskLog.setGmtModified(date);
            taskLog.setGmtCreated(date);
            evalTaskLogGeneratorService.save(taskLog);
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
            log.error("记录评测应用日志失败,metricReplyDTO={},evalRequestParam={},msg={}", JSON.toJSONString(metricReplyDTO), JSON.toJSONString(logParam), e.getMessage(), e);
        }
    }

    protected EvalResult executeEval(EvalRequestParam evalRequestParam) {
        EvalResult evalResult = new EvalResult();
        // 设置标识表示任务正在执行，用作重启后机器判定
        setRunningCache(evalRequestParam.getEvalTaskId());
        EvalResultEnum evalResultStatus = null;
        boolean isEnd = true;
        evalRequestParam.setLogType(TaskLogTypeEnum.TEST_APPLICATION.getCode());
        ApplicationRequestParam applicationRequestParam = buildApplicationRequestParam(evalRequestParam);
        GptReplyDTO gptReply = getGptOutput(applicationRequestParam);
        log.info("mis:{},taskId{},调用aida返回gptReply:{},applicationRequestParam:{}", evalRequestParam.getCreatorMis(), evalRequestParam.getEvalTaskId(), JSON.toJSONString(gptReply), JSON.toJSONString(applicationRequestParam));
        // query状态更新为评测
        updateQueryStatus(evalRequestParam.getQueryId(), TaskQueryStatusEnum.EVALUATING);
        for (EvalTaskQueryDetailPo modelDetail : evalRequestParam.getQueryDetailList()) {
            // 解析模型输出字段的值
            GptReplyDTO copyGptReplyDTO = parseModelDetail(modelDetail, gptReply);
            Integer metricId = modelDetail.getMetricId();
            // 获取模型输出失败
            if (CommonEvalStrategyService.GPT_INTERRUPT_CODE.equals(copyGptReplyDTO.getCode())) {
                // 写入数据库，当前操作被打断
                updateQueryDetailEvalResult(modelDetail, copyGptReplyDTO, null, AutoTaskEvalStatusEnum.INTERRUPT.getCode());
                evalResultStatus = EvalResultEnum.INTERRUPT;
            } else if (Objects.isNull(copyGptReplyDTO.getAnswer())) {
                evalResultStatus = CallTypeEnum.OFFLINE.getCode() == evalRequestParam.getCallType() ? EvalResultEnum.OUTPUT_FAIL : EvalResultEnum.EVAL_FAIL;
                // 写入数据库，执行指标失败
                updateQueryDetailEvalResult(modelDetail, copyGptReplyDTO, null, AutoTaskEvalStatusEnum.FAILED.getCode());
            } else {
                // 模型输出写入数据库
                updateQueryDetailEvalResult(modelDetail, copyGptReplyDTO, null, null);
                MetricConfigPo metricConfig = metricConfigGeneratorService.getById(metricId);
                if (metricConfig.getDimension() == null || metricConfig.getDimension() == MetricDimensionEnum.QUERY.getCode()) {
                    evalResult.setGptReply(copyGptReplyDTO.getAnswer());
                    // 执行评测指标
                    MetricRequestParam metricRequestParam = buildMetricRequestParam(evalRequestParam, modelDetail, metricConfig);
                    String gptAnswer = parseOutput(evalRequestParam.getApplicationParam(), copyGptReplyDTO.getAnswer());
                    MetricReplyDTO result = executeMetric(modelDetail, gptAnswer, metricRequestParam);
                    // 更新指标结果信息
                    String answer = StringUtils.isBlank(result.getResult()) ? result.getMessage() : result.getResult();
                    if (result.getIsEnd() == null || result.getIsEnd()) {
                        updateQueryDetailEvalResult(modelDetail, null, answer, result.getIsSuccess() ? AutoTaskEvalStatusEnum.EVALUATED.getCode() : AutoTaskEvalStatusEnum.FAILED.getCode());
                    }
                    if (evalRequestParam.getTaskType() != null && TaskTypeEnum.ANNOTATION.getCode() == evalRequestParam.getTaskType()) {
                        updateAnnotationResult(evalRequestParam.getSampleData(), result, metricConfig);
                    }
                    if (evalResultStatus == null || EvalResultEnum.SUCCESS.equals(evalResultStatus)) {
                        evalResultStatus = StringUtils.isNotBlank(result.getResult()) ? EvalResultEnum.SUCCESS : EvalResultEnum.EVAL_FAIL;
                    }
                    isEnd = (result.getIsEnd() == null || result.getIsEnd()) && isEnd;
                }
            }
        }
        evalResult.setEvalResult(evalResultStatus);
        evalResult.setIsEnd(isEnd);
        return evalResult;
    }

    /**
     * 构造应用请求参数
     */
    private ApplicationRequestParam buildApplicationRequestParam(EvalRequestParam evalRequestParam) {
        ApplicationRequestParam applicationRequestParam = new ApplicationRequestParam();
        applicationRequestParam.setInputContent(evalRequestParam.getInputContent());
        applicationRequestParam.setModel(evalRequestParam.getModel());
        applicationRequestParam.setEvalTaskId(evalRequestParam.getEvalTaskId());
        applicationRequestParam.setCallType(evalRequestParam.getCallType());
        applicationRequestParam.setAbility(evalRequestParam.getAbility());
        applicationRequestParam.setConversionInfo(evalRequestParam.getConversionInfo());
        applicationRequestParam.setRobotId(evalRequestParam.getRobotId());
        applicationRequestParam.setCreatorMis(evalRequestParam.getCreatorMis());
        applicationRequestParam.setApiSecretKey(evalRequestParam.getApiSecretKey());
        applicationRequestParam.setQueryDetailList(evalRequestParam.getQueryDetailList());
        applicationRequestParam.setAidaAppId(evalRequestParam.getAidaAppId());
        applicationRequestParam.setSessionId(evalRequestParam.getSessionId());
        applicationRequestParam.setParams(evalRequestParam.getParams());
        applicationRequestParam.setBusinessParam(evalRequestParam.getBusinessParam());
        applicationRequestParam.setDatasetId(evalRequestParam.getDatasetId());
        applicationRequestParam.setQueryId(evalRequestParam.getQueryId());
        applicationRequestParam.setTestApplication(evalRequestParam.getTestApplication());
        applicationRequestParam.setMessageList(evalRequestParam.getMessageList());
        applicationRequestParam.setIsInner(evalRequestParam.getIsInner());
        applicationRequestParam.setLogType(evalRequestParam.getLogType());
        applicationRequestParam.setConversationId(evalRequestParam.getConversationId());
        applicationRequestParam.setNodeId(evalRequestParam.getNodeId());
        applicationRequestParam.setNodeAppModelVersionId(evalRequestParam.getNodeAppModelVersionId());
        applicationRequestParam.setNodeApiToken(evalRequestParam.getNodeApiToken());
        applicationRequestParam.setAppOpenStreaming(evalRequestParam.getAppOpenStreaming());
        return applicationRequestParam;
    }

    /**
     * 构造指标请求参数
     */
    private MetricRequestParam buildMetricRequestParam(EvalRequestParam evalRequestParam, EvalTaskQueryDetailPo modelDetail, MetricConfigPo metricConfig) {
        MetricRequestParam metricRequestParam = new MetricRequestParam();
        metricRequestParam.setInputContent(evalRequestParam.getInputContent());
        metricRequestParam.setParams(evalRequestParam.getParams());
        metricRequestParam.setBusinessParam(evalRequestParam.getBusinessParam());
        metricRequestParam.setMetric(metricConfig);
        metricRequestParam.setTestApplication(evalRequestParam.getTestApplication());
        metricRequestParam.setMessageList(evalRequestParam.getMessageList());
        metricRequestParam.setIsInner(evalRequestParam.getIsInner());
        CommonLogParam param = new CommonLogParam();
        param.setLogType(TaskLogTypeEnum.QUERY_METRIC.getCode());
        param.setQueryDetailId(modelDetail == null ? null : modelDetail.getId());
        param.setMetricId(metricConfig.getId());
        param.setTaskId(evalRequestParam.getEvalTaskId());
        param.setDatasetId(evalRequestParam.getDatasetId());
        param.setSessionId(evalRequestParam.getSessionId());
        param.setQueryId(evalRequestParam.getQueryId());
        param.setTestApplication(evalRequestParam.getTestApplication());
        metricRequestParam.setLogParam(param);
        return metricRequestParam;
    }

    private GptReplyDTO parseModelDetail(EvalTaskQueryDetailPo evalTaskQueryDetail, GptReplyDTO gptReply) {
        if (gptReply == null) {
            return null;
        }
        GptReplyDTO gptReplyDTO = new GptReplyDTO();
        BeanUtils.copyProperties(gptReply, gptReplyDTO);
        if (StringUtils.isNotBlank(gptReply.getAnswer())) {
            String outputKey = evalTaskQueryDetail.getOutputKey();
            if (StringUtils.isNotBlank(outputKey) && !CommonConstants.COMPLETE_OUTPUT.equals(outputKey)) {
                JSONObject jsonObject = DataConvertUtil.tryConvertJson(gptReply.getAnswer());
                if (jsonObject == null || !jsonObject.containsKey(outputKey)) {
                    gptReplyDTO.setAnswer(null);
                    gptReplyDTO.setMessage("模型输出中没有" + outputKey);
                } else {
                    gptReplyDTO.setAnswer(jsonObject.getString(outputKey));
                }
            }
        } else {
            if (StringUtils.isNotBlank(gptReply.getMessage())) {
                gptReplyDTO.setMessage(gptReply.getMessage());
            } else {
                gptReplyDTO.setMessage("大模型输出没有结果");
            }
        }
        return gptReplyDTO;
    }

    private void updateAnnotationResult(SampleData sampleData, MetricReplyDTO result, MetricConfigPo metricConfig) {
        EvalDatasetDetailPo detailPo = evalDatasetDetailGeneratorService.getById(sampleData.getDetailId());
        // 将ranges中与result中对应的内容加入到content中
        Map<String, String> content = updateContentWithAnnotations(detailPo, metricConfig, result);
        detailPo.setContent(JSONObject.toJSONString(content));
        evalDatasetDetailGeneratorService.updateById(detailPo);
    }

    public GptReplyDTO getGptOutput(ApplicationRequestParam applicationRequestParam) {
        GptReplyDTO gptReply = new GptReplyDTO();
        try {
            if (CallTypeEnum.OFFLINE.getCode() == applicationRequestParam.getCallType()) {
                if (AbilityEnum.MULTI_ROUND.getName().equals(applicationRequestParam.getAbility())
                        && StringUtils.isBlank(applicationRequestParam.getConversionInfo().getConversationId())) {
                    gptReply.setMessage(StringUtils.isNotBlank(applicationRequestParam.getConversionInfo().getMessage()) ? applicationRequestParam.getConversionInfo().getMessage() : "生成ai搭会话失败");
                    return gptReply;
                }
                ApplicationConfigPo applicationConfig = getApplication(applicationRequestParam.getTestApplication());
                if (ApplicationSourceEnum.PB_SYSTEM.getCode() == applicationConfig.getSource()) {
                    PbRequest pbRequest = buildPbRequest(applicationRequestParam, applicationConfig);
                    // 保存session和conversation的映射，方便回调推送
                    saveSessionCache(applicationRequestParam);
                    gptReply = getPbResult(pbRequest);
                } else {
                    List<String> glmModelList = Lion.getList(ConfigUtil.getAppkey(), "aida.python.model.list", String.class);
                    if (StringUtils.isNotBlank(applicationRequestParam.getModel()) && CollectionUtils.isNotEmpty(glmModelList) && glmModelList.contains(applicationRequestParam.getModel())) {
                        Long applicationId = Long.parseLong(applicationRequestParam.getRobotId());
                        ApplicationConfigPo applicationConfigPo = applicationConfigGeneratorService.getById(applicationId);
                        String prompt = applicationConfigPo == null ? null : applicationConfigPo.getPrompt();
                        String realPrompt = replaceParam(prompt, applicationRequestParam.getParams());
                        GptRequest gptRequest = new GptRequest();
                        gptRequest.setModelName(applicationRequestParam.getModel());
                        gptRequest.setPrompt(realPrompt);
                        gptRequest.setSessionId(applicationRequestParam.getConversionInfo().getConversationId());
                        gptRequest.setInputContent(applicationRequestParam.getInputContent());
                        gptRequest.setMessageList(applicationRequestParam.getMessageList());
                        GptResponse response = gptRequestServiceProxy.invokeGlm(gptRequest);
                        gptReply = handleResponse(response, applicationConfigPo);
                    } else {
                        GptRequestDTO gptRequest = buildGptRequest(applicationRequestParam);
                        gptReply = getGptResult(gptRequest, applicationRequestParam.getAppOpenStreaming());
                    }
                }
                if (!CommonEvalStrategyService.GPT_SUCCESS_CODE.equals(gptReply.getCode()) || gptReply.getAnswer() == null) {
                    log.warn("未获取到模型输出,gptRequestParam={}", JSON.toJSONString(applicationRequestParam));
                    if (StringUtils.isBlank(gptReply.getMessage())) {
                        gptReply.setMessage("未获取到模型输出");
                    }
                }
            } else {
                gptReply = new GptReplyDTO();
                gptReply.setAnswer(applicationRequestParam.getQueryDetailList().get(0).getModelOutput());
            }
            return gptReply;
        } catch (Exception e) {
            gptReply.setMessage(e.getMessage());
            Cat.logError(e);
            log.error("获取模型输出失败,evalRequestParam={},msg={}", JSON.toJSONString(applicationRequestParam), e.getMessage(), e);
        } finally {
            // 记录评测应用日志
            if (TaskLogTypeEnum.ROBOT_MOCK.getCode() == applicationRequestParam.getLogType()) {
                logTestApplication(gptReply, applicationRequestParam, null);
            } else {
                for (EvalTaskQueryDetailPo queryDetail : applicationRequestParam.getQueryDetailList()) {
                    logTestApplication(gptReply, applicationRequestParam, queryDetail.getId());
                }
            }
        }
        return gptReply;
    }

    private JSONObject parseResultToJson(MetricReplyDTO result) {
        if (result == null || null == result.getIsSuccess() || !result.getIsSuccess()) {
            return null;
        }

        String answer = result.getResult();
        if (StringUtils.isBlank(answer)) {
            return null;
        }

        return DataConvertUtil.tryConvertJson(answer);
    }

    private String getErrorMessage(MetricReplyDTO result, String defaultMessage) {
        if (result != null && StringUtils.isNotBlank(result.getMessage())) {
            return result.getMessage();
        }
        return defaultMessage;
    }

    private Map<String, String> updateContentWithAnnotations(EvalDatasetDetailPo detailPo, MetricConfigPo metricConfig, MetricReplyDTO result) {
        Map<String, String> content = JSONObject.parseObject(detailPo.getContent(), new TypeReference<LinkedHashMap<String, String>>() {
        });
        JSONObject resultMap = parseResultToJson(result);
        String errorMsg = getErrorMessage(result, "大模型输出结果不是json或为空");

        JSONObject ranges = JSON.parseObject(metricConfig.getRanges());
        if (ranges.containsKey("annotationList")) {
            List<String> annotationList = ranges.getJSONArray("annotationList").toJavaList(String.class);

            for (String annotation : annotationList) {
                String key = metricConfig.getName() + "-" + annotation;
                String value = "";
                if (MapUtils.isNotEmpty(resultMap) && resultMap.containsKey(annotation)) {
                    value = StringUtils.isBlank(resultMap.getString(annotation)) ? "错误:大模型输出为空" : resultMap.getString(annotation);
                } else {
                    value = "错误:" + errorMsg;
                }
                content.put(key, value);
            }
        }
        return content;
    }

    private void saveSessionCache(ApplicationRequestParam applicationRequestParam) {
        try {
            if (applicationRequestParam.getConversationId() == null) {
                return;
            }
            SessionInfoParam sessionInfo = new SessionInfoParam();
            sessionInfo.setToken(PikeThreadLocalHelper.getToken());
            sessionInfo.setConversationId(applicationRequestParam.getConversationId());
            redisClientProxy.set(new StoreKey(RedisConstants.SESSION_INFO, applicationRequestParam.getConversionInfo().getConversationId()), JSON.toJSONString(sessionInfo));
        } catch (Exception e) {
            log.error("saveSessionCache error,evalRequestParam={},msg={}", JSONObject.toJSONString(applicationRequestParam), e.getMessage(), e);
            Cat.logError(new EvalException(e));
        }
    }

    private PbRequest buildPbRequest(ApplicationRequestParam applicationRequestParam, ApplicationConfigPo applicationConfig) {
        PbRequest pbRequest = new PbRequest();
        pbRequest.setSceneId(applicationConfig.getRobotId());
        pbRequest.setMessageList(applicationRequestParam.getMessageList());
        pbRequest.setConversationId(applicationRequestParam.getConversionInfo().getConversationId());
        // 过滤外呼槽位，mock线上外呼场景
        pbRequest.setExtraSlotMap(filterCallOut(applicationConfig.getRobotId(), applicationRequestParam.getConversionInfo().getConversationId(), applicationRequestParam.getParams()));
        pbRequest.setApplicationExtra(applicationConfig.getExtra());
        return pbRequest;
    }

    private Map<String, String> filterCallOut(String sceneId, String sessionId, Map<String, String> params) {
        // 获取外呼信号缓存，如果不为空，不需要过滤外呼槽位
        String callOutSignal = redisClientProxy.get(new StoreKey(RedisConstants.CALL_OUT_MOCK_SIGNAL, sessionId));
        if (StringUtils.isNotBlank(callOutSignal)) {
            return params;
        }
        if (MapUtils.isEmpty(params)) {
            return params;
        }
        Map<String, String> callOutSlotMap = Lion.getMap(ConfigUtil.getAppkey(), LionConstants.CALL_OUT_SLOT_MAP, String.class);
        if (MapUtils.isEmpty(callOutSlotMap)) {
            return params;
        }
        String callOutSlot = callOutSlotMap.get(sceneId);
        if (StringUtils.isBlank(callOutSlot)) {
            return params;
        }
        params.remove(callOutSlot);
        return params;
    }

    private GptReplyDTO getPbResult(PbRequest pbRequest) {
        GptReplyDTO gptReply = new GptReplyDTO();
        Stopwatch stopwatch = Stopwatch.createStarted();
        PbResponse pbResponse = pbServiceProxy.executeDialogRecommendation(pbRequest);
        stopwatch.stop();
        gptReply.setExecuteTime(stopwatch.elapsed(TimeUnit.MILLISECONDS));
        if (pbResponse.getResponse() != null && pbResponse.getResponse().getDialogResultCodeEnum() == DialogResultCodeEnum.SUCCESS) {
            DialogResponse dialogResponse = pbResponse.getResponse();
            if (ReplyEndTypeEnum.INTERRUPT.getCode().equals(dialogResponse.getEndType())) {
                gptReply.setCode(CommonEvalStrategyService.GPT_INTERRUPT_CODE);
            } else if (ReplyEndTypeEnum.FUSE_END.getCode().equals(dialogResponse.getEndType())) {
                gptReply.setCode(CommonEvalStrategyService.GPT_SUCCESS_CODE);
                gptReply.setAnswer(pbResponse.getResponse().getEndMessage());
            } else {
                gptReply.setCode(CommonEvalStrategyService.GPT_SUCCESS_CODE);
                gptReply.setAnswer(pbResponse.getResponse().getDmResultList().get(0).getSolution());
            }
        } else {
            gptReply.setMessage(pbResponse.getMessage());
        }
        return gptReply;
    }

    private void logTestApplication(GptReplyDTO gptReply, ApplicationRequestParam applicationRequestParam, Long queryDetailId) {
        try {
            EvalTaskLogPo taskLog = new EvalTaskLogPo();
            taskLog.setLogType(applicationRequestParam.getLogType());
            taskLog.setTaskId(applicationRequestParam.getEvalTaskId());
            taskLog.setDatasetId(applicationRequestParam.getDatasetId());
            taskLog.setSessionId(applicationRequestParam.getSessionId());
            taskLog.setQueryId(applicationRequestParam.getQueryId());
            taskLog.setTestApplication(applicationRequestParam.getTestApplication());
            taskLog.setQueryDetailId(queryDetailId);
            taskLog.setIsSuccess(StringUtils.isNotBlank(gptReply.getAnswer()) ? ApplicationResultEnum.SUCCESS.getCode() : ApplicationResultEnum.FAILED.getCode());
            Date date = new Date();
            taskLog.setGmtCreated(date);
            taskLog.setGmtModified(date);
            taskLog.setCostTime(gptReply.getExecuteTime() == null ? null : gptReply.getExecuteTime().intValue());
            taskLog.setCostToken(gptReply.getTokenParam() == null ? null : JSON.toJSONString(gptReply.getTokenParam()));
            taskLog.setContent(gptReply.getContent());
            evalTaskLogGeneratorService.save(taskLog);
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
            log.error("记录评测应用日志失败,gptReply={},evalRequestParam={},msg={}", JSON.toJSONString(gptReply), JSON.toJSONString(applicationRequestParam), e.getMessage(), e);
        }
    }

    private String replaceParam(String prompt, Map<String, String> replaceMap) {
        if (StringUtils.isNotBlank(prompt)) {
            if (MapUtils.isNotEmpty(replaceMap)) {
                prompt = PromptUtils.replacePromptContent(prompt, replaceMap);
            }
        }
        return prompt;
    }

    private GptReplyDTO handleResponse(GptResponse response, ApplicationConfigPo applicationConfigPo) {
        GptReplyDTO gptReply = new GptReplyDTO();
        if (response != null && response.getGptStatus() != null && ResultCodeEnum.SUCCESS.getCode() == response.getGptStatus() && StringUtils.isNotBlank(response.getGptReply())) {
            gptReply.setAnswer(response.getGptReply());
            gptReply.setCode(CommonEvalStrategyService.GPT_SUCCESS_CODE);
            gptReply.setExecuteTime(response.getCostTime());
            TokenParam tokenParam = new TokenParam();
            tokenParam.setTotalTokenNum(response.getTotalToken() == null ? null : response.getTotalToken().intValue());
            tokenParam.setInputTokenNum(response.getPromptToken() == null ? null : response.getPromptToken().intValue());
            tokenParam.setOutputTokenNum(response.getCompletionToken() == null ? null : response.getCompletionToken().intValue());
            gptReply.setTokenParam(tokenParam);
        } else {
            gptReply.setMessage(response == null ? "获取模型输出为空" : response.getGptMessage());
        }
        gptReply.setContent(buildGlmContext(response, applicationConfigPo));
        return gptReply;
    }

    public String buildGlmContext(GptResponse response, ApplicationConfigPo applicationConfigPo) {
        GptContextParam param = new GptContextParam();
        if (applicationConfigPo != null) {
            param.setPrompt(applicationConfigPo.getPrompt());
            if (StringUtils.isNotBlank(applicationConfigPo.getModelConfig())) {
                param.setModelConfig(JSON.parseObject(applicationConfigPo.getModelConfig(), ModelConfigRequestParam.class));
            }
        }
        if (response != null) {
            param.setOutput(response.getGptReply());
            param.setContext(response.getContext());
        }
        return JSON.toJSONString(param);
    }

    private GptRequestDTO buildGptRequest(ApplicationRequestParam applicationRequestParam) {
        GptRequestDTO gptRequest = new GptRequestDTO();
        gptRequest.setConversionId(applicationRequestParam.getConversionInfo() == null ? null : applicationRequestParam.getConversionInfo().getConversationId());
        gptRequest.setUser(applicationRequestParam.getCreatorMis());
        gptRequest.setAidaAppId(applicationRequestParam.getAidaAppId());
        gptRequest.setApiSecretKey(applicationRequestParam.getApiSecretKey());
        gptRequest.setModelConfigVersionId(applicationRequestParam.getRobotId());
        gptRequest.setInputContent(applicationRequestParam.getInputContent());
        gptRequest.setDataParams(applicationRequestParam.getParams());
        gptRequest.setBusinessParam(applicationRequestParam.getBusinessParam());
        gptRequest.setModel(applicationRequestParam.getRobotId());
        gptRequest.setIsInner(applicationRequestParam.getIsInner());
        gptRequest.setMessageList(applicationRequestParam.getMessageList());
        gptRequest.setNodeId(applicationRequestParam.getNodeId());
        gptRequest.setNodeAppModelVersionId(applicationRequestParam.getNodeAppModelVersionId());
        gptRequest.setNodeApiToken(applicationRequestParam.getNodeApiToken());
        return gptRequest;
    }

    private void setRunningCache(Long taskId) {
        try {
            Integer expireTime = Lion.getInt(ConfigUtil.getAppkey(), LionConstants.EVAL_RUNNING_CACHE_EXPIRE_TIME, 5 * 60);
            redisStoreClient.set(new StoreKey(RedisConstants.EVAL_RUNNING_TASK, taskId), true, expireTime);
        } catch (Exception e) {
            Cat.logError(e);
            log.error("设置任务执行缓存失败,taskId={},msg={}", taskId, e.getMessage(), e);
        }
    }

    public EvalIdInfo logData2Db(EvalTaskRequest evalRequest) {
        EvalIdInfo evalIdInfo = new EvalIdInfo();
        // 记录任务提交时间
        evalIdInfo.setDate(new Date());
        // 任务数据入库
        logTask(evalRequest, evalIdInfo);
        if (evalRequest.getInputSource() == TaskInputSourceEnum.DATASET.getCode()) {
            // query数据入库
            logTaskQuery(evalRequest, evalIdInfo);
            // 多轮会话session数据入库
            if (AbilityEnum.MULTI_ROUND.getName().equals(evalRequest.getAbility())) {
                logSession(evalRequest, evalIdInfo);
            }
            // 创建完成，将任务改为排队中
            updateTaskStatus(evalIdInfo.getEvalTaskId(), AutoTaskStatusEnum.QUEUING.getCode());
        } else {
            if (evalRequest.getInputSource() == TaskInputSourceEnum.MANUAL_MOCK.getCode()) {
                // 创建标注任务
                logMarkTask(evalRequest, evalIdInfo);
            }
            // 创建需要mock的会话信息
            logMockSession(evalRequest, evalIdInfo);
            if (evalRequest.getInputSource() == TaskInputSourceEnum.ROBOT_MOCK.getCode()) {
                // 创建完成，将任务改为排队中
                updateTaskStatus(evalIdInfo.getEvalTaskId(), AutoTaskStatusEnum.QUEUING.getCode());
            }
        }
        return evalIdInfo;
    }

    private void updateTaskStatus(Long taskId, Integer status) {
        EvalTaskPo task = new EvalTaskPo();
        task.setId(taskId);
        task.setStatus(status);
        evalTaskGeneratorService.updateById(task);
    }

    private void logMarkTask(EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo) {
        ManualMarkTaskPo manualMarkTaskPo = new ManualMarkTaskPo();
        manualMarkTaskPo.setDataSourceId(evalIdInfo.getEvalTaskId());
        manualMarkTaskPo.setName(evalRequest.getName() + "-人工模拟");
        manualMarkTaskPo.setType(MarkTaskTypeEnum.MANUAL_MOCK.getCode());
        manualMarkTaskPo.setDimension(DimensionTypeEnum.SESSION.getCode());
        manualMarkTaskPo.setStatus(MarkTaskStatusEnum.UNMARKED.getCode());
        manualMarkTaskPo.setCreateMis(evalRequest.getCreatorMis());
        manualMarkTaskPo.setCreateName(evalRequest.getCreatorName());
        manualMarkTaskPo.setGmtCreated(evalIdInfo.getDate());
        manualMarkTaskPo.setGmtModified(evalIdInfo.getDate());
        manualMarkTaskGeneratorService.save(manualMarkTaskPo);
    }

    private void logMockSession(EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo) {
        List<EvalTaskSessionPo> sessionList = new ArrayList<>();
        List<EvalTaskSessionPo> originSessionList = new ArrayList<>();
        // 第一级，按照datasetId分组
        Map<Long, List<SampleData>> datasetMap = evalRequest.getSampleDataList().stream().collect(Collectors.groupingBy(SampleData::getDatasetId, LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<Long, List<SampleData>> datasetEntry : datasetMap.entrySet()) {
            Long datasetId = datasetEntry.getKey();
            // 第二级，按照sessionId分组
            Map<String, List<SampleData>> sessionMap = datasetEntry.getValue().stream().collect(Collectors.groupingBy(SampleData::getSessionId, LinkedHashMap::new, Collectors.toList()));
            for (Map.Entry<String, List<SampleData>> sessionEntry : sessionMap.entrySet()) {
                String sessionId = sessionEntry.getKey();
                EvalTaskSessionPo evalTaskSessionPo = new EvalTaskSessionPo();
                evalTaskSessionPo.setTaskId(evalIdInfo.getEvalTaskId());
                evalTaskSessionPo.setDatasetId(datasetId);
                evalTaskSessionPo.setSessionId(sessionId);
                evalTaskSessionPo.setGmtCreated(evalIdInfo.getDate());
                evalTaskSessionPo.setGmtModified(evalIdInfo.getDate());
                evalTaskSessionPo.setReferenceData(buildContext(sessionEntry.getValue()));
                evalTaskSessionPo.setStatus(TaskSessionStatusEnum.EVALUATING.getCode());
                originSessionList.add(evalTaskSessionPo);
            }
        }
        int mockTimes = evalRequest.getMockTimes();
        while (mockTimes > 0) {
            for (EvalTaskSessionPo originSession : originSessionList) {
                EvalTaskSessionPo evalTaskSessionPo = new EvalTaskSessionPo();
                evalTaskSessionPo.setTaskId(originSession.getTaskId());
                evalTaskSessionPo.setDatasetId(originSession.getDatasetId());
                evalTaskSessionPo.setSessionId(originSession.getSessionId());
                evalTaskSessionPo.setReferenceData(originSession.getReferenceData());
                evalTaskSessionPo.setGmtCreated(originSession.getGmtCreated());
                evalTaskSessionPo.setGmtModified(originSession.getGmtModified());
                evalTaskSessionPo.setStatus(originSession.getStatus());
                sessionList.add(evalTaskSessionPo);
            }
            mockTimes--;
        }
        if (sessionList.size() > 100) {
            List<List<EvalTaskSessionPo>> partitionList = Lists.partition(sessionList, 100);
            for (List<EvalTaskSessionPo> partition : partitionList) {
                evalTaskSessionGeneratorService.saveBatch(partition);
            }
        } else {
            evalTaskSessionGeneratorService.saveBatch(sessionList);
        }
    }

    private String buildContext(List<SampleData> sampleDataList) {
        Map<String, Object> context = new HashMap<>();
        List<SampleData> referenceList = new ArrayList<>();
        for (SampleData sampleData : sampleDataList) {
            SampleData reference = new SampleData();
            reference.setQueryId(sampleData.getQueryId());
            reference.setInputContent(sampleData.getInputContent());
            reference.setModelExpectList(sampleData.getModelExpectList());
            referenceList.add(reference);
        }
        context.put("params", sampleDataList.get(0).getParams());
        context.put("summary", sampleDataList.get(0).getSummary());
        context.put("dataList", referenceList);
        //业务参数
        context.put("businessParam", sampleDataList.get(0).getBusinessParam());
        return JSON.toJSONString(context);
    }

    private void logSession(EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo) {
        List<EvalTaskQueryPo> evalTaskQueryList = new ArrayList<>();
        List<EvalTaskQueryDetailPo> evalTaskQueryDetailList = new ArrayList<>();
        if (evalRequest.getTaskType() != null && TaskTypeEnum.TRAIN_MANUAL_ANNOTATION.getCode() == evalRequest.getTaskType()) {
            for (SampleData sampleData : evalRequest.getSampleDataList()) {
                Map.Entry<String, List<SampleData>> sampleDataMap = new AbstractMap.SimpleEntry<>(sampleData.getSessionId(), Collections.singletonList(sampleData));
                buildTaskSession(evalIdInfo, sampleDataMap, sampleData.getDatasetId(), sampleData.getSessionId(), evalTaskQueryList, evalTaskQueryDetailList);
            }
        } else {
            // 第一级，按照datasetId分组
            Map<Long, List<SampleData>> datasetMap = evalRequest.getSampleDataList().stream().collect(Collectors.groupingBy(SampleData::getDatasetId, LinkedHashMap::new, Collectors.toList()));
            for (Map.Entry<Long, List<SampleData>> datasetEntry : datasetMap.entrySet()) {
                Long datasetId = datasetEntry.getKey();
                // 第二级，按照sessionId分组
                Map<String, List<SampleData>> sessionMap = datasetEntry.getValue().stream().collect(Collectors.groupingBy(SampleData::getSessionId, LinkedHashMap::new, Collectors.toList()));
                for (Map.Entry<String, List<SampleData>> sessionEntry : sessionMap.entrySet()) {
                    String sessionId = sessionEntry.getKey();
                    buildTaskSession(evalIdInfo, sessionEntry, datasetId, sessionId, evalTaskQueryList, evalTaskQueryDetailList);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(evalTaskQueryList)) {
            if (evalTaskQueryList.size() > 100) {
                List<List<EvalTaskQueryPo>> partitionList = Lists.partition(evalTaskQueryList, 100);
                for (List<EvalTaskQueryPo> partition : partitionList) {
                    evalTaskQueryGeneratorService.updateBatchById(partition);
                }
            } else {
                evalTaskQueryGeneratorService.updateBatchById(evalTaskQueryList);
            }
        }

        if (CollectionUtils.isNotEmpty(evalTaskQueryDetailList)) {
            if (evalTaskQueryDetailList.size() > 100) {
                List<List<EvalTaskQueryDetailPo>> partitionList = Lists.partition(evalTaskQueryDetailList, 100);
                for (List<EvalTaskQueryDetailPo> partition : partitionList) {
                    evalTaskQueryDetailGeneratorService.updateBatchById(partition);
                }
            } else {
                evalTaskQueryDetailGeneratorService.updateBatchById(evalTaskQueryDetailList);
            }
        }
    }

    private void buildTaskSession(EvalIdInfo evalIdInfo, Map.Entry<String, List<SampleData>> sessionEntry, Long datasetId, String sessionId, List<EvalTaskQueryPo> evalTaskQueryList, List<EvalTaskQueryDetailPo> evalTaskQueryDetailList) {
        EvalTaskSessionPo evalTaskSessionPo = new EvalTaskSessionPo();
        evalTaskSessionPo.setTaskId(evalIdInfo.getEvalTaskId());
        evalTaskSessionPo.setDatasetId(datasetId);
        evalTaskSessionPo.setSessionId(sessionId);
        evalTaskSessionPo.setGmtCreated(evalIdInfo.getDate());
        evalTaskSessionPo.setGmtModified(evalIdInfo.getDate());
        evalTaskSessionPo.setStatus(TaskSessionStatusEnum.EVALUATING.getCode());
        evalTaskSessionGeneratorService.save(evalTaskSessionPo);
        // 回填conversation_id
        for (SampleData sampleData : sessionEntry.getValue()) {
            EvalTaskQueryPo evalTaskQuery = new EvalTaskQueryPo();
            evalTaskQuery.setId(sampleData.getQueryId());
            evalTaskQuery.setConversationId(String.valueOf(evalTaskSessionPo.getId()));
            evalTaskQueryList.add(evalTaskQuery);
            if (CollectionUtils.isNotEmpty(sampleData.getTaskQueryDetailPoList())) {
                for (EvalTaskQueryDetailPo queryDetailPo : sampleData.getTaskQueryDetailPoList()) {
                    EvalTaskQueryDetailPo queryDetail = new EvalTaskQueryDetailPo();
                    queryDetail.setId(queryDetailPo.getId());
                    queryDetail.setConversationId(String.valueOf(evalTaskSessionPo.getId()));
                    evalTaskQueryDetailList.add(queryDetail);
                }
            }
        }
    }

    private void logTaskQuery(EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo) {
        List<EvalTaskQueryDetailPo> evalTaskQueryDetailList = new ArrayList<>();
        for (SampleData sampleData : evalRequest.getSampleDataList()) {
            EvalTaskQueryPo evalTaskQuery = new EvalTaskQueryPo();
            evalTaskQuery.setTaskId(evalIdInfo.getEvalTaskId());
            evalTaskQuery.setDatasetId(sampleData.getDatasetId());
            evalTaskQuery.setSessionId(sampleData.getSessionId());
            evalTaskQuery.setInput(sampleData.getInputContent());
            evalTaskQuery.setContent(sampleData.getContent());
            if (evalRequest.getWhetherRegressDataset() == null || !evalRequest.getWhetherRegressDataset()) {
                if (CollectionUtils.isNotEmpty(sampleData.getModelExpectList())) {
                    evalTaskQuery.setExpect(sampleData.getModelExpectList().get(0).getExpect());
                }
            }
            evalTaskQuery.setBusinessParam(sampleData.getBusinessParam());
            evalTaskQuery.setSummary(sampleData.getSummary());
            if (MapUtils.isNotEmpty(sampleData.getParams())) {
                evalTaskQuery.setParams(JSON.toJSONString(sampleData.getParams()));
            }
            evalTaskQuery.setGmtCreated(evalIdInfo.getDate());
            evalTaskQuery.setGmtModified(evalIdInfo.getDate());
            evalTaskQuery.setTurn(sampleData.getTurn());
            evalTaskQuery.setStatus(CallTypeEnum.OFFLINE.getCode() == evalRequest.getCallType() ? TaskQueryStatusEnum.EXECUTING.getCode() : TaskQueryStatusEnum.EVALUATING.getCode());
            evalTaskQuery.setDetailId(sampleData.getDetailId());
            evalTaskQueryGeneratorService.save(evalTaskQuery);
            sampleData.setQueryId(evalTaskQuery.getId());

            List<EvalTaskQueryDetailPo> taskQueryDetailList = buildTaskQueryDetail(evalRequest, sampleData, evalIdInfo);
            if (CollectionUtils.isNotEmpty(taskQueryDetailList)) {
                sampleData.setTaskQueryDetailPoList(taskQueryDetailList);
                evalTaskQueryDetailList.addAll(taskQueryDetailList);
            }
        }

        // 批量插入 detail
        if (CollectionUtils.isNotEmpty(evalTaskQueryDetailList)) {
            if (evalTaskQueryDetailList.size() > 100) {
                List<List<EvalTaskQueryDetailPo>> partitionList = Lists.partition(evalTaskQueryDetailList, 100);
                for (List<EvalTaskQueryDetailPo> partition : partitionList) {
                    evalTaskQueryDetailGeneratorService.saveBatch(partition);
                }
            } else {
                evalTaskQueryDetailGeneratorService.saveBatch(evalTaskQueryDetailList);
            }
        }
    }

    private List<EvalTaskQueryDetailPo> buildTaskQueryDetail(EvalTaskRequest evalRequest, SampleData sampleData, EvalIdInfo evalIdInfo) {
        return buildTaskQueryDetail(evalRequest, sampleData, evalIdInfo, null);
    }

    public List<EvalTaskQueryDetailPo> buildTaskQueryDetail(EvalTaskRequest evalRequest, SampleData sampleData, EvalIdInfo evalIdInfo, EvalTaskSessionPo session) {
        List<EvalTaskQueryDetailPo> evalTaskQueryDetailList = new ArrayList<>();
        List<Integer> metricList = getMetricList(evalRequest);
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricList.stream().map(Integer::longValue).collect(Collectors.toList()));
        Map<Long, MetricConfigPo> metricConfigMap = metricConfigList.stream().collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));
        List<TaskMetricParam> outputKeyMetricList = evalRequest.getMetricList()
                .stream()
                .filter(taskMetricParam -> StringUtils.isNotBlank(taskMetricParam.getOutputKey()))
                .collect(Collectors.toList());
        Map<String, ModelExpectDTO> outputKeyMetricMap = sampleData.getModelExpectList().stream().collect(Collectors.toMap(ModelExpectDTO::getOutputKey, Function.identity()));
        for (String application : evalRequest.getApplicationConfig()) {
            String model = evalRequest.getApplicationModelMap() != null && evalRequest.getApplicationModelMap().containsKey(application) ? evalRequest.getApplicationModelMap().get(application) : null;
            for (TaskMetricParam outputKeyMetric : evalRequest.getMetricList()) {
                String outputKey = CollectionUtils.isNotEmpty(outputKeyMetricList) ? outputKeyMetric.getOutputKey() : CommonConstants.COMPLETE_OUTPUT;
                Integer metricId = outputKeyMetric.getMetricId().intValue();
                EvalTaskQueryDetailPo evalTaskQueryDetail = new EvalTaskQueryDetailPo();
                evalTaskQueryDetail.setTaskId(evalIdInfo.getEvalTaskId());
                evalTaskQueryDetail.setOutputKey(outputKey);
                evalTaskQueryDetail.setDatasetId(sampleData.getDatasetId());
                evalTaskQueryDetail.setSessionId(sampleData.getSessionId());
                if (session != null) {
                    evalTaskQueryDetail.setConversationId(String.valueOf(session.getId()));
                }
                evalTaskQueryDetail.setQueryId(sampleData.getQueryId());
                evalTaskQueryDetail.setModelOutput(getModelOutput(sampleData, model, application));
                evalTaskQueryDetail.setOrdinal(1);
                evalTaskQueryDetail.setMetricId(metricId);
                evalTaskQueryDetail.setTestModel(model);
                evalTaskQueryDetail.setStatus(AutoTaskEvalStatusEnum.EVALUATING.getCode());
                evalTaskQueryDetail.setGmtCreated(evalIdInfo.getDate());
                evalTaskQueryDetail.setGmtModified(evalIdInfo.getDate());
                evalTaskQueryDetail.setTestApplication(application);
                if (null != outputKeyMetricMap.get(outputKey)) {
                    evalTaskQueryDetail.setExpect(outputKeyMetricMap.get(outputKey).getExpect());
                } else {
                    if (Objects.equals(evalRequest.getWhetherRegressDataset(), null) || !evalRequest.getWhetherRegressDataset()) {
                        evalTaskQueryDetail.setExpect(CollectionUtils.isNotEmpty(sampleData.getModelExpectList()) ? sampleData.getModelExpectList().get(0).getExpect() : null);
                    }
                }
                evalTaskQueryDetailList.add(evalTaskQueryDetail);
                // 二级指标
                List<EvalTaskQueryDetailPo> subMetricDetailList = getSubMetricDetail(evalTaskQueryDetail, metricConfigMap.get(metricId.longValue()));
                if (CollectionUtils.isNotEmpty(subMetricDetailList)) {
                    evalTaskQueryDetailList.addAll(subMetricDetailList);
                }
            }
        }
        return evalTaskQueryDetailList;
    }

    public List<EvalTaskQueryDetailPo> getSubMetricDetail(EvalTaskQueryDetailPo evalTaskQueryDetail, MetricConfigPo metricConfigPo) {
        List<EvalTaskQueryDetailPo> subMetricDetailList = new ArrayList<>();
        List<Integer> childIdNodeList = getChildNode(metricConfigPo.getRanges());
        if (CollectionUtils.isNotEmpty(childIdNodeList)) {
            for (Integer childIdNode : childIdNodeList) {
                EvalTaskQueryDetailPo subQueryDetailPo = new EvalTaskQueryDetailPo();
                BeanUtils.copyProperties(evalTaskQueryDetail, subQueryDetailPo);
                subQueryDetailPo.setMetricId(childIdNode);
                subMetricDetailList.add(subQueryDetailPo);
            }
        }
        return subMetricDetailList;
    }

    public List<Integer> getChildNode(String extra) {
        if (StringUtils.isBlank(extra)) {
            return new ArrayList<>();
        }
        JSONObject jsonObject = DataConvertUtil.tryConvertJson(extra);
        if (jsonObject == null || !jsonObject.containsKey("childNode")) {
            return new ArrayList<>();
        }
        return (List<Integer>) jsonObject.get("childNode");
    }

    public String getModelOutput(SampleData sampleData, String modelType, String application) {
        Long applicationId = DataConvertUtil.tryConvertLongWithNull(application);
        if (CollectionUtils.isNotEmpty(sampleData.getOutput())) {
            for (ModelOutputDTO modelOutputDTO : sampleData.getOutput()) {
                if (modelOutputDTO.getApplicationId() != null && applicationId != null) {
                    if (applicationId.equals(modelOutputDTO.getApplicationId())) {
                        return modelOutputDTO.getResult();
                    }
                } else {
                    if (modelType.equals(modelOutputDTO.getName())) {
                        return modelOutputDTO.getResult();
                    }
                }
            }
        }
        return null;
    }

    private void logTask(EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo) {
        EvalTaskPo evalTask = new EvalTaskPo();
        evalTask.setName(StringUtils.isBlank(evalRequest.getName()) ? "ai搭任务" : evalRequest.getName());
        evalTask.setPlatformType(PlatformTypeEnum.SYSTEM.getCode());
        evalTask.setPlatformWorkspace(evalRequest.getWorkspaceId());
        evalTask.setPlatformApp(evalRequest.getApplicationId());
        evalTask.setAbility(evalRequest.getAbility());
        String datasetIds = CollectionUtils.isNotEmpty(evalRequest.getDataSetIdList()) ? StringUtils.join(evalRequest.getDataSetIdList(), ",") : String.valueOf(evalIdInfo.getDatasetId());
        evalTask.setDatasetIds(datasetIds);
        evalTask.setType(evalRequest.getTaskType());
        evalTask.setCallType(evalRequest.getCallType());
        evalTask.setInspectors(evalRequest.getInspectors());
        evalTask.setInputSource(evalRequest.getInputSource());
        evalTask.setMockTimes(evalRequest.getMockTimes() == null ? 0L : evalRequest.getMockTimes().longValue());
        if (evalRequest.getVersionId() != null) {
            evalTask.setVersionId(evalRequest.getVersionId());
        }
        List<Integer> metricList = getMetricList(evalRequest);
        if (CollectionUtils.isNotEmpty(metricList)) {
            evalTask.setMetrics(StringUtils.join(metricList, ","));
        }
        evalTask.setDimension(AbilityEnum.SINGLE_ROUND.getName().equals(evalRequest.getAbility()) ? DimensionTypeEnum.QUERY.getCode() : DimensionTypeEnum.SESSION.getCode());
        evalTask.setStatus(evalRequest.getInputSource() == TaskInputSourceEnum.MANUAL_MOCK.getCode() ? AutoTaskStatusEnum.MANUAL_MOCK.getCode() : AutoTaskStatusEnum.CREATING.getCode());
        evalTask.setTotalCount(evalRequest.getSampleDataList() == null ? 0 : evalRequest.getSampleDataList().size());
        Date date = new Date();
        evalTask.setGmtCreated(date);
        evalTask.setGmtModified(date);
        evalTask.setCreatorMis(evalRequest.getCreatorMis());
        evalTask.setUpdaterMis(evalRequest.getCreatorMis());
        evalTask.setExtra(buildTaskExtra(evalRequest, evalIdInfo));
        evalTask.setApplicationConfig(JSON.toJSONString(evalRequest.getApplicationConfig()));
        evalTask.setDescription(evalRequest.getDescription());
        evalTaskGeneratorService.save(evalTask);
        evalIdInfo.setEvalTaskId(evalTask.getId());
    }

    private String buildTaskExtra(EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo) {
        TaskExtraParam taskExtraParam = new TaskExtraParam();
        if (CollectionUtils.isNotEmpty(evalRequest.getScoreThreshold())) {
            taskExtraParam.setScoreThresholdMap(JSON.toJSONString(evalRequest.getScoreThreshold()));
        }
        if (MapUtils.isNotEmpty(evalRequest.getBindFields())) {
            taskExtraParam.setBindFields(evalRequest.getBindFields());
        }
        if (CollectionUtils.isNotEmpty(evalRequest.getMetricList())) {
            taskExtraParam.setMetricList(evalRequest.getMetricList());
        }
        taskExtraParam.setEvalRequest(buildEvalRequest(evalRequest));
        taskExtraParam.setEvalIdInfo(JSON.toJSONString(evalIdInfo));
        if (null != evalRequest.getNodeId()) {
            taskExtraParam.setNodeId(evalRequest.getNodeId());
            taskExtraParam.setNodeAppModelVersionId(evalRequest.getNodeAppModelVersionId());
            taskExtraParam.setNodeApiToken(evalRequest.getNodeApiToken());
        }
        return JSON.toJSONString(taskExtraParam);
    }

    public String buildContent(EvalDatasetDetailPo evalDatasetDetailPo, AbilityEnum abilityEnum) {
        Map<String, String> dataList = new HashMap<>();
        if (StringUtils.isNotBlank(evalDatasetDetailPo.getParams()) && !"null".equalsIgnoreCase(evalDatasetDetailPo.getParams())) {
            Map<String, String> paramList = JSON.parseObject(evalDatasetDetailPo.getParams(), new TypeReference<Map<String, String>>() {
            });
            dataList.putAll(paramList);
        }
        if (StringUtils.isNotBlank(evalDatasetDetailPo.getExpect())) {
            dataList.put(TemplateFieldEnum.EXPECT.getInfo(), evalDatasetDetailPo.getExpect());
        }
        if (StringUtils.isNotBlank(evalDatasetDetailPo.getOutput())) {
            dataList.put(TemplateFieldEnum.REPLY.getInfo(), evalDatasetDetailPo.getOutput());
        }
        if (AbilityEnum.MULTI_ROUND.equals(abilityEnum)) {
            if (StringUtils.isNotBlank(evalDatasetDetailPo.getInput())) {
                dataList.put(TemplateFieldEnum.INPUT.getInfo(), evalDatasetDetailPo.getInput());
            }
            if (StringUtils.isNotBlank(evalDatasetDetailPo.getSessionId())) {
                dataList.put(TemplateFieldEnum.SESSION_ID.getInfo(), evalDatasetDetailPo.getSessionId());
            }
            if (StringUtils.isNotBlank(evalDatasetDetailPo.getSummary())) {
                dataList.put(TemplateFieldEnum.SUMMARY.getInfo(), evalDatasetDetailPo.getSummary());
            }
        }
        return JSON.toJSONString(dataList);
    }

    private String buildEvalRequest(EvalTaskRequest evalRequest) {
        EvalTaskRequest copyRequest = new EvalTaskRequest();
        BeanUtils.copyProperties(evalRequest, copyRequest);
        copyRequest.setSampleDataList(null);
        return JSON.toJSONString(copyRequest);
    }

    @Data
    public static class EvalIdInfo implements Serializable {
        private Long datasetId;
        private Long evalTaskId;
        private Date date;
    }

    @Data
    public static class EvalResult implements Serializable {
        /**
         * query评测结果 1-成功 2-失败 3-被中断
         */
        private EvalResultEnum evalResult;
        private String gptReply;
        /**
         * 是否结束评测，人工指标为false，自动指标为true
         */
        private Boolean isEnd;
    }

    /**
     * 评测整体执行结果
     */
    @Data
    public static class EvalExecuteResult implements Serializable {
        /**
         * 评测任务是否结束
         */
        private AutoTaskStatusEnum isTaskEnd;
    }
}
