package com.meituan.csc.aigc.eval.service.strategy.eval.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.google.common.collect.Lists;
import com.meituan.csc.aigc.eval.constants.CommonConstants;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.dao.entity.*;
import com.meituan.csc.aigc.eval.dto.application.ApplicationDTO;
import com.meituan.csc.aigc.eval.dto.gpt.GptReplyDTO;
import com.meituan.csc.aigc.eval.dto.metric.MetricReplyDTO;
import com.meituan.csc.aigc.eval.enums.*;
import com.meituan.csc.aigc.eval.enums.query.EvalResultEnum;
import com.meituan.csc.aigc.eval.exception.EvalSqlException;
import com.meituan.csc.aigc.eval.helper.EvalTaskThreadLocalHelper;
import com.meituan.csc.aigc.eval.param.ConversationInfo;
import com.meituan.csc.aigc.eval.param.dataset.SampleData;
import com.meituan.csc.aigc.eval.param.gpt.AidaRequest;
import com.meituan.csc.aigc.eval.param.gpt.ChatGptHttpRequest;
import com.meituan.csc.aigc.eval.param.session.SessionEvalResultParam;
import com.meituan.csc.aigc.eval.param.task.*;
import com.meituan.csc.aigc.eval.service.strategy.eval.EvalStrategyService;
import com.meituan.csc.aigc.eval.utils.CommonUtils;
import com.meituan.csc.aigc.eval.utils.DataConvertUtil;
import com.meituan.csc.aigc.eval.utils.MathUtils;
import com.meituan.inf.xmdlog.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MultiRoundEvalStrategyStrategyServiceImpl extends CommonEvalStrategyService implements EvalStrategyService {

    @Override
    public String getName() {
        return AbilityEnum.MULTI_ROUND.getName();
    }

    @Override
    public void executeTask(EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo) {
        try {
            List<EvalTaskSessionPo> sessionList = evalRequest.getSessionList();
            if (CollectionUtils.isEmpty(sessionList)) {
                return;
            }
            // 更新任务状态
            updateTaskResultStatusAndScore(evalIdInfo.getEvalTaskId(), AutoTaskStatusEnum.EVALUATING, null);
            // 执行评测 key=applicatConfigID，value=robotId
            Map<String, String> robotMap = getRobotMap(evalRequest);
            Map<String, List<SampleData>> sessionQueryMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(evalRequest.getSampleDataList())) {
                sessionQueryMap = evalRequest.getSampleDataList().stream().collect(Collectors.groupingBy(SampleData::getConversationId));
            }

            int parallelNum = getParallelNum(evalRequest);
            int chunkSize = (int) Math.ceil((double) sessionList.size() / parallelNum);
            List<List<EvalTaskSessionPo>> partitionList = Lists.partition(sessionList, chunkSize);
            List<Future<EvalExecuteResult>> futureList = new ArrayList<>();
            for (List<EvalTaskSessionPo> partition : partitionList) {
                // 并行执行评测任务，提高执行速度
                Map<String, List<SampleData>> finalSessionQueryMap = sessionQueryMap;
                Future<EvalExecuteResult> future = PARALLEL_EVAL_THREAD_POOL.submit(() -> doExecuteTask(partition, evalRequest, evalIdInfo, finalSessionQueryMap, robotMap));
                futureList.add(future);
            }
            // 合并多个并行线程的结果
            EvalExecuteResult evalExecuteResult = mergeResult(futureList);

            // 根据评测结果和任务状态，处理任务结束的相关操作。
            handleTaskEnd(evalRequest, evalIdInfo, evalExecuteResult.getIsTaskEnd());
        } finally {
            EvalTaskThreadLocalHelper.removeCache();
        }
    }

    /**
     * 执行评测任务
     *
     * @param sessionList
     * @param evalRequest
     * @param evalIdInfo
     * @param sessionQueryMap
     * @param robotMap        key=applicatConfigID，value=robotId
     * @return
     */
    private EvalExecuteResult doExecuteTask(List<EvalTaskSessionPo> sessionList, EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo, Map<String, List<SampleData>> sessionQueryMap, Map<String, String> robotMap) {
        EvalExecuteResult evalExecuteResult = new EvalExecuteResult();
        AutoTaskStatusEnum isTaskEnd = AutoTaskStatusEnum.FINISHED;
        // 遍历session
        Integer sessionIndex = 0;
        Integer sessionSize = sessionList.size();
        for (EvalTaskSessionPo session : sessionList) {
            List<SampleData> datasetDataList;
            boolean isSessionEnd = true;
            boolean sessionSuccess = true;
            // 上下文信息
            Map<String, List<ChatGptHttpRequest.GptMessage>> messageMap = new HashMap<>();
            // 按照session顺序排序
            datasetDataList = sessionQueryMap.get(String.valueOf(session.getId()));
            if (CollectionUtils.isNotEmpty(datasetDataList)) {
                datasetDataList = datasetDataList.stream().sorted(Comparator.comparing(SampleData::getTurn)).collect(Collectors.toList());
            } else {
                datasetDataList = new ArrayList<>();
            }
            // session的每一个query进行评测
            Map<String, String> robotSessionMap = new HashMap<>();
            for (int turn = 0; ; turn++) {
                SampleData sampleData = getSampleData(evalRequest, datasetDataList, session, turn, evalIdInfo);
                if (isEnd(evalRequest, turn, sampleData)) {
                    break;
                }
                // 重启任务时，可能已经评测了session中的部分query
                if (sampleData.getStatus() != null && sampleData.getStatus() == TaskQueryStatusEnum.COMPLETED.getCode()) {
                    continue;
                }
                EvalResultEnum evalResultStatus = null;
                boolean isQueryEnd = true;
                List<EvalTaskQueryDetailPo> queryDetailPoList = sampleData.getTaskQueryDetailPoList();
                if (CollectionUtils.isNotEmpty(queryDetailPoList)) {
                    // 按照applicationConfig维度进行评测
                    Map<String, List<EvalTaskQueryDetailPo>> applicationDetailMap = queryDetailPoList.stream().collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getTestApplication));
                    for (Map.Entry<String, List<EvalTaskQueryDetailPo>> applicationEntry : applicationDetailMap.entrySet()) {
                        String applicationConfigId = applicationEntry.getKey();
                        // 设置上下文信息
                        setHistory(messageMap, applicationConfigId, RoleEnum.USER.getName(), sampleData.getInputContent());
                        // 获取多轮会话ID
                        ConversationInfo conversationInfo = getConversationId(robotSessionMap, applicationConfigId, evalRequest.getApplicationModelMap().get(applicationConfigId), robotMap.get(applicationConfigId), sampleData.getSessionId(), evalRequest, datasetDataList.get(0).getParams());
                        EvalRequestParam evalRequestParam = buildEvalRequestParam(applicationEntry.getValue(), sampleData, applicationConfigId, evalRequest.getApplicationModelMap().get(applicationConfigId), evalRequest, evalIdInfo, conversationInfo, robotMap.get(applicationConfigId), messageMap.get(applicationConfigId), session);
                        // 这里增加 判断流式字段
                        // 中断检测，包括超时、任务删除
                        AutoTaskStatusEnum checkResult = interruptCheck(evalRequest.getStartTime(), evalIdInfo.getEvalTaskId(), sessionIndex, sessionSize);
                        if (checkResult != null) {
                            evalExecuteResult.setIsTaskEnd(checkResult);
                            return evalExecuteResult;
                        }

                        // 执行评测
                        EvalResult evalResult = executeEval(evalRequestParam);
                        evalResultStatus = evalResult.getEvalResult();
                        isQueryEnd = evalResult.getIsEnd();
                        setResultHistory(messageMap, applicationConfigId, sampleData, evalRequest, evalResult);
                    }
                }
                boolean evalSuccess = EvalResultEnum.SUCCESS.equals(evalResultStatus) || EvalResultEnum.INTERRUPT.equals(evalResultStatus);
                // 只要有一个query失败,session就是失败
                sessionSuccess = evalSuccess && sessionSuccess;
                if (isQueryEnd) {
                    updateQueryStatus(sampleData.getQueryId(), EvalResultEnum.mapQueryStatus(evalResultStatus));
                } else {
                    isSessionEnd = false;
                }
            }
            // 保存调用每个应用的唯一id
            saveConversation(session, robotSessionMap);
            long evalSessionId;
            if (CollectionUtils.isEmpty(datasetDataList)) {
                evalSessionId = Long.parseLong(session.getSessionId());
            } else {
                evalSessionId = Long.parseLong(datasetDataList.get(0).getConversationId());
            }

            if (CollectionUtils.isNotEmpty(evalRequest.getSessionMetric())) {
                List<Long> metricIdList = evalRequest.getSessionMetric().stream().map(Integer::longValue).collect(Collectors.toList());
                List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricIdList);
                Map<Long, MetricConfigPo> metricConfigMap = metricConfigList.stream().collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));
                // 执行session维度自动指标
                EvalResult sessionResult = executeSessionMetric(evalRequest, evalIdInfo, session.getDatasetId(), evalSessionId, metricConfigMap, messageMap, datasetDataList);
                sessionSuccess = !EvalResultEnum.EVAL_FAIL.equals(sessionResult.getEvalResult()) && sessionSuccess;
                isSessionEnd = sessionResult.getIsEnd() && isSessionEnd;
            }
            if (isSessionEnd) {
                updateSessionResultAndStatus(evalSessionId, null, sessionSuccess ? TaskSessionStatusEnum.COMPLETED : TaskSessionStatusEnum.FAILED);
            } else {
                isTaskEnd = AutoTaskStatusEnum.MARK;
            }
            //每个session结束+1
            sessionIndex++;
        }
        evalExecuteResult.setIsTaskEnd(isTaskEnd);
        return evalExecuteResult;
    }

    private void saveConversation(EvalTaskSessionPo session, Map<String, String> robotSessionMap) {
        try {
            session.setConversationId(JSONObject.toJSONString(robotSessionMap));
            evalTaskSessionGeneratorService.updateById(session);
        } catch (Exception e) {
            log.error("保存应用唯一id失败 ,session={},sessionMap={},msg={}", JSONObject.toJSONString(session), JSONObject.toJSONString(robotSessionMap), e.getMessage(), e);
        }
    }

    private SampleData getSampleData(EvalTaskRequest evalRequest, List<SampleData> datasetDataList, EvalTaskSessionPo session, int turn, EvalIdInfo evalIdInfo) {
        if (TaskInputSourceEnum.DATASET.getCode() == evalRequest.getInputSource()) {
            return turn < datasetDataList.size() ? datasetDataList.get(turn) : null;
        }
        // 如果机器模拟有历史记录，优先获取历史记录
        SampleData sampleData;
        List<EvalTaskQueryDetailPo> queryDetailList;
        if (turn < datasetDataList.size()) {
            sampleData = datasetDataList.get(turn);
            // 如果没有评测详情数据，需要创建
            queryDetailList = sampleData.getTaskQueryDetailPoList();
            if (CollectionUtils.isEmpty(queryDetailList)) {
                queryDetailList = createQueryDetail(evalRequest, sampleData, evalIdInfo, session);
            }
        } else {
            // 记录query信息
            EvalTaskQueryPo query = logQuery(session, turn, evalIdInfo);
            // 创建评测详情记录
            queryDetailList = createQueryDetail(evalRequest, query, evalIdInfo, session);
            //  无历史记录，调用模拟机器人获取输入
            ApplicationRequestParam param = buildRobotMockRequestParam(evalRequest, session, datasetDataList, query, evalIdInfo);
            GptReplyDTO reply = getGptOutput(param);
            sampleData = handleMockRobotResponse(session, query, turn, reply);
            datasetDataList.add(sampleData);
        }
        sampleData.setTaskQueryDetailPoList(queryDetailList);
        return sampleData;
    }

    private ApplicationRequestParam buildRobotMockRequestParam(EvalTaskRequest evalRequest, EvalTaskSessionPo session, List<SampleData> datasetDataList, EvalTaskQueryPo query, EvalIdInfo evalIdInfo) {
        Long applicationId = Lion.getMap(ConfigUtil.getAppkey(), LionConstants.EVAL_ROBOT_MOCK_LIST, ApplicationDTO.class).get(evalRequest.getRobotMockId().toString()).getId();
        ApplicationConfigPo applicationConfig = applicationConfigGeneratorService.getById(applicationId);
        String inputContent = Lion.getString(ConfigUtil.getAppkey(), LionConstants.MODEL_INITIAL_CONTEXT, "你好");
        if (CollectionUtils.isNotEmpty(datasetDataList)) {
            SampleData sampleData = datasetDataList.get(datasetDataList.size() - 1);
            if (CollectionUtils.isNotEmpty(sampleData.getTaskQueryDetailPoList())) {
                EvalTaskQueryDetailPo detail = sampleData.getTaskQueryDetailPoList().get(0);
                if (StringUtils.isNotBlank(detail.getModelOutput())) {
                    inputContent = detail.getModelOutput();
                }
            }
        }
        JSONObject referenceData = new JSONObject();
        if (StringUtils.isNotBlank(session.getReferenceData())) {
            referenceData = JSON.parseObject(session.getReferenceData());
        }
        Map<String, String> params = referenceData.containsKey("params") ? (Map<String, String>) referenceData.get("params") : new HashMap<>();
        AidaRequest aidaRequest = new AidaRequest();
        aidaRequest.setParams(params);
        aidaRequest.setUser(evalRequest.getCreatorMis());
        aidaRequest.setApiSecretKey(aidaInvokeServiceProxy.getApiSecretKey(getAidaAppId(String.valueOf(applicationId))));
        aidaRequest.setModelConfigVersionId(applicationConfig.getRobotId());
        ConversationInfo conversationInfo = aidaInvokeServiceProxy.generateAidaConversationId(aidaRequest);
        ApplicationRequestParam applicationRequestParam = new ApplicationRequestParam();
        applicationRequestParam.setConversionInfo(conversationInfo);
        applicationRequestParam.setInputContent(getOutputMessage(Long.parseLong(evalRequest.getApplicationConfig().get(0)), null, evalRequest, inputContent));
        applicationRequestParam.setParams(params);
        applicationRequestParam.setEvalTaskId(evalIdInfo.getEvalTaskId());
        applicationRequestParam.setCallType(CallTypeEnum.OFFLINE.getCode());
        applicationRequestParam.setAbility(AbilityEnum.MULTI_ROUND.getName());
        applicationRequestParam.setCreatorMis(evalRequest.getCreatorMis());
        applicationRequestParam.setDatasetId(session.getDatasetId());
        applicationRequestParam.setQueryId(query.getId());
        applicationRequestParam.setSessionId(session.getSessionId());
        applicationRequestParam.setTestApplication(String.valueOf(applicationId));
        applicationRequestParam.setIsInner(evalRequest.getIsInner());
        applicationRequestParam.setConversationId(session.getId());
        applicationRequestParam.setRobotId(applicationConfig.getRobotId());
        applicationRequestParam.setAidaAppId(applicationConfig.getPlatformApp());
        applicationRequestParam.setMessageList(buildMessageList(evalRequest, datasetDataList));
        applicationRequestParam.setLogType(TaskLogTypeEnum.ROBOT_MOCK.getCode());
        return applicationRequestParam;
    }

    public List<ChatGptHttpRequest.GptMessage> buildMessageList(EvalTaskRequest evalRequest, List<SampleData> datasetDataList) {
        if (CollectionUtils.isEmpty(datasetDataList)) {
            return new ArrayList<>();
        }
        List<ChatGptHttpRequest.GptMessage> messages = new ArrayList<>();
        datasetDataList.forEach(datasetData -> {
            if (StringUtils.isNotBlank(datasetData.getInputContent())) {
                ChatGptHttpRequest.GptMessage message = new ChatGptHttpRequest.GptMessage();
                message.setRole(CommonConstants.GPT_PARAM_ASSISTANT);
                message.setContent(getInputParam(datasetData.getInputContent()));
                messages.add(message);
            }
            if (CollectionUtils.isNotEmpty(datasetData.getTaskQueryDetailPoList())) {
                EvalTaskQueryDetailPo detail = datasetData.getTaskQueryDetailPoList().get(0);
                if (StringUtils.isNotBlank(detail.getModelOutput())) {
                    ChatGptHttpRequest.GptMessage message = new ChatGptHttpRequest.GptMessage();
                    message.setRole(CommonConstants.GPT_PARAM_USER);
                    message.setContent(getOutputMessage(Long.parseLong(detail.getTestApplication()), datasetData.getExpectedResult(), evalRequest, detail.getModelOutput()));
                    messages.add(message);
                }
            }
        });
        return messages;
    }

    private String getInputParam(String inputContent) {
        if (StringUtils.isBlank(inputContent)) {
            return inputContent;
        }
        JSONObject jsonObject = DataConvertUtil.tryConvertJson(inputContent);
        if (jsonObject != null && !jsonObject.containsKey("response")) {
            return jsonObject.getString("response");
        }
        return inputContent;
    }

    private SampleData handleMockRobotResponse(EvalTaskSessionPo session, EvalTaskQueryPo query, int turn, GptReplyDTO reply) {
        SampleData sampleData = new SampleData();
        sampleData.setConversationId(String.valueOf(session.getId()));
        sampleData.setSessionId(session.getSessionId());
        sampleData.setDatasetId(session.getDatasetId());
        sampleData.setQueryId(query.getId());
        JSONObject referenceData = new JSONObject();
        if (StringUtils.isNotBlank(session.getReferenceData())) {
            referenceData = JSON.parseObject(session.getReferenceData());
        }
        sampleData.setParams(referenceData.containsKey("params") ? (Map<String, String>) referenceData.get("params") : new HashMap<>());
        sampleData.setTurn(turn + 1);
        sampleData.setStatus(TaskQueryStatusEnum.EVALUATING.getCode());
        sampleData.setSummary(referenceData.containsKey("summary") ? referenceData.get("summary").toString() : null);
        JSONObject jsonObject = DataConvertUtil.tryConvertJson(reply.getAnswer());
        // 当有结束标识时，标记为会话结束
        if (jsonObject != null && jsonObject.containsKey("completion") && "1".equalsIgnoreCase(jsonObject.getString("completion"))) {
            sampleData.setIsEnd(Boolean.TRUE);
        } else {
            // 当没有结束标记时，获取模拟的输入内容
            if (jsonObject != null && jsonObject.containsKey("reply")) {
                String inputContent = StringUtils.isNotBlank(jsonObject.getString("reply")) ? jsonObject.getString("reply") : " ";
                sampleData.setInputContent(inputContent);
                query.setInput(jsonObject.getString("reply"));
                evalTaskQueryGeneratorService.updateById(query);
            } else {
                // 如果没有获取到输入内容，则用空输入继续执行流程
                sampleData.setInputContent(" ");
            }
        }
        return sampleData;
    }

    private EvalTaskQueryPo logQuery(EvalTaskSessionPo session, int turn, EvalIdInfo evalIdInfo) {
        EvalTaskQueryPo query = new EvalTaskQueryPo();
        query.setTaskId(evalIdInfo.getEvalTaskId());
        query.setDatasetId(session.getDatasetId());
        query.setSessionId(session.getSessionId());
        query.setConversationId(String.valueOf(session.getId()));
        query.setGmtCreated(evalIdInfo.getDate());
        query.setGmtModified(evalIdInfo.getDate());
        query.setTurn(turn + 1);
        query.setStatus(TaskQueryStatusEnum.EVALUATING.getCode());
        JSONObject referenceData = new JSONObject();
        if (StringUtils.isNotBlank(session.getReferenceData())) {
            referenceData = JSON.parseObject(session.getReferenceData());
        }
        query.setParams(referenceData.containsKey("params") ? JSON.toJSONString(referenceData.get("params")) : null);
        query.setSummary(referenceData.containsKey("summary") ? referenceData.get("summary").toString() : null);
        evalTaskQueryGeneratorService.save(query);
        return query;
    }

    private List<EvalTaskQueryDetailPo> createQueryDetail(EvalTaskRequest evalRequest, EvalTaskQueryPo query, EvalIdInfo evalIdInfo, EvalTaskSessionPo session) {
        SampleData sampleData = new SampleData();
        sampleData.setDatasetId(query.getDatasetId());
        sampleData.setSessionId(query.getSessionId());
        sampleData.setQueryId(query.getId());
        return createQueryDetail(evalRequest, sampleData, evalIdInfo, session);
    }

    private List<EvalTaskQueryDetailPo> createQueryDetail(EvalTaskRequest evalRequest, SampleData sampleData, EvalIdInfo evalIdInfo, EvalTaskSessionPo session) {
        List<EvalTaskQueryDetailPo> queryDetailList = buildTaskQueryDetail(evalRequest, sampleData, evalIdInfo, session);
        evalTaskQueryDetailGeneratorService.saveBatch(queryDetailList);
        return queryDetailList;
    }

    private boolean isEnd(EvalTaskRequest evalRequest, int turn, SampleData sampleData) {
        if (TaskInputSourceEnum.DATASET.getCode() == evalRequest.getInputSource()) {
            return sampleData == null;
        }
        boolean isEnd = sampleData == null || (sampleData.getIsEnd() != null && sampleData.getIsEnd());
        // 模拟器内容熔断内容熔断
        isEnd = isEnd || contentFuse(sampleData.getInputContent());
        // 兜底20轮结束
        isEnd = isEnd || turn >= Lion.getInt(ConfigUtil.getAppkey(), LionConstants.ROBOT_MOCK_MAX_TURN, 20);
        // 模拟结束时不记录数据，删除记录
        if (isEnd && sampleData != null) {
            evalTaskQueryGeneratorService.removeById(sampleData.getQueryId());
        }
        return isEnd;
    }

    private boolean contentFuse(String inputContent) {
        List<String> fuseContentList = Lion.getList(ConfigUtil.getAppkey(), LionConstants.ROBOT_MOCK_FUSE_CONTENT, String.class);
        if (CollectionUtils.isEmpty(fuseContentList)) {
            return false;
        }
        List<Pattern> patterns = fuseContentList.stream()
                .map(Pattern::compile)
                .collect(Collectors.toList());
        return patterns.stream()
                .anyMatch(pattern -> pattern.matcher(inputContent).find());
    }

    private EvalResult executeSessionMetric(EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo, Long datasetId, Long evalSessionId, Map<Long, MetricConfigPo> metricConfigMap, Map<String, List<ChatGptHttpRequest.GptMessage>> messageMap, List<SampleData> sampleDataList) {
        EvalResult evalResult = new EvalResult();
        boolean isSessionEnd = true;
        EvalResultEnum sessionResultStatus = EvalResultEnum.SUCCESS;
        for (Integer metric : evalRequest.getSessionMetric()) {
            MetricConfigPo metricConfig = metricConfigMap.get(metric.longValue());
            if (metricConfig.getEvalType() != null && metricConfig.getEvalType() == MetricEvalTypeEnum.MANUAL.getCode()) {
                isSessionEnd = false;
                continue;
            }
            for (String application : evalRequest.getApplicationConfig()) {
                MetricRequestParam evalRequestParam = buildEvalRequestParam(evalRequest, evalIdInfo, datasetId, evalSessionId, sampleDataList, messageMap.get(application), application, metric);
                // session维度指标没有具体的评测记录
                MetricReplyDTO result = executeMetric(null, null, evalRequestParam);
                String answer = StringUtils.isBlank(result.getResult()) ? result.getMessage() : result.getResult();
                List<EvalTaskQueryDetailPo> queryDetailList = buildQueryDetail(sampleDataList, application, metric);
                for (EvalTaskQueryDetailPo queryDetail : queryDetailList) {
                    updateQueryDetailEvalResult(queryDetail, null, answer, result.getIsSuccess() ? AutoTaskEvalStatusEnum.EVALUATED.getCode() : AutoTaskEvalStatusEnum.FAILED.getCode());
                }
                if (sessionResultStatus.equals(EvalResultEnum.SUCCESS) && StringUtils.isBlank(result.getResult())) {
                    sessionResultStatus = EvalResultEnum.EVAL_FAIL;
                }
                SessionEvalResultParam sessionResult = new SessionEvalResultParam();
                sessionResult.setMetricId(metric);
                sessionResult.setMetricName(metricConfig.getName());
                sessionResult.setApplication(application);
                sessionResult.setResult(StringUtils.isNotBlank(result.getResult()) ? result.getResult() : result.getMessage());
                updateSessionResultAndStatus(evalSessionId, sessionResult, null);
            }
        }
        evalResult.setIsEnd(isSessionEnd);
        evalResult.setEvalResult(sessionResultStatus);
        return evalResult;
    }

    private void setResultHistory(Map<String, List<ChatGptHttpRequest.GptMessage>> messageMap, String application, SampleData sampleData, EvalTaskRequest evalRequest, EvalResult evalResult) {
        Long applicationId = Long.parseLong(application);
        String message = getOutputMessage(applicationId, sampleData.getExpectedResult(), evalRequest, evalResult.getGptReply());
        if (StringUtils.isNotBlank(message)) {
            setHistory(messageMap, application, RoleEnum.ASSISTANT.getName(), message);
        }
    }

    private void setHistory(Map<String, List<ChatGptHttpRequest.GptMessage>> messageMap, String application, String role, String content) {
        ChatGptHttpRequest.GptMessage message = new ChatGptHttpRequest.GptMessage(role, content);
        List<ChatGptHttpRequest.GptMessage> messageList = messageMap.getOrDefault(application, new ArrayList<>());
        messageList.add(message);
        messageMap.put(application, messageList);
    }

    private MetricRequestParam buildEvalRequestParam(EvalTaskRequest evalRequest, EvalIdInfo evalIdInfo, Long datasetId, Long evalSessionId, List<SampleData> sampleDataList, List<ChatGptHttpRequest.GptMessage> messageList, String application, Integer metric) {
        MetricRequestParam metricRequestParam = new MetricRequestParam();
        metricRequestParam.setParams(sampleDataList.get(0).getParams());
        metricRequestParam.setMetric(metricConfigGeneratorService.getById(metric));
        metricRequestParam.setIsInner(evalRequest.getIsInner());
        metricRequestParam.setMessageList(messageList);
        metricRequestParam.setTestApplication(application);
        CommonLogParam logParam = new CommonLogParam();
        logParam.setLogType(TaskLogTypeEnum.SESSION_METRIC.getCode());
        logParam.setMetricId(metric.longValue());
        logParam.setTaskId(evalIdInfo.getEvalTaskId());
        logParam.setDatasetId(datasetId);
        logParam.setSessionId(String.valueOf(evalSessionId));
        logParam.setTestApplication(application);
        metricRequestParam.setLogParam(logParam);
        return metricRequestParam;
    }

    private List<EvalTaskQueryDetailPo> buildQueryDetail(List<SampleData> sampleDataList, String application, Integer metric) {
        List<EvalTaskQueryDetailPo> queryDetailPoList = new ArrayList<>();
        for (SampleData sampleData : sampleDataList) {
            if (CollectionUtils.isNotEmpty(sampleData.getTaskQueryDetailPoList())) {
                for (EvalTaskQueryDetailPo queryDetail : sampleData.getTaskQueryDetailPoList()) {
                    if (queryDetail.getTestApplication().equals(application) && queryDetail.getMetricId().equals(metric)) {
                        queryDetailPoList.add(queryDetail);
                    }
                }
            }
        }
        return queryDetailPoList;
    }

    /**
     * 获取会话ID
     *
     * @param robotSessionMap
     * @param application
     * @param model
     * @param robotId
     * @param sessionId
     * @param evalRequest
     * @param params
     * @return
     */
    private ConversationInfo getConversationId(Map<String, String> robotSessionMap, String application, String model, String robotId, String sessionId, EvalTaskRequest evalRequest, Map<String, String> params) {
        List<String> glmModelList = Lion.getList(ConfigUtil.getAppkey(), "aida.python.model.list", String.class);
        ConversationInfo conversationInfo = new ConversationInfo();
        String conversationId = robotSessionMap.get(application);
        if (CallTypeEnum.OFFLINE.getCode() == evalRequest.getCallType()) {
            if (StringUtils.isBlank(conversationId)) {
                ApplicationConfigPo applicationConfig = getApplication(application);
                if (ApplicationSourceEnum.PB_SYSTEM.getCode() == applicationConfig.getSource()) {
                    // PB来源应用
                    conversationId = CommonUtils.uuid();
                    conversationInfo.setConversationId(conversationId);
                } else {
                    // AI搭来源应用，如果是glm走自定义调用，其他走AI搭平台
                    if (StringUtils.isNotBlank(model) && CollectionUtils.isNotEmpty(glmModelList) && glmModelList.contains(model)) {
                        conversationId = CommonUtils.uuid();
                        conversationInfo.setConversationId(conversationId);
                    } else {
                        AidaRequest aidaRequest = builderAidaRequest(application, evalRequest, params, sessionId, robotId);
                        conversationInfo = aidaInvokeServiceProxy.generateAidaConversationId(aidaRequest);
                        conversationId = conversationInfo.getConversationId();
                    }
                }
                robotSessionMap.put(application, conversationId);
            } else {
                conversationInfo.setConversationId(conversationId);
            }
        }
        return conversationInfo;
    }

    private void updateSessionResultAndStatus(Long evalSessionId, SessionEvalResultParam result, TaskSessionStatusEnum taskSessionStatusEnum) {
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            EvalTaskSessionPo evalTaskSessionPo = evalTaskSessionGeneratorService.getById(evalSessionId);
            ZebraForceMasterHelper.clearLocalContext();
            if (evalTaskSessionPo == null) {
                Cat.logError(new EvalSqlException("session不存在"));
                log.error("session不存在,evalSessionId={}", evalSessionId);
                return;
            }
            if (result == null && taskSessionStatusEnum == null) {
                return;
            }
            if (taskSessionStatusEnum != null) {
                evalTaskSessionPo.setStatus(taskSessionStatusEnum.getCode());
            }
            if (result != null) {
                List<SessionEvalResultParam> sessionResultList = new ArrayList<>();
                if (StringUtils.isNotBlank(evalTaskSessionPo.getSessionResult())) {
                    sessionResultList = JSONObject.parseArray(evalTaskSessionPo.getSessionResult(), SessionEvalResultParam.class);
                }
                sessionResultList.add(result);
                evalTaskSessionPo.setSessionResult(JSON.toJSONString(sessionResultList));
            }
            evalTaskSessionPo.setGmtModified(new Date());
            evalTaskSessionGeneratorService.updateById(evalTaskSessionPo);
        } catch (Exception e) {
            Cat.logError(new EvalSqlException(e));
            log.error("写入session数据库失败,evalSessionId={},result={},taskSessionStatusEnum={},msg={}", evalSessionId, JSON.toJSONString(result), taskSessionStatusEnum, e.getMessage(), e);
        }
    }

    private AidaRequest builderAidaRequest(String application, EvalTaskRequest evalRequest, Map<String, String> params, String sessionId, String robotId) {
        AidaRequest aidaRequest = new AidaRequest();
        aidaRequest.setParams(params);
        aidaRequest.setUser(evalRequest.getCreatorMis());
        aidaRequest.setConversationId(sessionId);
        aidaRequest.setApiSecretKey(aidaInvokeServiceProxy.getApiSecretKey(getAidaAppId(application)));
        aidaRequest.setNodeAppModelVersionId(evalRequest.getNodeAppModelVersionId());
        aidaRequest.setNodeApiToken(evalRequest.getNodeApiToken());
        aidaRequest.setModelConfigVersionId(robotId);
        aidaRequest.setNodeId(evalRequest.getNodeId());
        return aidaRequest;
    }

    @Override
    public double calScore(Long taskId, Double scoreThreshold) {
        // 重新读取一遍数据，因为任务重启后，数据集是未完成部分，不是全部数据
        ZebraForceMasterHelper.forceMasterInLocalContext();
        List<EvalTaskQueryDetailPo> evalTaskQueryDetailList = evalTaskQueryDetailGeneratorService.getByTaskId(taskId);
        ZebraForceMasterHelper.clearLocalContext();
        Map<String, List<EvalTaskQueryDetailPo>> sessionDetailMap = evalTaskQueryDetailList.stream().collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getSessionId));
        int correctSize = 0;
        int totalSize = 0;
        for (Map.Entry<String, List<EvalTaskQueryDetailPo>> sessionEntry : sessionDetailMap.entrySet()) {
            List<EvalTaskQueryDetailPo> queryDetailPoList = sessionEntry.getValue();
            boolean correct = true;
            for (EvalTaskQueryDetailPo evalTaskQueryDetailPo : queryDetailPoList) {
                double score = 0;
                if (StringUtils.isNotBlank(evalTaskQueryDetailPo.getMetricResult())) {
                    score = DataConvertUtil.tryConvertDouble(evalTaskQueryDetailPo.getMetricResult());
                }
                if (score <= scoreThreshold) {
                    correct = false;
                    break;
                }
            }
            if (correct) {
                correctSize++;
            }
            totalSize++;
        }
        double avgScore = 0.0;
        if (totalSize > 0) {
            avgScore = ((double) correctSize) / ((double) totalSize);
        }
        avgScore = MathUtils.save2Double(avgScore * 100);
        return avgScore;
    }
}
