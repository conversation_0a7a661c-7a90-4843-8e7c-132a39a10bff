package com.meituan.csc.aigc.eval.service.template;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.constants.RedisConstants;
import com.meituan.csc.aigc.eval.dao.entity.EvalDatasetDetailPo;
import com.meituan.csc.aigc.eval.dao.entity.EvalDatasetPo;
import com.meituan.csc.aigc.eval.dao.service.generator.EvalDatasetDetailGeneratorService;
import com.meituan.csc.aigc.eval.dao.service.generator.EvalDatasetGeneratorService;
import com.meituan.csc.aigc.eval.dto.dataset.SceneFiledDTO;
import com.meituan.csc.aigc.eval.dto.dataset.SceneFiledWithCodeDTO;
import com.meituan.csc.aigc.eval.enums.DatasetStatusEnum;
import com.meituan.csc.aigc.eval.enums.dataset.DatasetDetailStatusEnum;
import com.meituan.csc.aigc.eval.enums.dataset.DatasetFiledEnum;
import com.meituan.csc.aigc.eval.enums.dataset.FieldSourceEnum;
import com.meituan.csc.aigc.eval.enums.dataset.SceneFiledTypeEnum;
import com.meituan.csc.aigc.eval.exception.EvalException;
import com.meituan.csc.aigc.eval.param.dataset.*;
import com.meituan.csc.aigc.eval.proxy.HiveRequestServiceProxy;
import com.meituan.csc.aigc.eval.proxy.RedisClientProxy;
import com.meituan.csc.aigc.eval.utils.CommonUtils;
import com.meituan.csc.aigc.eval.utils.DateUtil;
import com.meituan.csc.aigc.runtime.inner.api.AcInvokeRemoteService;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.meituan.talos.commons.domain.Column;
import com.sankuai.data.talos.AsyncTalosClient;
import com.sankuai.data.talos.model.QueryResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class DatasetTemplate {

    @Autowired
    private EvalDatasetGeneratorService evalDatasetGeneratorService;

    @Autowired
    protected EvalDatasetDetailGeneratorService evalDatasetDetailGeneratorService;

    @Autowired
    private HiveRequestServiceProxy hiveRequestServiceProxy;

    @Autowired
    private RedisClientProxy redisStoreClient;

    @Autowired
    private AcInvokeRemoteService acInvokeRemoteService;

    public final DatasetPullResponse invoke(DatasetPullRequest request) {
        DatasetPullResponse response = new DatasetPullResponse();
        try {
            // 线下数据mock，hive没有线下数据，线下测试只能通过mock得到线上数据，线上不会执行
            mockData(request);
            // step：预处理
            preProcess(request, response);
            // step2:查询hive
            searchHive(request, response);
            if (CollectionUtils.isNotEmpty(response.getDataList())) {
                // step3:调用AC
                invokeAc(request, response);
                // step4:后处理
                postProcess(request, response);
            }
            if (response.getStatus() == null) {
                response.setStatus(DatasetPullResponse.DatasetPullStatus.SUCCESS);
            }
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
            log.error("拉取数据失败,msg={}", e.getMessage(), e);
            response.setStatus(DatasetPullResponse.DatasetPullStatus.INNER_ERROR);
            setResponseMessage(response, e.getMessage());
        } finally {
            // 更新数据集状态
            updateDatasetStatus(request.getDatasetId(), DatasetPullResponse.DatasetPullStatus.SUCCESS.equals(response.getStatus()) ? DatasetStatusEnum.VALID : DatasetStatusEnum.FAILED);
        }
        return response;
    }

    private boolean isFailed(DatasetPullResponse response) {
        return response.getStatus() != null && !response.getStatus().equals(DatasetPullResponse.DatasetPullStatus.SUCCESS);
    }

    /**
     * 预处理
     *
     * @param request  请求数据
     * @param response 返回数据
     */
    private void preProcess(DatasetPullRequest request, DatasetPullResponse response) {
        if (isFailed(response)) {
            return;
        }
        try {
            log.info("初始参数:{}", JSON.toJSONString(request));
            updateDatasetStatus(request.getDatasetId(), DatasetStatusEnum.PRE_HANDLE);
            response.getProcessData().setPreHandleStartTime(DateUtil.getNow());
            DatasetPullProcessResponse processResponse = executePreProcess(request);
            response.getProcessData().setPreHandleEndTime(DateUtil.getNow());
            response.getProcessData().setPreHandleStatus(DatasetPullResponse.DatasetPullStatus.getStatusString(processResponse.getStatus()));
            log.info("预处理执行完成,request={},response={}", JSON.toJSONString(request), JSON.toJSONString(processResponse));
            handleResponse(request, processResponse, response);
        } catch (Exception e) {
            response.setStatus(DatasetPullResponse.DatasetPullStatus.PRE_HANDLE_ERROR);
            setResponseMessage(response, e.getMessage());
            log.info("预处理失败,request={},msg={}", JSON.toJSONString(request), e.getMessage(), e);
        } finally {
            updateDatasetProcess(request.getDatasetId(), response.getProcessData());
        }
    }

    /**
     * 执行预处理方法，默认没有任何处理，各模版自己实现
     *
     * @param request 请求数据
     * @return 预处理结果
     */
    protected DatasetPullProcessResponse executePreProcess(DatasetPullRequest request) {
        DatasetPullProcessResponse processResponse = DatasetPullProcessResponse.create();
        processResponse.setIsUpdate(false);
        return processResponse;
    }

    /**
     * 查询hive数据
     *
     * @param request  请求数据
     * @param response 返回数据
     */
    private void searchHive(DatasetPullRequest request, DatasetPullResponse response) {
        if (isFailed(response)) {
            return;
        }
        try {
            updateDatasetStatus(request.getDatasetId(), DatasetStatusEnum.HIVE);
            response.getProcessData().setHiveStartTime(DateUtil.getNow());
            DatasetPullProcessResponse processResponse = executeSearchHive(request);
            response.getProcessData().setHiveEndTime(DateUtil.getNow());
            response.getProcessData().setHiveStatus(DatasetPullResponse.DatasetPullStatus.getStatusString(processResponse.getStatus()));
            response.getProcessData().setHiveCount(getListSize(processResponse.getDataList()));
            log.info("查询hive执行完成 request={},response={}", JSON.toJSONString(request), JSON.toJSONString(processResponse));
            handleResponse(request, processResponse, response);
        } catch (Exception e) {
            response.setStatus(DatasetPullResponse.DatasetPullStatus.HIVE_ERROR);
            setResponseMessage(response, e.getMessage());
            log.info("查询hive失败,param={},msg={}", JSON.toJSONString(request), e.getMessage(), e);
        } finally {
            // 更新数据集处理信息
            updateDatasetProcess(request.getDatasetId(), response.getProcessData());
        }
    }

    /**
     * 执行查询hive数据，默认返回空数据，各模版自己实现
     *
     * @param request 请求数据
     * @return 查询hive结果
     */
    protected DatasetPullProcessResponse executeSearchHive(DatasetPullRequest request) {
        Map<String, DatasetSqlParam> sqlMap = Lion.getMap(ConfigUtil.getAppkey(), LionConstants.DATA_PULL_HIVE_SQL_CONFIG, DatasetSqlParam.class);
        DatasetSqlParam sqlParam = sqlMap.get(getName());
        String sql = buildSql(request, sqlParam);
        log.info("拼接sql完成,request={},sql={}", JSONObject.toJSONString(request), sql);
        AsyncTalosClient client = null;
        try {
            client = hiveRequestServiceProxy.getClient();
            QueryResult queryResult = hiveRequestServiceProxy.invoke(client, sql);
            if (queryResult != null && queryResult.getResultSize() > 0) {
                List<Map<String, String>> result = new ArrayList<>();
                List<Column> headList = queryResult.getColumns();
                List<List<Object>> dataList = fetchData(queryResult);
                for (List<Object> data : dataList) {
                    Map<String, String> map = new HashMap<>();
                    for (int index = 0; index < headList.size(); index++) {
                        map.put(headList.get(index).getName(), data.get(index).toString());
                    }
                    result.add(map);
                }
                DatasetPullProcessResponse processResponse = DatasetPullProcessResponse.searchSuccess(result);
                setIsUpdate(processResponse);
                return processResponse;
            }
            if (queryResult == null) {
                DatasetPullProcessResponse.fail(DatasetPullResponse.DatasetPullStatus.HIVE_ERROR, "查询结果为空");
            }
            return DatasetPullProcessResponse.fail(DatasetPullResponse.DatasetPullStatus.HIVE_ERROR, "通过指定条件没有拉取到数据信息");
        } catch (Exception e) {
            log.error("查询hive失败,msg={}", e.getMessage(), e);
            return DatasetPullProcessResponse.fail(DatasetPullResponse.DatasetPullStatus.HIVE_ERROR, e.getMessage());
        } finally {
            if (client != null) {
                hiveRequestServiceProxy.closeClient(client);
            }
        }
    }

    protected void setIsUpdate(DatasetPullProcessResponse processResponse) {
        processResponse.setIsUpdate(true);
    }

    protected List<List<Object>> fetchData(QueryResult queryResult) throws Exception {
        return queryResult.fetchMany(Lion.getInt(ConfigUtil.getAppkey(), LionConstants.DATA_PULL_HIVE_MAX_SIZE, 1000));
    }

    protected String buildSql(DatasetPullRequest request, DatasetSqlParam sqlParam) {
        Map<String, SceneFiledWithCodeDTO> sceneFiledMap = getSceneFiledMap();
        List<String> conditionList = buildConditionSql(request, sceneFiledMap);
        List<String> resultList = buildResultSql(request, sceneFiledMap);
        String conditionSql = buildSql(sqlParam.getDefaultCondition(), conditionList, " and ");
        String resultSql = buildSql(sqlParam.getDefaultResult(), resultList, ", ");
        String finalSql = sqlParam.getSql().replace("#{condition}", conditionSql).replace("#{result}", resultSql);
        if (request.getConditionConfig().getMaxScale() != null) {
            finalSql += String.format(" LIMIT %s", request.getConditionConfig().getMaxScale());
        } else {
            finalSql += " LIMIT " + sqlParam.getLimit();
        }
        return finalSql;
    }

    /**
     * 调用AC
     *
     * @param request  请求数据
     * @param response 返回数据
     */
    private void invokeAc(DatasetPullRequest request, DatasetPullResponse response) {
        if (isFailed(response)) {
            return;
        }
        try {
            updateDatasetStatus(request.getDatasetId(), DatasetStatusEnum.AC);
            response.getProcessData().setAcStartTime(DateUtil.getNow());
            DatasetPullProcessResponse processResponse = executeInvokeAc(request);
            response.getProcessData().setAcEndTime(DateUtil.getNow());
            response.getProcessData().setAcStatus(DatasetPullResponse.DatasetPullStatus.getStatusString(processResponse.getStatus()));
            response.getProcessData().setAcSuccessCount(getListSize(processResponse.getSuccessDataList()));
            response.getProcessData().setAcFailureCount(getListSize(processResponse.getFailDataList()));
            log.info("查询AC执行完成,request={},response={}", JSON.toJSONString(request), JSON.toJSONString(processResponse));
            handleResponse(request, processResponse, response);
        } catch (Exception e) {
            response.setStatus(DatasetPullResponse.DatasetPullStatus.AC_ERROR);
            setResponseMessage(response, e.getMessage());
            log.info("查询AC失败,param={},msg={}", JSON.toJSONString(request), e.getMessage(), e);
        } finally {
            updateDatasetProcess(request.getDatasetId(), response.getProcessData());
        }
    }

    /**
     * 执行调用AC，默认不调用接口，各模版自己实现
     *
     * @param request 请求数据
     * @return 调用AC结果
     */
    protected DatasetPullProcessResponse executeInvokeAc(DatasetPullRequest request) {
        DatasetPullProcessResponse processResponse = DatasetPullProcessResponse.processSuccess(request.getDataList());
        processResponse.setIsUpdate(false);
        return processResponse;
    }

    /**
     * 后处理
     *
     * @param request  请求数据
     * @param response 返回数据
     */
    private void postProcess(DatasetPullRequest request, DatasetPullResponse response) {
        if (isFailed(response)) {
            return;
        }
        try {
            updateDatasetStatus(request.getDatasetId(), DatasetStatusEnum.PROCESS);
            response.getProcessData().setPostProcessStartTime(DateUtil.getNow());
            DatasetPullProcessResponse processResponse = executePostProcess(request);
            response.getProcessData().setPostProcessEndTime(DateUtil.getNow());
            response.getProcessData().setPostProcessStatus(DatasetPullResponse.DatasetPullStatus.getStatusString(processResponse.getStatus()));
            response.getProcessData().setPostProcessSuccessCount(getListSize(processResponse.getSuccessDataList()));
            response.getProcessData().setPostProcessFailureCount(getListSize(processResponse.getFailDataList()));
            log.info("数据后处理执行完成,request={},response={}", JSON.toJSONString(request), JSON.toJSONString(processResponse));
            handleResponse(request, processResponse, response);
        } catch (Exception e) {
            response.setStatus(DatasetPullResponse.DatasetPullStatus.AC_ERROR);
            setResponseMessage(response, e.getMessage());
            log.info("数据后处理失败,param={},msg={}", JSON.toJSONString(request), e.getMessage(), e);
        } finally {
            updateDatasetProcess(request.getDatasetId(), response.getProcessData());
        }
    }

    /**
     * 执行后处理，默认不处理，各模版自己实现
     *
     * @param request 请求数据
     * @return 数据加工结果
     */
    protected DatasetPullProcessResponse executePostProcess(DatasetPullRequest request) {
        DatasetPullProcessResponse processResponse = DatasetPullProcessResponse.processSuccess(request.getDataList());
        processResponse.setIsUpdate(false);
        return processResponse;
    }

    /**
     * 获取名称
     *
     * @return 名称
     */
    public abstract String getName();

    private void updateDatasetStatus(Long datasetId, DatasetStatusEnum datasetStatus) {
        updateDataset(datasetId, datasetStatus, null, null);
    }

    private void updateDatasetProcess(Long datasetId, DatasetProcessParam processParam) {
        updateDatasetProcess(datasetId, processParam, false);
    }

    private void updateDatasetProcess(Long datasetId, DatasetProcessParam processParam, boolean isUpdate) {
        updateDataset(datasetId, null, processParam, isUpdate);
    }

    private void updateDataset(Long datasetId, DatasetStatusEnum datasetStatus, DatasetProcessParam processParam, Boolean isUpdate) {
        EvalDatasetPo evalDatasetPo = new EvalDatasetPo();
        evalDatasetPo.setId(datasetId);
        if (datasetStatus != null) {
            evalDatasetPo.setStatus(datasetStatus.getCode());
        }
        if (processParam != null) {
            if (isUpdate) {
                ZebraForceMasterHelper.forceMasterInLocalContext();
                EvalDatasetPo oldDataset = evalDatasetGeneratorService.getById(datasetId);
                ZebraForceMasterHelper.clearLocalContext();
                evalDatasetPo.setProcess(updateProcess(oldDataset.getProcess(), processParam));
            } else {
                evalDatasetPo.setProcess(JSON.toJSONString(processParam));
            }
        }
        evalDatasetPo.setGmtModified(new Date());
        evalDatasetGeneratorService.updateById(evalDatasetPo);
    }

    private String updateProcess(String oldProcess, DatasetProcessParam processParam) {
        DatasetProcessParam oldProcessParam;
        if (StringUtils.isBlank(oldProcess)) {
            oldProcessParam = new DatasetProcessParam();
        } else {
            oldProcessParam = JSON.parseObject(oldProcess, DatasetProcessParam.class);
        }
        return JSON.toJSONString(updateProcess(oldProcessParam, processParam));
    }

    private DatasetProcessParam updateProcess(DatasetProcessParam oldProcessParam, DatasetProcessParam processParam) {
        try {
            // 获取processParam的所有属性
            Field[] fields = processParam.getClass().getDeclaredFields();
            // 遍历属性，将processParam中不为空的属性值赋值给oldProcess的对应属性
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(processParam);
                if (value != null) {
                    field.set(oldProcessParam, value);
                }
            }
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
            log.error("更新process失败,processParam={},oldProcess={},msg={}", JSON.toJSONString(processParam), oldProcessParam, e.getMessage(), e);
        }
        return oldProcessParam;
    }

    protected boolean isInterrupt(Long datasetId) {
        try {
            Long cache = redisStoreClient.get(new StoreKey(RedisConstants.DATASET_INTERRUPT, datasetId));
            if (cache != null) {
                log.info("检测到数据集拉取任务被删除，执行中断,datasetId={}", datasetId);
                return true;
            }
            return false;
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
            log.error("获取数据集拉取中断标识失败,datasetId={},msg={}", datasetId, e.getMessage(), e);
        }
        return false;
    }

    private void handleResponse(DatasetPullRequest request, DatasetPullProcessResponse processResponse, DatasetPullResponse response) {
        if (CollectionUtils.isNotEmpty(processResponse.getDataList())) {
            response.setDataList(buildDatasetDetail(request, processResponse.getDataList()));
        }
        if (CollectionUtils.isNotEmpty(processResponse.getSuccessDataList())) {
            response.setDataList(processResponse.getSuccessDataList());
        }
        if (processResponse.getProcessData() != null) {
            response.setProcessData(updateProcess(response.getProcessData(), processResponse.getProcessData()));
        }
        if (processResponse.getStatus() != null) {
            response.setStatus(processResponse.getStatus());
        }
        if (processResponse.getIsUpdate() != null && processResponse.getIsUpdate()) {
            // 保存数据集详情信息
            saveOrUpdateDatasetDetail(response);
        }
        // 将上一步的保存结果作为下一次的请求参数
        request.setDataList(response.getDataList());
    }

    private int getListSize(List<?> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return list.size();
    }

    protected void setResponseMessage(DatasetPullResponse response, String failMessage) {
        if (response.getProcessData() == null) {
            response.setProcessData(new DatasetProcessParam());
        }
        response.getProcessData().setFailMessage(failMessage);
    }

    private void saveOrUpdateDatasetDetail(DatasetPullResponse response) {
        List<EvalDatasetDetailPo> detailList = response.getDataList();
        CommonUtils.checkEval(CollectionUtils.isNotEmpty(detailList), "通过指定条件没有拉取到数据信息");
        evalDatasetDetailGeneratorService.saveOrUpdateBatch(detailList, Lion.getInt(ConfigUtil.getAppkey(), LionConstants.UPDATE_BATCH_SIZE, 100));
    }

    private List<EvalDatasetDetailPo> buildDatasetDetail(DatasetPullRequest request, List<Map<String, String>> dataList) {
        Date date = new Date();
        return dataList.stream().map(data -> {
            EvalDatasetDetailPo detailPo = new EvalDatasetDetailPo();
            detailPo.setDatasetId(request.getDatasetId());
            detailPo.setContent(JSON.toJSONString(data));
            detailPo.setStatus(DatasetDetailStatusEnum.VALID.getCode());
            detailPo.setCreatorMis(request.getCreateMis());
            detailPo.setUpdaterMis(request.getCreateMis());
            detailPo.setGmtCreated(date);
            detailPo.setGmtModified(date);
            return detailPo;
        }).collect(Collectors.toList());
    }

    protected String updateContent(String content, Map<String, String> extraData) {
        if (MapUtils.isNotEmpty(extraData)) {
            return content;
        }
        Map<String, String> contentMap = new HashMap<>();
        if (StringUtils.isNotBlank(content)) {
            contentMap = JSON.parseObject(content, new TypeReference<Map<String, String>>() {
            });
        }
        contentMap.putAll(extraData);
        return JSON.toJSONString(contentMap);
    }

    protected Map<String, SceneFiledWithCodeDTO> getSceneFiledMap() {
        String dataPullConfig = Lion.getString(ConfigUtil.getAppkey(), LionConstants.DATA_PULL_CONFIG_SOURCE);
        Map<String, List<SceneFiledWithCodeDTO>> configMap = JSON.parseObject(dataPullConfig, new TypeReference<Map<String, List<SceneFiledWithCodeDTO>>>() {
        });
        return configMap.values().stream().flatMap(Collection::stream).collect(Collectors.toMap(SceneFiledDTO::getValue, Function.identity()));
    }

    protected DatasetPullProcessResponse getThirdContactPartyData(DatasetPullRequest request) {
        DatasetPullProcessResponse processResponse = DatasetPullProcessResponse.create();
        String acConfig = Lion.getString(ConfigUtil.getAppkey(), LionConstants.DATA_PULL_AC_CONFIG);
        Map<String, List<AcInvokeParam>> acInvokeParamMap = JSON.parseObject(acConfig, new TypeReference<Map<String, List<AcInvokeParam>>>() {
        });
        List<AcInvokeParam> acInvokeList = acInvokeParamMap.get(getName());
        List<EvalDatasetDetailPo> successList = new ArrayList<>();
        List<EvalDatasetDetailPo> failList = new ArrayList<>();
        Map<String, SceneFiledWithCodeDTO> sceneFiledMap = getSceneFiledMap();
        Set<String> needPullCode = getNeedPullCode(request.getConditionConfig().getResultField(), sceneFiledMap);
        for (EvalDatasetDetailPo detail : request.getDataList()) {
            Map<String, Object> resultMap = JSON.parseObject(detail.getContent());
            try {
                Map<String, Object> allParamMap = new HashMap<>(resultMap);
                for (AcInvokeParam acInvoke : acInvokeList) {
                    // 检测中断
                    if (isInterrupt(request.getDatasetId())) {
                        return DatasetPullProcessResponse.fail(DatasetPullResponse.DatasetPullStatus.INTERRUPT, "数据集拉取任务被中断");
                    }
                    Map<String, String> outputMap = invokeAc(request, sceneFiledMap, needPullCode, allParamMap, acInvoke);
                    if (MapUtils.isNotEmpty(outputMap)) {
                        resultMap.putAll(outputMap);
                    }
                    // 每次AC接口调用后暂停1s，防止过快调用业务方接口
                    try {
                        Thread.sleep(Lion.getInt(ConfigUtil.getAppkey(), LionConstants.AC_INVOKE_SLEEP_TIME, 1000));
                    } catch (Exception e) {
                        Cat.logError(e);
                        log.error("AC调用暂停异常", e);
                    }
                }
                detail.setContent(JSON.toJSONString(resultMap));
                successList.add(detail);
                evalDatasetDetailGeneratorService.updateById(detail);
            } catch (Exception e) {
                detail.setStatus(DatasetDetailStatusEnum.AC_FAILED.getCode());
                evalDatasetDetailGeneratorService.updateById(detail);
                log.error("executeInvokeAc error,detail={},request={},msg={}", JSON.toJSONString(detail), JSON.toJSONString(request), e.getMessage(), e);
                failList.add(detail);
            }
        }
        processResponse.setSuccessDataList(successList);
        processResponse.setFailDataList(failList);
        processResponse.setIsUpdate(false);
        return processResponse;
    }

    private Map<String, String> invokeAc(DatasetPullRequest request, Map<String, SceneFiledWithCodeDTO> sceneFiledMap, Set<String> needPullCode, Map<String, Object> allParamMap, AcInvokeParam acInvoke) {
        // key：AC接口code  value：参数code
        Map<String, String> inputCodeMap = new HashMap<>();
        // key：参数code value：参数值
        Map<String, Object> inputCodeValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(acInvoke.getInputParamList())) {
            inputCodeMap = acInvoke.getInputParamList().stream().collect(Collectors.toMap(AcInvokeParam.Param::getCode, AcInvokeParam.Param::getSourceCode));
            inputCodeValueMap.putAll(inputCodeMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, entry -> getValueByCode(request, sceneFiledMap, acInvoke.getInterfaceId(), entry, allParamMap))));
            // 校验入参
            acInvoke.getInputParamList().forEach(input -> checkParamValue(input, inputCodeValueMap.get(input.getSourceCode())));
        }
        // key：AC接口code  value：参数值
        Map<String, Object> inputParamMap = new HashMap<>();
        if (MapUtils.isNotEmpty(inputCodeMap)) {
            inputParamMap = inputCodeMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> inputCodeValueMap.get(entry.getValue())));
        }
        // 目前只有Object类型的接口
        Map<String, Object> outputParamMap = acInvokeRemoteService.invokeApi(inputParamMap, Long.parseLong(acInvoke.getInterfaceId()), acInvoke.getTimeOut() != null ? acInvoke.getTimeOut() : 10000);
        Map<String, String> outputMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(acInvoke.getOutputParamList())) {
            for (AcInvokeParam.Param outputParam : acInvoke.getOutputParamList()) {
                String code = outputParam.getCode();
                if (outputParam.getIsRequired() != null && outputParam.getIsRequired() && needPullCode.contains(code)) {
                    Object result = outputParamMap.get(code);
                    // 校验出参
                    checkParamValue(outputParam, result);
                    outputMap.put(code, String.valueOf(result));
                }
                allParamMap.put(code, outputParamMap.get(code));
            }
        }
        return outputMap;
    }

    private void checkParamValue(AcInvokeParam.Param param, Object result) {
        if (param.getIsCheck() != null && param.getIsCheck()) {
            String failMessage = StringUtils.isNotBlank(param.getCheckFailMessage()) ? param.getCheckFailMessage() : "【" + param.getName() + "】字段异常";
            if (StringUtils.isBlank(param.getCheckValue())) {
                CommonUtils.checkEval(result != null && !"null".equalsIgnoreCase(result.toString()), failMessage);
            } else {
                CommonUtils.checkEval(param.getCheckValue().equals(result.toString()), failMessage);
            }
        }
    }

    protected Object getValueByCode(DatasetPullRequest request, Map<String, SceneFiledWithCodeDTO> sceneFiledMap, String interfaceId, Map.Entry<String, String> inputCodeEntry, Map<String, Object> resultMap) {
        // 需要特殊处理的值
        String specialInterfaceId = Lion.getString(ConfigUtil.getAppkey(), LionConstants.DATA_PULL_INTERFACE_ID, "9951");
        if (specialInterfaceId.equals(interfaceId) && "startTime".equals(inputCodeEntry.getKey())) {
            String code = request.getConditionConfig().getBeginTime();
            if (DatasetFiledEnum.DEFAULT_TIME.getValue().equals(code)) {
                SceneFiledWithCodeDTO sceneFiled = sceneFiledMap.get(DatasetFiledEnum.ORDER_TIME.getValue());
                CommonUtils.checkEval(MapUtils.isNotEmpty(resultMap) && resultMap.containsKey(sceneFiled.getCode()), "数据集缺少订单时间字段");
                long orderTime = Long.parseLong(resultMap.get(sceneFiled.getCode()).toString());
                // 86400为一天的秒数，表示前一天时间
                return orderTime - 86400 * 1000;
            }
            SceneFiledWithCodeDTO sceneFiled = sceneFiledMap.get(code);
            CommonUtils.checkEval(Objects.nonNull(sceneFiled), "数据集缺少开始时间字段");
            return resultMap.get(sceneFiled.getCode());
        }
        if (specialInterfaceId.equals(interfaceId) && "endTime".equals(inputCodeEntry.getKey())) {
            String code = request.getConditionConfig().getEndTime();
            if (DatasetFiledEnum.DEFAULT_TIME.getValue().equals(code)) {
                SceneFiledWithCodeDTO sceneFiled = sceneFiledMap.get(DatasetFiledEnum.ORDER_TIME.getValue());
                long orderTime = Long.parseLong(resultMap.get(sceneFiled.getCode()).toString());
                // 86400为一天的秒数，表示后15天时间
                return orderTime + 15 * 86400 * 1000;
            }
            SceneFiledWithCodeDTO sceneFiled = sceneFiledMap.get(code);
            return resultMap.get(sceneFiled.getCode());
        }
        return resultMap.get(inputCodeEntry.getValue());
    }

    private Set<String> getNeedPullCode(List<String> resultField, Map<String, SceneFiledWithCodeDTO> sceneFiledMap) {
        if (CollectionUtils.isEmpty(resultField)) {
            return new HashSet<>();
        }
        return resultField.stream().map(sceneFiledMap::get).filter(Objects::nonNull).map(SceneFiledWithCodeDTO::getCode).collect(Collectors.toSet());
    }

    private String buildSql(String defaultSql, List<String> sqlList, String split) {
        String resultSql = StringUtils.isNotBlank(defaultSql) ? defaultSql : "";
        if (CollectionUtils.isNotEmpty(sqlList)) {
            if (StringUtils.isNotBlank(resultSql)) {
                resultSql += split;
            }
            resultSql += StringUtils.join(sqlList, split);
        }
        return resultSql;
    }

    protected List<String> buildConditionSql(DatasetPullRequest request, Map<String, SceneFiledWithCodeDTO> sceneFiledMap) {
        List<String> conditionList = new ArrayList<>();
        for (ConditionConfig.Condition condition : request.getConditionConfig().getCondition()) {
            SceneFiledWithCodeDTO sceneFiled = sceneFiledMap.get(condition.getCode());
            // 设置条件sql
            List<String> specialFieldConditionList = handleSpecialConditionField(request, condition);
            if (CollectionUtils.isNotEmpty(specialFieldConditionList)) {
                conditionList.addAll(specialFieldConditionList);
                continue;
            }
            String code = sceneFiled.getCode();
            if (StringUtils.isNotBlank(sceneFiled.getTemplate())) {
                code = String.format(sceneFiled.getTemplate(), code);
            }
            if (sceneFiled.getType() != null && SceneFiledTypeEnum.TIME.getCode() == sceneFiled.getType()) {
                conditionList.add(code + " between \"" + condition.getValue().get(0) + "\" and \"" + condition.getValue().get(1) + "\"");
            } else if (condition.getValue().size() > 1) {
                conditionList.add(code + " in (\"" + StringUtils.join(condition.getValue(), "\",\"") + "\")");
            } else {
                conditionList.add(code + " = \"" + condition.getValue().get(0) + "\"");
            }
        }
        return conditionList;
    }

    protected List<String> buildResultSql(DatasetPullRequest request, Map<String, SceneFiledWithCodeDTO> sceneFiledMap) {
        List<String> resultList = new ArrayList<>();
        for (String resultField : request.getConditionConfig().getResultField()) {
            SceneFiledWithCodeDTO sceneFiled = sceneFiledMap.get(resultField);
            if (sceneFiled.getFieldSource() != null && FieldSourceEnum.AC.getCode() == sceneFiled.getFieldSource()) {
                continue;
            }
            List<String> specialFieldConditionList = handleSpecialResultField(request, sceneFiled);
            if (CollectionUtils.isNotEmpty(specialFieldConditionList)) {
                resultList.addAll(specialFieldConditionList);
            }
            if (StringUtils.isNotBlank(sceneFiled.getTemplate())) {
                resultList.add(String.format(sceneFiled.getTemplate(), sceneFiled.getCode()));
            } else if (StringUtils.isNotBlank(sceneFiled.getPrefix())) {
                resultList.add(sceneFiled.getPrefix() + "." + sceneFiled.getCode());
            } else {
                resultList.add(sceneFiled.getCode());
            }
        }
        return resultList;
    }

    protected List<String> handleSpecialConditionField(DatasetPullRequest request, ConditionConfig.Condition condition) {
        return new ArrayList<>();
    }

    protected List<String> handleSpecialResultField(DatasetPullRequest request, SceneFiledWithCodeDTO sceneFiled) {
        return new ArrayList<>();
    }

    protected String getFaqCodeByIndex(int index) {
        switch (index) {
            case 1:
                return "first_category_id";
            case 2:
                return "second_category_id";
            case 3:
                return "third_category_id";
            case 4:
                return "fourth_category_id";
            case 5:
                return "fifth_category_id";
            default:
                return "question_id";
        }
    }

    private void mockData(DatasetPullRequest request) {
        String mockCondition = Lion.getString(ConfigUtil.getAppkey(), LionConstants.ONLINE_MOCK_DATA);
        if (StringUtils.isBlank(mockCondition)) {
            return;
        }
        // 通过Lion配置来mock无法获取的线上数据
        ConditionConfig conditionConfig = JSON.parseObject(mockCondition, ConditionConfig.class);
        if (conditionConfig != null) {
            if (CollectionUtils.isNotEmpty(request.getConditionConfig().getCondition()) && CollectionUtils.isNotEmpty(conditionConfig.getCondition())) {
                Map<String, ConditionConfig.Condition> conditionMap = request.getConditionConfig().getCondition().stream().collect(Collectors.toMap(ConditionConfig.Condition::getCode, Function.identity()));
                for (ConditionConfig.Condition condition : conditionConfig.getCondition()) {
                    if (conditionMap.containsKey(condition.getCode())) {
                        conditionMap.get(condition.getCode()).setValue(condition.getValue());
                    }
                }
            }
            if (StringUtils.isNotBlank(conditionConfig.getApplicationId()) && StringUtils.isNotBlank(request.getConditionConfig().getApplicationId())) {
                request.getConditionConfig().setWorkSpaceId(conditionConfig.getWorkSpaceId());
                request.getConditionConfig().setApplicationId(conditionConfig.getApplicationId());
                request.getConditionConfig().setVersionId(conditionConfig.getVersionId());
                request.getConditionConfig().setAiDaInputCodeList(conditionConfig.getAiDaInputCodeList());
                request.getConditionConfig().setAiDaOutputCodeList(conditionConfig.getAiDaOutputCodeList());
                request.getConditionConfig().setBeginTime(conditionConfig.getBeginTime());
                request.getConditionConfig().setEndTime(conditionConfig.getEndTime());
                request.getConditionConfig().setAiDaSystemCodeList(conditionConfig.getAiDaSystemCodeList());
            }
            // mock resultExtra
            if (CollectionUtils.isNotEmpty(conditionConfig.getResultExtra()) && CollectionUtils.isNotEmpty(request.getConditionConfig().getResultExtra())) {
                request.getConditionConfig().setResultExtra(conditionConfig.getResultExtra());
            }
        }
    }

    @Data
    public static class DatasetPullProcessResponse implements Serializable {
        private List<Map<String, String>> dataList;
        private List<EvalDatasetDetailPo> successDataList;
        private List<EvalDatasetDetailPo> failDataList;
        private DatasetProcessParam processData;
        private DatasetPullResponse.DatasetPullStatus status;
        /**
         * 是否需要更新数据库，默认为否
         */
        private Boolean isUpdate;

        public static DatasetPullProcessResponse create() {
            return searchSuccess(null);
        }

        public static DatasetPullProcessResponse searchSuccess(List<Map<String, String>> dataList) {
            DatasetPullProcessResponse processResponse = new DatasetPullProcessResponse();
            processResponse.setStatus(DatasetPullResponse.DatasetPullStatus.SUCCESS);
            processResponse.setDataList(dataList);
            return processResponse;
        }

        public static DatasetPullProcessResponse processSuccess(List<EvalDatasetDetailPo> successDataList) {
            return processSuccess(successDataList, null);
        }

        public static DatasetPullProcessResponse processSuccess(List<EvalDatasetDetailPo> successDataList, List<EvalDatasetDetailPo> failDataList) {
            DatasetPullProcessResponse processResponse = new DatasetPullProcessResponse();
            processResponse.setStatus(DatasetPullResponse.DatasetPullStatus.SUCCESS);
            processResponse.setSuccessDataList(successDataList);
            processResponse.setFailDataList(failDataList);
            return processResponse;
        }

        public static DatasetPullProcessResponse fail(DatasetPullResponse.DatasetPullStatus status, String failMessage) {
            DatasetPullProcessResponse processResponse = new DatasetPullProcessResponse();
            processResponse.setStatus(status);
            DatasetProcessParam processParam = new DatasetProcessParam();
            processParam.setFailMessage(failMessage);
            processResponse.setProcessData(processParam);
            return processResponse;
        }
    }
}
