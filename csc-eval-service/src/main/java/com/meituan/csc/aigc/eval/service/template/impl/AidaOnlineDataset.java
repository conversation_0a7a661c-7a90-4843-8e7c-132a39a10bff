package com.meituan.csc.aigc.eval.service.template.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.dto.aida.AidaRobotInfo;
import com.meituan.csc.aigc.eval.dto.dataset.SceneFiledWithCodeDTO;
import com.meituan.csc.aigc.eval.enums.dataset.DatasetPullTemplateEnum;
import com.meituan.csc.aigc.eval.helper.PullDataThreadLocalHelper;
import com.meituan.csc.aigc.eval.param.dataset.DatasetPullRequest;
import com.meituan.csc.aigc.eval.param.dataset.DatasetPullResponse;
import com.meituan.csc.aigc.eval.service.AidaExecuteService;
import com.meituan.csc.aigc.eval.service.template.DatasetTemplate;
import com.meituan.inf.xmdlog.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AidaOnlineDataset extends DatasetTemplate {

    @Autowired
    private AidaExecuteService aidaExecuteService;

    @Override
    protected DatasetPullProcessResponse executePreProcess(DatasetPullRequest request) {
        try {
            // 线下环境已经mock，无需再去获取配置，即使获取了拿不到
            if (StringUtils.isNotBlank(Lion.getString(ConfigUtil.getAppkey(), LionConstants.ONLINE_MOCK_DATA))) {
                return DatasetPullProcessResponse.create();
            }
            AidaRobotInfo aidaRobotInfo = aidaExecuteService.getAidaRobotInfo(request.getConditionConfig().getVersionId(), request.getConditionConfig().getApplicationId());
            request.getConditionConfig().setAiDaSystemCodeList(aidaRobotInfo.getSystemList());
            request.getConditionConfig().setAiDaInputCodeList(aidaRobotInfo.getInputList());
            request.getConditionConfig().setAiDaOutputCodeList(aidaRobotInfo.getOutputList());
            return DatasetPullProcessResponse.create();
        } catch (Exception e) {
            log.info("预处理失败,param={},msg={}", JSON.toJSONString(request), e.getMessage(), e);
            return DatasetPullProcessResponse.fail(DatasetPullResponse.DatasetPullStatus.PRE_HANDLE_ERROR, e.getMessage());
        }
    }

    @Override
    protected List<String> buildConditionSql(DatasetPullRequest request, Map<String, SceneFiledWithCodeDTO> sceneFiledMap) {
        List<String> conditionList = new ArrayList<>();
        conditionList.add("app_id = '" + request.getConditionConfig().getApplicationId() + "'");
        // 当前没有版本信息，后续上线后可以打开开关
        String robotTemplate = Lion.getString(ConfigUtil.getAppkey(), LionConstants.ROBOT_ID_TEMPLATE);
        if (StringUtils.isNotBlank(robotTemplate)) {
            conditionList.add(String.format(robotTemplate, request.getConditionConfig().getVersionId()));
        }
        conditionList.add(String.format("created_at between '%s' and '%s'", request.getConditionConfig().getBeginTime(), request.getConditionConfig().getEndTime()));
        return conditionList;
    }

    @Override
    protected List<String> buildResultSql(DatasetPullRequest request, Map<String, SceneFiledWithCodeDTO> sceneFiledMap) {
        List<String> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getConditionConfig().getAiDaSystemCodeList())) {
            // 系统变量不为空，说明是多轮类型的，增加会话ID和输入内容查询项
            resultList.add("conversation_id as `会话ID`");
            resultList.add("query as `输入内容`");
        }
        if (CollectionUtils.isNotEmpty(request.getConditionConfig().getAiDaInputCodeList())) {
            for (String code : request.getConditionConfig().getAiDaInputCodeList()) {
                resultList.add(String.format("get_json_object(inputs, '$.%s') %s", code, code));
            }
        }

        boolean isAutoInspect = PullDataThreadLocalHelper.isAutoInspect();
        List<String> outputCodes = request.getConditionConfig().getAiDaOutputCodeList();

        if (CollectionUtils.isNotEmpty(outputCodes)) {
            for (String code : outputCodes) {
                resultList.add(String.format("get_json_object(answer, '$.%s') %s", code, code));
            }
        }
        if (isAutoInspect || CollectionUtils.isEmpty(outputCodes)) {
            resultList.add("answer as `输出`");
        }
        return resultList;
    }

    @Override
    public String getName() {
        return DatasetPullTemplateEnum.AIDA_ONLINE.getName();
    }
}
