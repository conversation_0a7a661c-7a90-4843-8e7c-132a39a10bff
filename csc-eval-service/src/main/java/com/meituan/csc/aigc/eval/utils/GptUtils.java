package com.meituan.csc.aigc.eval.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.param.gpt.ChatParamConfig;
import com.meituan.inf.xmdlog.ConfigUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class GptUtils {

    public static Map<String, Map<String, String>> getParamConfigMap() {
        Map<String, Map<String, String>> defaultParamMap = new HashMap<>();
        Map<String, String> defaultMap = new HashMap<>();
        defaultMap.put("maxtoken", "1024");
        defaultMap.put("frequencyPenalty", "0.2");
        defaultMap.put("presencePenalty", "0");
        defaultMap.put("topP", "0.5");
        defaultMap.put("temperature", "0");
        defaultParamMap.put("default", defaultMap);
        String param = Lion.getString(ConfigUtil.getAppkey(), LionConstants.GPT_DEFAULT_PARAM_MAP);

        return StringUtils.isNotBlank(param) ? JSON.parseObject(param, new TypeReference<Map<String, Map<String, String>>>() {
        }) : defaultParamMap;
    }

    public static Map<String, String> getDefaultParamConfigMap() {
        return getParamConfigMap().get("default");
    }
}
