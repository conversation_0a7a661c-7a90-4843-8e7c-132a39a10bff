package com.meituan.csc.aigc.eval.utils;

import com.meituan.csc.aigc.eval.config.http.RestTemplateContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Map;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class HttpClientHelper {

    private static final String FORMAT = "%s_%s";

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 获取RestTemplate对象，优先级如下：
     * 1、"tenantId_model"
     * 2、"tenantId_all"
     * 3、默认restTemplate对象
     *
     * @param tenantId 租户ID
     * @param model 模型
     * @return RestTemplate对象
     */
    public RestTemplate getRestTemplate(String tenantId, String model) {
        // 获取RestTemplate上下文映射
        Map<String, RestTemplate> restTemplateContextMap = RestTemplateContext.getRestTemplateContextMap();
        // 如果映射为空，或者tenantId或model为空，则返回默认的restTemplate
        if (MapUtils.isEmpty(restTemplateContextMap) || StringUtils.isBlank(tenantId) || StringUtils.isBlank(model)) {
            log.info("获取到restTemplate={},tenantId={},model={}", restTemplate, tenantId, model);
            return restTemplate;
        }
       // 使用Map的getOrDefault方法，如果映射中存在tenantId_model，则返回对应的RestTemplate，否则返回tenantId对应的RestTemplate，如果都不存在，则返回默认的restTemplate
        RestTemplate result = restTemplateContextMap.getOrDefault(String.format(FORMAT, tenantId, model), restTemplateContextMap.getOrDefault(tenantId, restTemplate));
        log.info("获取到restTemplate={},tenantId={},model={}", result, tenantId, model);
        return result;
    }


    public RestTemplate getDefaultRestTemplate() {
        return restTemplate;
    }

}
