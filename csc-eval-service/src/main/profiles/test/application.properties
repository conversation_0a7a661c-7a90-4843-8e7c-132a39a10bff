management.server.port=8080
management.endpoints.web.base-path=/monitor
management.endpoints.web.path-mapping.health=/alive

server.port=8080

sso.auth-url=http://api.upm-in.sankuai.com
sso.sso-url=https://sso.sankuai.com
sso.sso-api-url=http://api.sso-in.sankuai.com

sso.clientId=55684f3d01

dx.appKey=21188g2105S04005
dx.targetUrl=http://api.xm.test.sankuai.com/api/pub/push
dx.uid=137439372818

http.maxTotal=500
http.defaultMaxPerRoute=100
http.connectTimeout=3000
http.connectionRequestTimeout=6000
http.socketTimeout=30000
http.validateAfterInactivity=2000

eval.service.domain=eval.csp.test.sankuai.com

spring.servlet.multipart.max-file-size=300MB
spring.servlet.multipart.max-request-size=300MB

# tenantId
call.out.tenantId=c12946f2-0b49-11ea-9cf3-00223e9f3d04

# es
es.case.analysis.cluster.name=csp_llmcaseanalysis_default
es.case.analysis.appkey=com.sankuai.csccratos.eval.service