<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.csc.aigc.eval.dao.mapper.ApplicationConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meituan.csc.aigc.eval.dao.entity.ApplicationConfigPo">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="model_config" property="modelConfig" />
        <result column="prompt" property="prompt" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="creator_mis" property="creatorMis" />
        <result column="updater_mis" property="updaterMis" />
        <result column="robot_id" property="robotId" />
        <result column="source" property="source" />
        <result column="type" property="type" />
        <result column="platform_workspace" property="platformWorkspace" />
        <result column="platform_app" property="platformApp" />
        <result column="extra" property="extra" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, description, model_config, prompt, gmt_created, gmt_modified, creator_mis, updater_mis, robot_id, source, type, platform_workspace, platform_app, extra
    </sql>

</mapper>
