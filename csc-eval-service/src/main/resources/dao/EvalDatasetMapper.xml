<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.csc.aigc.eval.dao.mapper.EvalDatasetMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meituan.csc.aigc.eval.dao.entity.EvalDatasetPo">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="platform_type" property="platformType" />
        <result column="platform_workspace" property="platformWorkspace" />
        <result column="platform_app" property="platformApp" />
        <result column="scene" property="scene" />
        <result column="ability" property="ability" />
        <result column="description" property="description" />
        <result column="source" property="source" />
        <result column="status" property="status" />
        <result column="file" property="file" />
        <result column="extra" property="extra" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="creator_mis" property="creatorMis" />
        <result column="updater_mis" property="updaterMis" />
        <result column="application_list" property="applicationList" />
        <result column="create_method" property="createMethod" />
        <result column="user_source" property="userSource" />
        <result column="process" property="process" />
        <result column="reference_id" property="referenceId" />
        <result column="type" property="type" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, platform_type, platform_workspace, platform_app, scene, ability, description, source, status, file, extra, gmt_created, gmt_modified, creator_mis, updater_mis, application_list, create_method, user_source, process, reference_id, type
    </sql>

</mapper>
