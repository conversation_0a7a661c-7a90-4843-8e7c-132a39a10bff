<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.csc.aigc.eval.dao.mapper.SessionAnalysisMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meituan.csc.aigc.eval.dao.entity.SessionAnalysisPo">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="data_index" property="dataIndex" />
        <result column="session_id" property="sessionId" />
        <result column="message_id" property="messageId" />
        <result column="business_scene" property="businessScene" />
        <result column="app_id" property="appId" />
        <result column="question_tags" property="questionTags" />
        <result column="analysis_details" property="analysisDetails" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="task_type" property="taskType" />
        <result column="manual_label_task_id" property="manualLabelTaskId" />
        <result column="session_time" property="sessionTime" />
        <result column="tag_group_id" property="tagGroupId" />
        <result column="tag_group_name" property="tagGroupName" />
        <result column="comment" property="comment" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, data_index, session_id, message_id, business_scene, app_id, question_tags, analysis_details, create_time, update_time, task_type, manual_label_task_id, session_time, tag_group_id, tag_group_name, comment
    </sql>

</mapper>
