<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.csc.aigc.eval.dao.mapper.WorkbenchMessageReviewRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meituan.csc.aigc.eval.dao.entity.WorkbenchMessageReviewRecordPo">
        <id column="id" property="id" />
        <result column="message_id" property="messageId" />
        <result column="session_id" property="sessionId" />
        <result column="review_mis" property="reviewMis" />
        <result column="review_name" property="reviewName" />
        <result column="review_content" property="reviewContent" />
        <result column="like_number" property="likeNumber" />
        <result column="add_time" property="addTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, message_id, session_id, review_mis, review_name, review_content, like_number, add_time, update_time, is_deleted
    </sql>

</mapper>
