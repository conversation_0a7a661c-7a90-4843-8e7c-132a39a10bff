<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.csc.aigc.eval.dao.mapper.WorkbenchSessionInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meituan.csc.aigc.eval.dao.entity.WorkbenchSessionInfoPo">
        <id column="id" property="id" />
        <result column="session_id" property="sessionId" />
        <result column="llm_session_id" property="llmSessionId" />
        <result column="platform_type" property="platformType" />
        <result column="platform_workspace" property="platformWorkspace" />
        <result column="platform_app" property="platformApp" />
        <result column="session_gmt_created" property="sessionGmtCreated" />
        <result column="session_turn" property="sessionTurn" />
        <result column="session_order_id" property="sessionOrderId" />
        <result column="session_user_id" property="sessionUserId" />
        <result column="status" property="status" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="creator_mis" property="creatorMis" />
        <result column="updater_mis" property="updaterMis" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, session_id, llm_session_id, platform_type, platform_workspace, platform_app, session_gmt_created, session_turn, session_order_id, session_user_id, status, gmt_created, gmt_modified, creator_mis, updater_mis
    </sql>

</mapper>
