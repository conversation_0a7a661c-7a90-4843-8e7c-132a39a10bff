package com.meituan.csc.aigc.eval.service;

import com.meituan.csc.aigc.eval.Application;
import com.meituan.csc.aigc.eval.dao.service.generator.ApplicationConfigGeneratorService;
import com.meituan.csc.aigc.eval.dao.service.generator.AutoInspectConfigGeneratorService;
import com.meituan.csc.aigc.eval.dao.service.generator.AutoInspectDetailGeneratorService;
import com.meituan.csc.aigc.eval.job.EvalTaskJob;
import com.meituan.csc.aigc.eval.service.impl.AutoInspectServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = {Application.class})
@RunWith(SpringRunner.class)
public class AutoCollect {

    @Autowired
    private AutoInspectServiceImpl autoInspectServiceImpl;

    @Autowired
    private ApplicationConfigGeneratorService applicationConfigGeneratorService;

    @Autowired
    private AutoInspectDetailGeneratorService autoInspectDetailGeneratorService;

    @Autowired
    private AutoInspectConfigGeneratorService autoInspectConfigGeneratorService;

    @Autowired
    private EvalTaskJob evalTaskJob;

    @Test
    public void test() {

    }

}
