package com.meituan.csc.aigc.eval.service.mock.generate;

import com.meituan.csc.aigc.eval.dao.entity.MetricConfigPo;
import com.meituan.csc.aigc.eval.dao.service.generator.MetricConfigGeneratorService;
import com.meituan.csc.aigc.eval.param.metric.MetricParam;
import com.meituan.csc.aigc.eval.service.ApplicationExecuteService;
import com.meituan.csc.aigc.eval.service.PermissionService;
import com.meituan.csc.aigc.eval.service.impl.MetricExecuteServiceImpl;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
@Ignore
public class MetricExecuteServiceImplTest {

    @InjectMocks
    private MetricExecuteServiceImpl metricExecuteService;

    @Mock
    private MetricConfigGeneratorService metricConfigGeneratorService;

    @Mock
    private ApplicationExecuteService applicationExecuteService;

    @Mock
    private PermissionService permissionService;

    private MetricParam metricParam;

    @Before
    public void setUp() {
        metricParam = new MetricParam();
        metricParam.setName("test");
        metricParam.setStartScore(1);
        metricParam.setEndScore(2);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testCreateNormal() {
        when(metricConfigGeneratorService.getByCondition(any())).thenReturn(Collections.emptyList());
        when(applicationExecuteService.create(any())).thenReturn(1L);
        when(metricConfigGeneratorService.save(any())).thenReturn(true);

        Long result = metricExecuteService.create(metricParam);

        assertEquals(1L, result.longValue());
    }

    /**
     * 测试异常情况1：metricParam 的 startScore 和 endScore 属性设置不正确
     */
    @Test(expected = IllegalArgumentException.class)
    public void testCreateException1() {
        metricParam.setStartScore(2);
        metricParam.setEndScore(1);

        metricExecuteService.create(metricParam);
    }

    /**
     * 测试异常情况2：数据库中已存在相同名称的 MetricConfigPo 对象
     */
    @Test(expected = IllegalArgumentException.class)
    public void testCreateException2() {
        when(metricConfigGeneratorService.getByCondition(any())).thenReturn(Collections.singletonList(new MetricConfigPo()));

        metricExecuteService.create(metricParam);
    }

    /**
     * 测试异常情况3：新创建的应用的 id 为 null
     */
    @Test(expected = IllegalArgumentException.class)
    public void testCreateException3() {
        when(metricConfigGeneratorService.getByCondition(any())).thenReturn(Collections.emptyList());
        when(applicationExecuteService.create(any())).thenReturn(null);

        metricExecuteService.create(metricParam);
    }
}
