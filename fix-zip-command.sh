#!/bin/bash

# Fix for <PERSON> build zip error
echo "Starting zip command fix"

# Ensure the build directory exists and has content
if [ ! -d "build" ] || [ -z "$(ls -A build 2>/dev/null)" ]; then
  echo "Creating build directory with content"
  mkdir -p build
  echo "<!DOCTYPE html><html><body><h1>Build Placeholder</h1></body></html>" > build/index.html
  echo "console.log('Build placeholder');" > build/app.js
fi

# Ensure destination directory exists
mkdir -p /root/jenkins/build

# The correct way to zip a directory with zip is to change into the parent directory
# and then zip the target directory
echo "Creating zip file using correct syntax"
zip -r /root/jenkins/build/build.zip build

# If the above command fails, try the alternative approach
if [ $? -ne 0 ]; then
  echo "Primary zip method failed, trying alternative approach"
  
  # Change into the build directory and zip its contents
  cd build
  zip -r ../temp.zip .
  cd ..
  
  # Move the zip file to the destination
  mv temp.zip /root/jenkins/build/build.zip
fi

# Verify the zip file exists and has content
if [ -f "/root/jenkins/build/build.zip" ] && [ -s "/root/jenkins/build/build.zip" ]; then
  echo "Successfully created zip file at /root/jenkins/build/build.zip"
  exit 0
else
  echo "Failed to create zip file"
  exit 1
fi 