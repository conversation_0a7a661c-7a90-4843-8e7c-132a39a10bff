<template>
  <div class="card-container">
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="card-title-section">
        <div class="title-content">
          <span class="title-text">输入输出</span>
        </div>

        <!-- 额外操作区域 -->
        <div class="card-extra">
          <a-checkbox v-model:checked="aidaExecPathModalVisible"/>
          <a-button
              type="link"
              @click="onAidaExecPathClick"
              class="exec-path-btn"
          >
            执行链路
          </a-button>
        </div>

        <!-- 展开收起按钮 -->
        <a-button
            type="text"
            size="small"
            @click="toggleExpanded"
            class="expand-toggle-btn"
        >
          {{ isExpanded ? '收起' : '展开' }}
          <span class="arrow-icon" :class="{ 'expanded': isExpanded }">▼</span>
        </a-button>
      </div>
    </div>
    <!-- 卡片内容 -->
    <div class="card-content" v-if="inputOutputInfo?.length && isExpanded">
      <div class="content-list">
        <div class="content-item" v-for="(item, index) in inputOutputInfo" :key="index">
          <div class="item-label">{{ item.displayName }}:</div>
          <div class="item-value">
            <div class="value-text">{{ item.displayContent }}</div>
            <!-- 用户输入标签 -->
            <a-tag v-if="item.displayName === '输入'" color="blue" class="user-input-tag">
              用户输入
            </a-tag>
            <text-show-modal
                v-if="item.displayContent"
                class="text-show-modal-style"
                :value="item.displayContent"
                :title="item.displayName"
                :read-only="true"
                placeholder=""
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!inputOutputInfo?.length" class="empty-state">
      <a-empty description="暂无输入输出信息"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, ref} from 'vue';
import TextShowModal from '@/components/TextShowModal.vue';
import {PlatformType} from "@/constants/platform";

// 定义 props
const props = defineProps<{
  curMessageLlmInfo?: any;
  sessionBaseInfo?: any;
  aidaExecPathModalVisible: boolean;
}>();

// 定义 emits
const emit = defineEmits<{
  'update:aidaExecPathModalVisible': [value: boolean];
  'aidaExecPathClick': [];
}>();

// 展开状态
const isExpanded = ref(false);

// 计算输入输出信息
const inputOutputInfo = computed(() => {
  if (!props.curMessageLlmInfo) return [];
  return props.curMessageLlmInfo?.detail?.basicInfo?.filter(
      (item: any) => item.displayName === '输入' || item.displayName === '输出'
  );
});

// 事件处理函数
const onAidaExecPathClick = () => {
  emit('aidaExecPathClick');
};

// 切换展开状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};

// 双向绑定处理
const aidaExecPathModalVisible = computed({
  get: () => props.aidaExecPathModalVisible,
  set: (value: boolean) => emit('update:aidaExecPathModalVisible', value)
});
</script>

<style scoped lang="scss">
.card-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;

  .card-title-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;

    .title-content {
      display: flex;
      align-items: center;
      gap: 8px;

      .title-icon {
        font-size: 16px;
      }

      .title-text {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }

    .expand-toggle-btn {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 4px 12px;
      border: none;
      background: transparent;
      color: #1890ff;
      font-size: 12px;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(24, 144, 255, 0.1);
      }

      .arrow-icon {
        font-size: 10px;
        transition: transform 0.2s ease;

        &.expanded {
          transform: rotate(180deg);
        }
      }
    }
  }

  .card-extra {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 16px;

    .exec-path-btn {
      padding: 0 !important;
      height: auto !important;
      font-size: 14px !important;
    }
  }
}

.card-content {
  padding: 16px 20px;

  .content-list {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .content-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;

      .item-label {
        flex: 0 0 auto;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.65);
        min-width: 60px;
        line-height: 1.5;
      }

      .item-value {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 8px;

        .value-text {
          flex: 1;
          font-size: 14px;
          color: #262626;
          line-height: 1.5;
          word-break: break-all;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }

        .user-input-tag {
          font-size: 12px;
          margin-left: 8px;
          border-radius: 4px;
        }

        .text-show-modal-style {
          color: #1890ff;
          font-weight: 500;
          display: none;
          cursor: pointer;

          &:hover {
            color: #40a9ff;
          }
        }

        &:hover {
          .text-show-modal-style {
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
}

.empty-state {
  padding: 40px 20px;

  :deep(.ant-empty) {
    .ant-empty-description {
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}
</style>
